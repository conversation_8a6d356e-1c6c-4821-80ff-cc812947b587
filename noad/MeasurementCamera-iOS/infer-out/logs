[30816][      debug] Loading models took 26.955ms
[30820][      debug] Sqlite write daemon: starting up
[30820][      debug] Sqlite write daemon: set up complete, waiting for connections
[30816][environment] CWD = /Users/<USER>/Desktop/项目/测测相机/noad/MeasurementCamera-iOS
[30816][environment] No .inferconfig file found
[30816][environment] Project root = /Users/<USER>/Desktop/项目/测测相机/noad/MeasurementCamera-iOS
[30816][environment] INFER_ARGS =   @/var/folders/mj/s0wdns3j0c53tk2qmxnnlwj40000gq/T/args.tmp.d0d7e9
[30816][environment] command line arguments:   infer run -- xcodebuild -workspace
[30816][environment]                           TimeMachine.xcworkspace -scheme TimeMachine
[30816][environment]                           -configuration Debug -sdk iphonesimulator
[30816][environment]                           --keep-going
[30816][environment] Could not retrieve available memory (possibly not on Linux)
[30816][environment] Active checkers: self-in-block (C/C++/ObjC), starvation (C/C++/ObjC, Java), uninit (C/C++/ObjC), siof (C/C++/ObjC), racerd (C/C++/ObjC, Java), liveness (C/C++/ObjC), inefficient-keyset-iterator (Java), fragment-retains-view (Java), biabduction (C/C++/ObjC, Java)
[30816][environment] Scheduler: file
[30816][environment] Cores used: 4
[30816][environment] Infer version v1.0.0
[30816][environment] Copyright 2009 - present Facebook. All Rights Reserved.
[30816][environment] 
[30816][   progress] Capturing in xcodebuild mode...
[30816][environment] Xcode version: Xcode 15.0
Build version 15A240d

[30816][environment] clang version: Apple clang version 15.0.0 (clang-1500.0.40.1)
Target: x86_64-apple-darwin23.2.0
Thread model: posix
InstalledDir: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin

[30816][   progress] XCODEBUILD: 2024-06-07 17:43:51.362 xcodebuild[30833:549507] Writing error result bundle to /var/folders/mj/s0wdns3j0c53tk2qmxnnlwj40000gq/T/ResultBundle_2024-07-06_17-43-0051.xcresult
[30816][   progress] XCODEBUILD: 2024-06-07 17:43:51.420 xcodebuild[30833:549507] Requested but did not find extension point with identifier Xcode.IDEFoundation.IDEResultKitSerializationConverter
[30816][   progress] XCODEBUILD: xcodebuild: error: invalid option '--keep-going'
[30816][   progress] XCODEBUILD: 
[30816][   progress] XCODEBUILD: Usage: xcodebuild [-project <projectname>] [[-target <targetname>]...|-alltargets] [-configuration <configurationname>] [-arch <architecture>]... [-sdk [<sdkname>|<sdkpath>]] [-showBuildSettings [-json]] [<buildsetting>=<value>]... [<buildaction>]...
[30816][   progress] XCODEBUILD:        xcodebuild [-project <projectname>] -scheme <schemeName> [-destination <destinationspecifier>]... [-configuration <configurationname>] [-arch <architecture>]... [-sdk [<sdkname>|<sdkpath>]] [-showBuildSettings [-json]] [-showdestinations] [<buildsetting>=<value>]... [<buildaction>]...
[30816][   progress] XCODEBUILD:        xcodebuild -workspace <workspacename> -scheme <schemeName> [-destination <destinationspecifier>]... [-configuration <configurationname>] [-arch <architecture>]... [-sdk [<sdkname>|<sdkpath>]] [-showBuildSettings] [-showdestinations] [<buildsetting>=<value>]... [<buildaction>]...
[30816][   progress] XCODEBUILD:        xcodebuild -version [-sdk [<sdkfullpath>|<sdkname>] [-json] [<infoitem>] ]
[30816][   progress] XCODEBUILD:        xcodebuild -list [[-project <projectname>]|[-workspace <workspacename>]] [-json]
[30816][   progress] XCODEBUILD:        xcodebuild -showsdks [-json]
[30816][   progress] XCODEBUILD:        xcodebuild -exportArchive -archivePath <xcarchivepath> [-exportPath <destinationpath>] -exportOptionsPlist <plistpath>
[30816][   progress] XCODEBUILD:        xcodebuild -exportNotarizedApp -archivePath <xcarchivepath> -exportPath <destinationpath>
[30816][   progress] XCODEBUILD:        xcodebuild -exportLocalizations -localizationPath <path> -project <projectname> [-defaultLanguage <language>] [-exportLanguage <targetlanguage>... [-includeScreenshots]]
[30816][   progress] XCODEBUILD:        xcodebuild -importLocalizations -localizationPath <path> -project <projectname> [-mergeImport]
[30816][   progress] XCODEBUILD:        xcodebuild -resolvePackageDependencies [-project <projectname>|-workspace <workspacename>] -clonedSourcePackagesDirPath <path>
[30816][   progress] XCODEBUILD:        xcodebuild -create-xcframework [-help] [-framework <path>] [-library <path> [-headers <path>]] -output <path>
[30816][   progress] XCODEBUILD: 
[30816][   progress] XCODEBUILD: Options:
[30816][   progress] XCODEBUILD:     -usage                                                   print brief usage
[30816][   progress] XCODEBUILD:     -help                                                    print complete usage
[30816][   progress] XCODEBUILD:     -verbose                                                 provide additional status output
[30816][   progress] XCODEBUILD:     -license                                                 show the Xcode and SDK license agreements
[30816][   progress] XCODEBUILD:     -checkFirstLaunchStatus                                  Check if any First Launch tasks need to be performed
[30816][   progress] XCODEBUILD:     -runFirstLaunch                                          install packages and agree to the license
[30816][   progress] XCODEBUILD:     -downloadAllPlatforms                                    download all platforms
[30816][   progress] XCODEBUILD:     -downloadPlatform NAME                                   download the platform NAME
[30816][   progress] XCODEBUILD:     -downloadAllPreviouslySelectedPlatforms                  download all previously selected platforms
[30816][   progress] XCODEBUILD:     -project NAME                                            build the project NAME
[30816][   progress] XCODEBUILD:     -target NAME                                             build the target NAME
[30816][   progress] XCODEBUILD:     -alltargets                                              build all targets
[30816][   progress] XCODEBUILD:     -workspace NAME                                          build the workspace NAME
[30816][   progress] XCODEBUILD:     -scheme NAME                                             build the scheme NAME
[30816][   progress] XCODEBUILD:     -configuration NAME                                      use the build configuration NAME for building each target
[30816][   progress] XCODEBUILD:     -xcconfig PATH                                           apply the build settings defined in the file at PATH as overrides
[30816][   progress] XCODEBUILD:     -arch ARCH                                               build each target for the architecture ARCH; this will override architectures defined in the project
[30816][   progress] XCODEBUILD:     -sdk SDK                                                 use SDK as the name or path of the base SDK when building the project
[30816][   progress] XCODEBUILD:     -toolchain NAME                                          use the toolchain with identifier or name NAME
[30816][   progress] XCODEBUILD:     -destination DESTINATIONSPECIFIER                        use the destination described by DESTINATIONSPECIFIER (a comma-separated set of key=value pairs describing the destination to use)
[30816][   progress] XCODEBUILD:     -destination-timeout TIMEOUT                             wait for TIMEOUT seconds while searching for the destination device
[30816][   progress] XCODEBUILD:     -parallelizeTargets                                      build independent targets in parallel
[30816][   progress] XCODEBUILD:     -jobs NUMBER                                             specify the maximum number of concurrent build operations
[30816][   progress] XCODEBUILD:     -maximum-concurrent-test-device-destinations NUMBER      the maximum number of device destinations to test on concurrently
[30816][   progress] XCODEBUILD:     -maximum-concurrent-test-simulator-destinations NUMBER   the maximum number of simulator destinations to test on concurrently
[30816][   progress] XCODEBUILD:     -parallel-testing-enabled YES|NO                         overrides the per-target setting in the scheme
[30816][   progress] XCODEBUILD:     -parallel-testing-worker-count NUMBER                    the exact number of test runners that will be spawned during parallel testing
[30816][   progress] XCODEBUILD:     -maximum-parallel-testing-workers NUMBER                 the maximum number of test runners that will be spawned during parallel testing
[30816][   progress] XCODEBUILD:     -dry-run                                                 do everything except actually running the commands
[30816][   progress] XCODEBUILD:     -quiet                                                   do not print any output except for warnings and errors
[30816][   progress] XCODEBUILD:     -hideShellScriptEnvironment                              don't show shell script environment variables in build log
[30816][   progress] XCODEBUILD:     -showsdks                                                display a compact list of the installed SDKs
[30816][   progress] XCODEBUILD:     -showdestinations                                        display a list of destinations
[30816][   progress] XCODEBUILD:     -showTestPlans                                           display a list of test plans
[30816][   progress] XCODEBUILD:     -showBuildSettings                                       display a list of build settings and values
[30816][   progress] XCODEBUILD:     -showBuildSettingsForIndex                               display build settings for the index service
[30816][   progress] XCODEBUILD:     -list                                                    lists the targets and configurations in a project, or the schemes in a workspace
[30816][   progress] XCODEBUILD:     -find-executable NAME                                    display the full path to executable NAME in the provided SDK and toolchain
[30816][   progress] XCODEBUILD:     -find-library NAME                                       display the full path to library NAME in the provided SDK and toolchain
[30816][   progress] XCODEBUILD:     -version                                                 display the version of Xcode; with -sdk will display info about one or all installed SDKs
[30816][   progress] XCODEBUILD:     -enableAddressSanitizer YES|NO                           turn the address sanitizer on or off
[30816][   progress] XCODEBUILD:     -enableThreadSanitizer YES|NO                            turn the thread sanitizer on or off
[30816][   progress] XCODEBUILD:     -enableUndefinedBehaviorSanitizer YES|NO                 turn the undefined behavior sanitizer on or off
[30816][   progress] XCODEBUILD:     -resultBundlePath PATH                                   specifies the directory where a result bundle describing what occurred will be placed
[30816][   progress] XCODEBUILD:     -resultStreamPath PATH                                   specifies the file where a result stream will be written to (the file must already exist)
[30816][   progress] XCODEBUILD:     -resultBundleVersion 3 [default]                         specifies which result bundle version should be used
[30816][   progress] XCODEBUILD:     -clonedSourcePackagesDirPath PATH                        specifies the directory to which remote source packages are fetch or expected to be found
[30816][   progress] XCODEBUILD:     -derivedDataPath PATH                                    specifies the directory where build products and other derived data will go
[30816][   progress] XCODEBUILD:     -archivePath PATH                                        specifies the directory where any created archives will be placed, or the archive that should be exported
[30816][   progress] XCODEBUILD:     -exportArchive                                           specifies that an archive should be exported
[30816][   progress] XCODEBUILD:     -exportNotarizedApp                                      export an archive that has been notarized by Apple
[30816][   progress] XCODEBUILD:     -exportOptionsPlist PATH                                 specifies a path to a plist file that configures archive exporting
[30816][   progress] XCODEBUILD:     -enableCodeCoverage YES|NO                               turn code coverage on or off when testing
[30816][   progress] XCODEBUILD:     -exportPath PATH                                         specifies the destination for the product exported from an archive
[30816][   progress] XCODEBUILD:     -skipUnavailableActions                                  specifies that scheme actions that cannot be performed should be skipped instead of causing a failure
[30816][   progress] XCODEBUILD:     -exportLocalizations                                     exports completed and outstanding project localizations
[30816][   progress] XCODEBUILD:     -importLocalizations                                     imports localizations for a project, assuming any necessary localized resources have been created in Xcode
[30816][   progress] XCODEBUILD:     -localizationPath                                        specifies a path to XLIFF localization files
[30816][   progress] XCODEBUILD:     -exportLanguage                                          specifies multiple optional ISO 639-1 languages included in a localization export
[30816][   progress] XCODEBUILD:     -defaultLanguage                                         specifies the default ISO 639-1 language to be used as the source language on export
[30816][   progress] XCODEBUILD:     -xctestrun                                               specifies a path to a test run specification
[30816][   progress] XCODEBUILD:     -testProductsPath                                        specifies a path for the test products
[30816][   progress] XCODEBUILD:     -enablePerformanceTestsDiagnostics YES|NO                enables performance trace and memgraph collection for performance XCTests
[30816][   progress] XCODEBUILD:     -testPlan                                                specifies the name of the test plan associated with the scheme to use for testing
[30816][   progress] XCODEBUILD:     -only-testing                                            constrains testing by specifying tests to include, and excluding other tests
[30816][   progress] XCODEBUILD:     -only-testing:TEST-IDENTIFIER                            constrains testing by specifying tests to include, and excluding other tests
[30816][   progress] XCODEBUILD:     -skip-testing                                            constrains testing by specifying tests to exclude, but including other tests
[30816][   progress] XCODEBUILD:     -skip-testing:TEST-IDENTIFIER                            constrains testing by specifying tests to exclude, but including other tests
[30816][   progress] XCODEBUILD:     -test-timeouts-enabled YES|NO                            enable or disable test timeout behavior
[30816][   progress] XCODEBUILD:     -default-test-execution-time-allowance SECONDS           the default execution time an individual test is given to execute, if test timeouts are enabled
[30816][   progress] XCODEBUILD:     -maximum-test-execution-time-allowance SECONDS           the maximum execution time an individual test is given to execute, regardless of the test's preferred allowance
[30816][   progress] XCODEBUILD:     -test-iterations <number>                                If specified, tests will run <number> times. May be used in conjunction with either -retry-tests-on-failure or -run-tests-until-failure, in which case this will become the maximum number of iterations.
[30816][   progress] XCODEBUILD:     -retry-tests-on-failure                                  If specified, tests will retry on failure. May be used in conjunction with -test-iterations <number>, in which case <number> will be the maximum number of iterations. Otherwise, a maximum of 3 is assumed. May not be used with -run-tests-until-failure.
[30816][   progress] XCODEBUILD:     -run-tests-until-failure                                 If specified, tests will run until they fail. May be used in conjunction with -test-iterations <number>, in which case <number> will be the maximum number of iterations. Otherwise, a maximum of 100 is assumed. May not be used with -retry-tests-on-failure.
[30816][   progress] XCODEBUILD:     -test-repetition-relaunch-enabled YES|NO                 Enable or disable, tests repeating in a new process for each repetition.  Must be used in conjunction with -test-iterations, -retry-tests-on-failure, or -run-tests-until-failure. If not specified, tests will repeat in the same process.
[30816][   progress] XCODEBUILD:     -only-test-configuration                                 constrains testing by specifying test configurations to include, and excluding other test configurations
[30816][   progress] XCODEBUILD:     -skip-test-configuration                                 constrains testing by specifying test configurations to exclude, but including other test configurations
[30816][   progress] XCODEBUILD:     -collect-test-diagnostics on-failure|never               Whether or not testing collects verbose diagnostics (like a sysdiagnose) when encountering a failure
[30816][   progress] XCODEBUILD:     -testLanguage                                            constrains testing by specifying ISO 639-1 language in which to run the tests
[30816][   progress] XCODEBUILD:     -testRegion                                              constrains testing by specifying ISO 3166-1 region in which to run the tests
[30816][   progress] XCODEBUILD:     -enumerate-tests                                         Enumerate the tests that would be executed by this command without actually executing them
[30816][   progress] XCODEBUILD:     -test-enumeration-style                                  The style in which to enumerate the tests. Valid styles: flat, hierarchical
[30816][   progress] XCODEBUILD:     -test-enumeration-format                                 The output format of the enumerated tests. Valid formats: text, json
[30816][   progress] XCODEBUILD:     -test-enumeration-output-path                            The path (relative or absolute) where the results of test enumeration should be written. If '-' is supplied, writes to standard out.
[30816][   progress] XCODEBUILD:     -resolvePackageDependencies                              resolves any Swift package dependencies referenced by the project or workspace
[30816][   progress] XCODEBUILD:     -disableAutomaticPackageResolution                       prevents packages from automatically being resolved to versions other than those recorded in the `Package.resolved` file
[30816][   progress] XCODEBUILD:     -onlyUsePackageVersionsFromResolvedFile                  prevents packages from automatically being resolved to versions other than those recorded in the `Package.resolved` file
[30816][   progress] XCODEBUILD:     -skipPackageUpdates                                      Skip updating package dependencies from their remote
[30816][   progress] XCODEBUILD:     -disablePackageRepositoryCache                           disable use of a local cache of remote package repositories
[30816][   progress] XCODEBUILD:     -skipPackagePluginValidation                             Skip validation of package plugins (this can be a security risk if they are not from trusted sources)
[30816][   progress] XCODEBUILD:     -skipMacroValidation                                     Skip validation of macros (this can be a security risk if they are not from trusted sources)
[30816][   progress] XCODEBUILD:     -packageCachePath                                        path of caches used for package support
[30816][   progress] XCODEBUILD:     -defaultPackageRegistryURL                               URL of the default package registry
[30816][   progress] XCODEBUILD:     -packageDependencySCMToRegistryTransformation            specifies the transformation to apply to SCM-based package dependencies (none, useRegistryIdentity, or useRegistryIdentityAndSources)
[30816][   progress] XCODEBUILD:     -skipPackageSignatureValidation                          Skip validation of package signatures (this can be a security risk if they are not from trusted sources)
[30816][   progress] XCODEBUILD:     -packageFingerprintPolicy                                Package fingerprint checking policy (`warn` or `strict`)
[30816][   progress] XCODEBUILD:     -packageSigningEntityPolicy                              Package signing entity checking policy (`warn` or `strict`)
[30816][   progress] XCODEBUILD:     -json                                                    output as JSON (note: -json implies -quiet)
[30816][   progress] XCODEBUILD:     -allowProvisioningUpdates                                Allow xcodebuild to communicate with the Apple Developer website. For automatically signed targets, xcodebuild will create and update profiles, app IDs, and certificates. For manually signed targets, xcodebuild will download missing or updated provisioning profiles. Requires a developer account to have been added in Xcode's Accounts settings or an App Store Connect authentication key to be specified via the -authenticationKeyPath, -authenticationKeyID, and -authenticationKeyIssuerID parameters.
[30816][   progress] XCODEBUILD:     -allowProvisioningDeviceRegistration                     Allow xcodebuild to register your destination device on the developer portal if necessary. This flag only takes effect if -allowProvisioningUpdates is also passed.
[30816][   progress] XCODEBUILD:     -authenticationKeyPath                                   specifies the path to an authentication key issued by App Store Connect. If specified, xcodebuild will authenticate with the Apple Developer website using this credential. The -authenticationKeyID and -authenticationKeyIssuerID parameters are required.
[30816][   progress] XCODEBUILD:     -authenticationKeyID                                     specifies the key identifier associated with the App Store Conect authentication key at -authenticationKeyPath. This string can be located in the users and access details for your provider at "https://appstoreconnect.apple.com".
[30816][   progress] XCODEBUILD:     -authenticationKeyIssuerID                               specifies the App Store Connect issuer identifier associated with the authentication key at -authenticationKeyPath. This string can be located in the users and access details for your provider at "https://appstoreconnect.apple.com".
[30816][   progress] XCODEBUILD:     -scmProvider                                             which implementation to use for Git operations (system/xcode)
[30816][   progress] XCODEBUILD:     -showBuildTimingSummary                                  display a report of the timings of all the commands invoked during the build
[30816][   progress] XCODEBUILD:     -create-xcframework                                      create an xcframework from prebuilt libraries; -help for more information.
[30816][ extern err] *** capture failed to execute: exited with code 64
[30816][ extern err] Raised at Stdlib.input_line.scan in file "stdlib.ml", line 449, characters 14-31
Called from Stdio__In_channel.input_line_exn in file "src/in_channel.ml" (inlined), line 68, characters 13-30
Called from IBase__Utils.with_channel_in in file "src/base/Utils.ml", line 252, characters 11-44

[30820][      debug] Sqlite write daemon: terminating
