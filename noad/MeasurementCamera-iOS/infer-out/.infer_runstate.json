{"run_sequence": [{"date": "2024-06-07 17:43:50.449842+08:00", "command": "run", "infer_version": {"major": 1, "minor": 0, "patch": 0, "commit": "79c6cd3"}}], "results_dir_format": "db_filename: infer-out/results.db\ndb_schema: \n      CREATE TABLE IF NOT EXISTS procedures\n        ( proc_uid TEXT PRIMARY KEY NOT NULL\n        , proc_name BLOB NOT NULL\n        , attr_kind INTEGER NOT NULL\n        , source_file TEXT NOT NULL\n        , proc_attributes BLOB NOT NULL\n        , cfg BLOB\n        , callees BLOB NOT NULL\n        )\n    ;\n\n      CREATE TABLE IF NOT EXISTS source_files\n        ( source_file TEXT PRIMARY KEY\n        , type_environment BLOB NOT NULL\n        , integer_type_widths BLOB\n        , procedure_names BLOB NOT NULL\n        , freshly_captured INT NOT NULL )\n    ;\n\n      CREATE TABLE IF NOT EXISTS specs\n        ( proc_uid TEXT PRIMARY KEY NOT NULL\n        , proc_name BLOB NOT NULL\n        , analysis_summary BLOB NOT NULL\n        , report_summary BLOB NOT NULL\n        )\n    ;\n\n      CREATE TABLE IF NOT EXISTS model_specs\n        ( proc_uid TEXT PRIMARY KEY NOT NULL\n        , proc_name BLOB NOT NULL\n        , analysis_summary BLOB NOT NULL\n        , report_summary BLOB NOT NULL\n        )\n    ", "should_merge_capture": false}