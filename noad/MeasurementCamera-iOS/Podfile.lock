PODS:
  - Ads-CN (6.9.1.8):
    - Ads-CN/BUAdLive-Framework (= 6.9.1.8)
    - Ads-CN/BUAdSDK (= 6.9.1.8)
  - Ads-CN/BUAdLive-Framework (6.9.1.8):
    - BUTTSDKFramework/LivePull-Lite (= ********-premium)
  - Ads-CN/BUAdSDK (6.9.1.8)
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - AliyunOSSiOS (2.11.1)
  - BlocksKit (2.2.6):
    - BlocksKit/All (= 2.2.6)
  - BlocksKit/All (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
    - BlocksKit/MessageUI
    - BlocksKit/QuickLook
    - BlocksKit/UIKit
  - BlocksKit/Core (2.2.6)
  - BlocksKit/DynamicDelegate (2.2.6)
  - BlocksKit/MessageUI (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BlocksKit/QuickLook (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BlocksKit/UIKit (2.2.6):
    - BlocksKit/Core
    - BlocksKit/DynamicDelegate
  - BRPickerView (2.9.7):
    - BRPickerView/All (= 2.9.7)
  - BRPickerView/All (2.9.7):
    - BRPickerView/Default
    - BRPickerView/Deprecated
  - BRPickerView/Core (2.9.7)
  - BRPickerView/DatePicker (2.9.7):
    - BRPickerView/Core
  - BRPickerView/Default (2.9.7):
    - BRPickerView/DatePicker
    - BRPickerView/TextPicker
  - BRPickerView/Deprecated (2.9.7):
    - BRPickerView/Core
    - BRPickerView/Deprecated/AddressPickerView (= 2.9.7)
    - BRPickerView/Deprecated/Base (= 2.9.7)
    - BRPickerView/Deprecated/StringPickerView (= 2.9.7)
  - BRPickerView/Deprecated/AddressPickerView (2.9.7):
    - BRPickerView/Core
    - BRPickerView/Deprecated/Base
  - BRPickerView/Deprecated/Base (2.9.7):
    - BRPickerView/Core
  - BRPickerView/Deprecated/StringPickerView (2.9.7):
    - BRPickerView/Core
    - BRPickerView/Deprecated/Base
  - BRPickerView/TextPicker (2.9.7):
    - BRPickerView/Core
  - BUTTSDKFramework/Core (********-premium)
  - BUTTSDKFramework/LiveBase (********-premium):
    - BUTTSDKFramework/Core
    - BUTTSDKFramework/Reachability
  - BUTTSDKFramework/LivePlayer/Base (********-premium):
    - BUTTSDKFramework/Core
    - BUTTSDKFramework/LiveBase
    - BUTTSDKFramework/Reachability
  - BUTTSDKFramework/LivePlayer/Lite (********-premium):
    - BUTTSDKFramework/LivePlayer/Base
    - BUTTSDKFramework/PlayerCore/LiveLite
  - BUTTSDKFramework/LivePull-Lite (********-premium):
    - BUTTSDKFramework/LivePlayer/Lite
    - BUTTSDKFramework/Strategy/Lite
  - BUTTSDKFramework/PlayerCore/Base (********-premium):
    - BUTTSDKFramework/Core
    - BUTTSDKFramework/Tools
  - BUTTSDKFramework/PlayerCore/LiveLite (********-premium):
    - BUTTSDKFramework/PlayerCore/Base
    - BUTTSDKFramework/TTFFmpeg/LiveLite
  - BUTTSDKFramework/Reachability (********-premium)
  - BUTTSDKFramework/SSL (********-premium)
  - BUTTSDKFramework/Strategy/Core (********-premium):
    - BUTTSDKFramework/LiveBase
    - BUTTSDKFramework/Reachability
  - BUTTSDKFramework/Strategy/Lite (********-premium):
    - BUTTSDKFramework/LivePlayer/Lite
    - BUTTSDKFramework/Strategy/Core
  - BUTTSDKFramework/Tools (********-premium):
    - BUTTSDKFramework/SSL
  - BUTTSDKFramework/TTFFmpeg/Base (********-premium):
    - BUTTSDKFramework/Tools
  - BUTTSDKFramework/TTFFmpeg/LiveLite (********-premium):
    - BUTTSDKFramework/TTFFmpeg/Base
  - Masonry (1.1.0)
  - OpenCV2 (4.3.0)
  - Toast (4.0.0)
  - UMAPM (2.0.4):
    - UMCommon
  - UMCommon (7.5.3):
    - UMDevice
  - UMDevice (3.4.0)
  - WMPageController (2.5.2)

DEPENDENCIES:
  - Ads-CN
  - AFNetworking
  - AliyunOSSiOS
  - BlocksKit (from `https://github.com/Tioks/BlocksKit`)
  - BRPickerView
  - Masonry
  - OpenCV2
  - Toast
  - UMAPM
  - UMCommon
  - UMDevice
  - WMPageController

SPEC REPOS:
  trunk:
    - Ads-CN
    - AFNetworking
    - AliyunOSSiOS
    - BRPickerView
    - BUTTSDKFramework
    - Masonry
    - OpenCV2
    - Toast
    - UMAPM
    - UMCommon
    - UMDevice
    - WMPageController

EXTERNAL SOURCES:
  BlocksKit:
    :git: https://github.com/Tioks/BlocksKit

CHECKOUT OPTIONS:
  BlocksKit:
    :commit: 1700172dcd52949c3d5aa25caa4c9bffb10da29a
    :git: https://github.com/Tioks/BlocksKit

SPEC CHECKSUMS:
  Ads-CN: e5dedb93de1804965b0687ee727bc14dd34ac182
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AliyunOSSiOS: 51e8aa24dda25918e3b5b80d72105a57485b6bf7
  BlocksKit: 5fae06c13638d9fdb0bbddca3dabfe8f5aa735c5
  BRPickerView: 6548c8fbe21c0f29e72ab2fae177d196edc83f09
  BUTTSDKFramework: b5d45b40003335843f05088c2373cef0b5638a9e
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  OpenCV2: ffe82e5cb5d16dc3fc01a5f4127b9a5b3cfc7d1b
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  UMAPM: f038b65a3df4fe8e2df3245642d84cff7f5f63c8
  UMCommon: 3b850836e8bc162b4e7f6b527d30071ed8ea75a1
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  WMPageController: 0c7624d2b48e4f1ab92371688cf6744ed701575d

PODFILE CHECKSUM: f8f81399071ccfe1d15b2b694c8770ca83ec85dd

COCOAPODS: 1.16.2
