// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00ADBDD91314E13FE9E99739 /* Pods_TimeMachineTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = FB734F328401C6FCF513BFB2 /* Pods_TimeMachineTests.framework */; };
		0D37E6A874457D820B2BC701 /* Pods_TimeMachine_TimeMachineUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B82C7FF7DD7EE20455599C0D /* Pods_TimeMachine_TimeMachineUITests.framework */; };
		A4179DE426AD354300FBB442 /* PalmsDatabaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A4179DE326AD354300FBB442 /* PalmsDatabaseManager.m */; };
		A4179DE726AD355D00FBB442 /* PsychologicalDatabaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A4179DE626AD355D00FBB442 /* PsychologicalDatabaseManager.m */; };
		A418CF7B26A51C2F00AA67F8 /* AWPolygonView.m in Sources */ = {isa = PBXBuildFile; fileRef = A418CF7A26A51C2F00AA67F8 /* AWPolygonView.m */; };
		A418CF8B26A5622900AA67F8 /* StarsView.m in Sources */ = {isa = PBXBuildFile; fileRef = A418CF8526A5622900AA67F8 /* StarsView.m */; };
		A418CF8C26A5622900AA67F8 /* StarView.m in Sources */ = {isa = PBXBuildFile; fileRef = A418CF8626A5622900AA67F8 /* StarView.m */; };
		A418CF8D26A5622900AA67F8 /* Star_highlight.png in Resources */ = {isa = PBXBuildFile; fileRef = A418CF8826A5622900AA67F8 /* Star_highlight.png */; };
		A418CF8E26A5622900AA67F8 /* Star.png in Resources */ = {isa = PBXBuildFile; fileRef = A418CF8926A5622900AA67F8 /* Star.png */; };
		A418CF9126A58EEA00AA67F8 /* PalmsImagePickerVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A418CF9026A58EEA00AA67F8 /* PalmsImagePickerVC.m */; };
		A429D1C826AA5AD700E145FD /* PalmsResultCircleMaskView.m in Sources */ = {isa = PBXBuildFile; fileRef = A429D1C726AA5AD700E145FD /* PalmsResultCircleMaskView.m */; };
		A4686DD926A11FA9005F7A87 /* PalmsViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A4686DD826A11FA9005F7A87 /* PalmsViewController.m */; };
		A4686DDC26A12C77005F7A87 /* PalmsUnscrambleVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A4686DDB26A12C77005F7A87 /* PalmsUnscrambleVC.m */; };
		A4686DDF26A163BB005F7A87 /* PalmsResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A4686DDE26A163BB005F7A87 /* PalmsResultVC.m */; };
		A49042D226C0E1C60083A3D7 /* template1.txt in Resources */ = {isa = PBXBuildFile; fileRef = A49042D126C0E1C60083A3D7 /* template1.txt */; };
		A49042D726C0E2460083A3D7 /* template3.txt in Resources */ = {isa = PBXBuildFile; fileRef = A49042D526C0E2430083A3D7 /* template3.txt */; };
		A49042D826C0E2460083A3D7 /* template2.txt in Resources */ = {isa = PBXBuildFile; fileRef = A49042D626C0E2460083A3D7 /* template2.txt */; };
		A49CD67C26C9F62400F19C02 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = A49CD67E26C9F62400F19C02 /* Localizable.strings */; };
		A49CD68426C9F6DE00F19C02 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = A49CD68626C9F6DE00F19C02 /* InfoPlist.strings */; };
		A50B9D672C194E21002C4109 /* 手相_en.json in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D662C194E1B002C4109 /* 手相_en.json */; };
		A50B9D762C194F34002C4109 /* UpgradeVIPViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D692C194F34002C4109 /* UpgradeVIPViewController.m */; };
		A50B9D772C194F34002C4109 /* UpgradeVIPPayButton.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D6A2C194F34002C4109 /* UpgradeVIPPayButton.m */; };
		A50B9D782C194F34002C4109 /* APPStoreIAPObserver.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D6C2C194F34002C4109 /* APPStoreIAPObserver.m */; };
		A50B9D792C194F34002C4109 /* NSString+Base64.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D6D2C194F34002C4109 /* NSString+Base64.m */; };
		A50B9D7A2C194F34002C4109 /* APPMakeStoreIAPManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D722C194F34002C4109 /* APPMakeStoreIAPManager.m */; };
		A50B9D7B2C194F34002C4109 /* NSDictionary+Value.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D732C194F34002C4109 /* NSDictionary+Value.m */; };
		A50B9D9D2C1991F8002C4109 /* BSLaunchScreenVC.xib in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D7D2C1991F8002C4109 /* BSLaunchScreenVC.xib */; };
		A50B9D9E2C1991F8002C4109 /* EnterViewBackground_en.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D7E2C1991F8002C4109 /* EnterViewBackground_en.png */; };
		A50B9D9F2C1991F8002C4109 /* UIView+BSAnimation.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D7F2C1991F8002C4109 /* UIView+BSAnimation.m */; };
		A50B9DA02C1991F8002C4109 /* BSEnterBaseController.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D802C1991F8002C4109 /* BSEnterBaseController.m */; };
		A50B9DA12C1991F8002C4109 /* BSEnterBaseController.xib in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D812C1991F8002C4109 /* BSEnterBaseController.xib */; };
		A50B9DA22C1991F8002C4109 /* BSLaunchScreenVC.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D832C1991F8002C4109 /* BSLaunchScreenVC.m */; };
		A50B9DA32C1991F8002C4109 /* IntroduceBackground04.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D842C1991F8002C4109 /* IntroduceBackground04.png */; };
		A50B9DA52C1991F8002C4109 /* IntroduceBackground02.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D862C1991F8002C4109 /* IntroduceBackground02.png */; };
		A50B9DA62C1991F8002C4109 /* IntroduceBackground03.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D872C1991F8002C4109 /* IntroduceBackground03.png */; };
		A50B9DA72C1991F8002C4109 /* IntroduceBackground01.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D882C1991F8002C4109 /* IntroduceBackground01.png */; };
		A50B9DA82C1991F8002C4109 /* ProgressViewUserInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D8A2C1991F8002C4109 /* ProgressViewUserInfo.m */; };
		A50B9DA92C1991F8002C4109 /* BSWaitingAnimationView.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D8B2C1991F8002C4109 /* BSWaitingAnimationView.m */; };
		A50B9DAA2C1991F8002C4109 /* BSEtDetailsView.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D8D2C1991F8002C4109 /* BSEtDetailsView.m */; };
		A50B9DAB2C1991F8002C4109 /* BSSubscribeRadio.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D8E2C1991F8002C4109 /* BSSubscribeRadio.m */; };
		A50B9DAC2C1991F8002C4109 /* BSAgreementView.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D8F2C1991F8002C4109 /* BSAgreementView.m */; };
		A50B9DAD2C1991F8002C4109 /* BSEtWebUrlView.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D902C1991F8002C4109 /* BSEtWebUrlView.m */; };
		A50B9DAE2C1991F8002C4109 /* BSIntroduceView.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D952C1991F8002C4109 /* BSIntroduceView.m */; };
		A50B9DAF2C1991F8002C4109 /* EnterViewBackground.png in Resources */ = {isa = PBXBuildFile; fileRef = A50B9D992C1991F8002C4109 /* EnterViewBackground.png */; };
		A50B9DB02C1991F8002C4109 /* BSUserDefaultManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A50B9D9B2C1991F8002C4109 /* BSUserDefaultManager.m */; };
		A50C5E992D75A10900A601FC /* TMYearViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = A50C5E982D75A10900A601FC /* TMYearViewController.m */; };
		A51C5B342D79965600266A15 /* UIButton+CenterImageAndTitle.m in Sources */ = {isa = PBXBuildFile; fileRef = A51C5B332D79965600266A15 /* UIButton+CenterImageAndTitle.m */; };
		A51C5B372D799D8300266A15 /* TMZhanbuController.m in Sources */ = {isa = PBXBuildFile; fileRef = A51C5B362D799D8300266A15 /* TMZhanbuController.m */; };
		A51C5C1B2D79A13D00266A15 /* TiDi.m in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C1A2D79A13D00266A15 /* TiDi.m */; };
		A51C5C3C2D79A29500266A15 /* SolarWeek.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C342D79A29500266A15 /* SolarWeek.swift */; };
		A51C5C3D2D79A29500266A15 /* TaoUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C382D79A29500266A15 /* TaoUtil.swift */; };
		A51C5C3E2D79A29500266A15 /* Solar.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2F2D79A29500266A15 /* Solar.swift */; };
		A51C5C3F2D79A29500266A15 /* LunarTime.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C292D79A29500266A15 /* LunarTime.swift */; };
		A51C5C402D79A29500266A15 /* ShouXingUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2D2D79A29500266A15 /* ShouXingUtil.swift */; };
		A51C5C412D79A29500266A15 /* LunarYear.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2B2D79A29500266A15 /* LunarYear.swift */; };
		A51C5C422D79A29500266A15 /* Holiday.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C222D79A29500266A15 /* Holiday.swift */; };
		A51C5C432D79A29500266A15 /* Tao.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C362D79A29500266A15 /* Tao.swift */; };
		A51C5C442D79A29500266A15 /* FotoUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C202D79A29500266A15 /* FotoUtil.swift */; };
		A51C5C452D79A29500266A15 /* SolarMonth.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C312D79A29500266A15 /* SolarMonth.swift */; };
		A51C5C462D79A29500266A15 /* Foto.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C1E2D79A29500266A15 /* Foto.swift */; };
		A51C5C472D79A29500266A15 /* TaoFestival.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C372D79A29500266A15 /* TaoFestival.swift */; };
		A51C5C482D79A29500266A15 /* LunarUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2A2D79A29500266A15 /* LunarUtil.swift */; };
		A51C5C492D79A29500266A15 /* LunarMonth.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C282D79A29500266A15 /* LunarMonth.swift */; };
		A51C5C4A2D79A29500266A15 /* SolarUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C332D79A29500266A15 /* SolarUtil.swift */; };
		A51C5C4B2D79A29500266A15 /* LiuYue.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C262D79A29500266A15 /* LiuYue.swift */; };
		A51C5C4C2D79A29500266A15 /* LiuNian.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C252D79A29500266A15 /* LiuNian.swift */; };
		A51C5C4D2D79A29500266A15 /* ShuJiu.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2E2D79A29500266A15 /* ShuJiu.swift */; };
		A51C5C4E2D79A29500266A15 /* EightChar.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C1D2D79A29500266A15 /* EightChar.swift */; };
		A51C5C4F2D79A29500266A15 /* JieQi.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C242D79A29500266A15 /* JieQi.swift */; };
		A51C5C502D79A29500266A15 /* SolarHalfYear.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C302D79A29500266A15 /* SolarHalfYear.swift */; };
		A51C5C512D79A29500266A15 /* Lunar.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C272D79A29500266A15 /* Lunar.swift */; };
		A51C5C522D79A29500266A15 /* SolarYear.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C352D79A29500266A15 /* SolarYear.swift */; };
		A51C5C532D79A29500266A15 /* Yun.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C3A2D79A29500266A15 /* Yun.swift */; };
		A51C5C542D79A29500266A15 /* HolidayUtil.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C232D79A29500266A15 /* HolidayUtil.swift */; };
		A51C5C552D79A29500266A15 /* NineStar.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C2C2D79A29500266A15 /* NineStar.swift */; };
		A51C5C562D79A29500266A15 /* SolarSeason.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C322D79A29500266A15 /* SolarSeason.swift */; };
		A51C5C572D79A29500266A15 /* FotoFestival.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C1F2D79A29500266A15 /* FotoFestival.swift */; };
		A51C5C582D79A29500266A15 /* DaYun.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C1C2D79A29500266A15 /* DaYun.swift */; };
		A51C5C592D79A29500266A15 /* Fu.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C212D79A29500266A15 /* Fu.swift */; };
		A51C5C5A2D79A29500266A15 /* XiaoYun.swift in Sources */ = {isa = PBXBuildFile; fileRef = A51C5C392D79A29500266A15 /* XiaoYun.swift */; };
		A570F62A2C103F9D00614266 /* 手相.json in Resources */ = {isa = PBXBuildFile; fileRef = A570F6292C103F9D00614266 /* 手相.json */; };
		A5C295482D7C183400B3F394 /* OSSUploadImageTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A5C295472D7C183400B3F394 /* OSSUploadImageTool.m */; };
		A5D3345A2A58FE7500F67392 /* File.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5D334592A58FE7500F67392 /* File.swift */; };
		A5D334772A59110700F67392 /* UILabel+createLabels.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D3345E2A59110700F67392 /* UILabel+createLabels.m */; };
		A5D334782A59110700F67392 /* PopAnimationTool.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334612A59110700F67392 /* PopAnimationTool.m */; };
		A5D334792A59110700F67392 /* PopView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334622A59110700F67392 /* PopView.m */; };
		A5D3347A2A59110700F67392 /* UIButton+Create.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334662A59110700F67392 /* UIButton+Create.m */; };
		A5D3347B2A59110700F67392 /* UIColor+Hex.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334682A59110700F67392 /* UIColor+Hex.m */; };
		A5D3347C2A59110700F67392 /* ZYELabel.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D3346C2A59110700F67392 /* ZYELabel.m */; };
		A5D3347D2A59110700F67392 /* UIImageView+Create.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D3346D2A59110700F67392 /* UIImageView+Create.m */; };
		A5D3347E2A59110700F67392 /* UpdateManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334702A59110700F67392 /* UpdateManager.m */; };
		A5D3347F2A59110700F67392 /* UIView+Extension.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334722A59110700F67392 /* UIView+Extension.m */; };
		A5D334802A59110700F67392 /* ZYEButton.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334742A59110700F67392 /* ZYEButton.m */; };
		A5D334812A59110700F67392 /* ZYEUpdateView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334752A59110700F67392 /* ZYEUpdateView.m */; };
		A5D334852A59117800F67392 /* Reachability.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334832A59117800F67392 /* Reachability.m */; };
		A5D334882A59123E00F67392 /* MyAdManager.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334862A59123E00F67392 /* MyAdManager.m */; };
		A5D3348B2A59132000F67392 /* IPChangeIconView.m in Sources */ = {isa = PBXBuildFile; fileRef = A5D334892A59131F00F67392 /* IPChangeIconView.m */; };
		A5DC045C2CB7693F006E281C /* FacePalmController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DC045B2CB7693F006E281C /* FacePalmController.m */; };
		A5DD99532D6C241900412F3C /* DeepSeekStreamClient.m in Sources */ = {isa = PBXBuildFile; fileRef = A5DD99522D6C241900412F3C /* DeepSeekStreamClient.m */; };
		A5E87C562CBD127B0039716D /* face_en.json in Resources */ = {isa = PBXBuildFile; fileRef = A5E87C552CBD127B0039716D /* face_en.json */; };
		A5E87C582CBD12B40039716D /* face.json in Resources */ = {isa = PBXBuildFile; fileRef = A5E87C572CBD12B40039716D /* face.json */; };
		A5FDEB502CBCF52E00F3D63A /* FaceResultController.m in Sources */ = {isa = PBXBuildFile; fileRef = A5FDEB4F2CBCF52E00F3D63A /* FaceResultController.m */; };
		AB8901B72DBF234700C17211 /* CardView.m in Sources */ = {isa = PBXBuildFile; fileRef = AB8901B62DBF234700C17211 /* CardView.m */; };
		AB8901BA2DBF279C00C17211 /* CardSliderView.m in Sources */ = {isa = PBXBuildFile; fileRef = AB8901B92DBF279C00C17211 /* CardSliderView.m */; };
		AB8901BD2DBF758300C17211 /* AllFuncShowView.m in Sources */ = {isa = PBXBuildFile; fileRef = AB8901BC2DBF758300C17211 /* AllFuncShowView.m */; };
		AB8DB0992DBE05410052A79B /* MainTabbarVC.m in Sources */ = {isa = PBXBuildFile; fileRef = AB8DB0982DBE05410052A79B /* MainTabbarVC.m */; };
		AB8DB09C2DBE0A070052A79B /* MentalTestVC.m in Sources */ = {isa = PBXBuildFile; fileRef = AB8DB09B2DBE0A070052A79B /* MentalTestVC.m */; };
		AB9BE91F2DC0ABDD00D4CE8B /* FaceAnalysisVC.m in Sources */ = {isa = PBXBuildFile; fileRef = AB9BE91E2DC0ABDD00D4CE8B /* FaceAnalysisVC.m */; };
		ABA367A22DCB091D00E23A5C /* CopyLabel.m in Sources */ = {isa = PBXBuildFile; fileRef = ABA367A12DCB091D00E23A5C /* CopyLabel.m */; };
		ABE8B6182DC1BCC0009C13D3 /* FaceAnalysisResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = ABE8B6172DC1BCC0009C13D3 /* FaceAnalysisResultVC.m */; };
		ABE8B61B2DC1BFB5009C13D3 /* FaceAnalysisTabVC.m in Sources */ = {isa = PBXBuildFile; fileRef = ABE8B61A2DC1BFB5009C13D3 /* FaceAnalysisTabVC.m */; };
		F307651C26A7B25E00FF6708 /* ESBFWaitView.m in Sources */ = {isa = PBXBuildFile; fileRef = F307651826A7B25E00FF6708 /* ESBFWaitView.m */; };
		F307651D26A7B25E00FF6708 /* RSA.m in Sources */ = {isa = PBXBuildFile; fileRef = F307651926A7B25E00FF6708 /* RSA.m */; };
		F307652426A7B26E00FF6708 /* UIView+Layout.m in Sources */ = {isa = PBXBuildFile; fileRef = F307652226A7B26E00FF6708 /* UIView+Layout.m */; };
		F307653A26A7B53E00FF6708 /* AFURLResponseSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653026A7B53E00FF6708 /* AFURLResponseSerialization.m */; };
		F307653B26A7B53E00FF6708 /* AFHTTPSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653126A7B53E00FF6708 /* AFHTTPSessionManager.m */; };
		F307653C26A7B53E00FF6708 /* AFURLSessionManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653326A7B53E00FF6708 /* AFURLSessionManager.m */; };
		F307653D26A7B53E00FF6708 /* AFURLRequestSerialization.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653426A7B53E00FF6708 /* AFURLRequestSerialization.m */; };
		F307653E26A7B53E00FF6708 /* AFNetworkReachabilityManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653626A7B53E00FF6708 /* AFNetworkReachabilityManager.m */; };
		F307653F26A7B53E00FF6708 /* AFSecurityPolicy.m in Sources */ = {isa = PBXBuildFile; fileRef = F307653726A7B53E00FF6708 /* AFSecurityPolicy.m */; };
		F30766F826A7B84100FF6708 /* SDImageHEICCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307662D26A7B84100FF6708 /* SDImageHEICCoder.m */; };
		F30766F926A7B84100FF6708 /* NSImage+Compatibility.m in Sources */ = {isa = PBXBuildFile; fileRef = F307662F26A7B84100FF6708 /* NSImage+Compatibility.m */; };
		F30766FA26A7B84100FF6708 /* SDAnimatedImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663126A7B84100FF6708 /* SDAnimatedImageView+WebCache.m */; };
		F30766FB26A7B84100FF6708 /* SDImageCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663326A7B84100FF6708 /* SDImageCoder.m */; };
		F30766FC26A7B84100FF6708 /* SDWebImageOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663626A7B84100FF6708 /* SDWebImageOperation.m */; };
		F30766FD26A7B84100FF6708 /* SDWebImageDownloaderDecryptor.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663926A7B84100FF6708 /* SDWebImageDownloaderDecryptor.m */; };
		F30766FE26A7B84100FF6708 /* SDWebImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663D26A7B84100FF6708 /* SDWebImageManager.m */; };
		F30766FF26A7B84100FF6708 /* SDImageIOAnimatedCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307663F26A7B84100FF6708 /* SDImageIOAnimatedCoder.m */; };
		F307670026A7B84100FF6708 /* UIImageView+HighlightedWebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664126A7B84100FF6708 /* UIImageView+HighlightedWebCache.m */; };
		F307670126A7B84100FF6708 /* SDAnimatedImage.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664626A7B84100FF6708 /* SDAnimatedImage.m */; };
		F307670226A7B84100FF6708 /* SDAnimatedImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664726A7B84100FF6708 /* SDAnimatedImageView.m */; };
		F307670326A7B84100FF6708 /* UIImage+ForceDecode.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664826A7B84100FF6708 /* UIImage+ForceDecode.m */; };
		F307670426A7B84100FF6708 /* SDImageAWebPCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664A26A7B84100FF6708 /* SDImageAWebPCoder.m */; };
		F307670526A7B84100FF6708 /* SDAnimatedImagePlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664C26A7B84100FF6708 /* SDAnimatedImagePlayer.m */; };
		F307670626A7B84100FF6708 /* UIImage+Transform.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664D26A7B84100FF6708 /* UIImage+Transform.m */; };
		F307670726A7B84100FF6708 /* SDWebImageDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = F307664F26A7B84100FF6708 /* SDWebImageDownloader.m */; };
		F307670826A7B84100FF6708 /* SDImageLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665126A7B84100FF6708 /* SDImageLoader.m */; };
		F307670926A7B84100FF6708 /* SDWebImageCacheSerializer.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665226A7B84100FF6708 /* SDWebImageCacheSerializer.m */; };
		F307670A26A7B84100FF6708 /* SDWebImageIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665326A7B84100FF6708 /* SDWebImageIndicator.m */; };
		F307670B26A7B84100FF6708 /* SDGraphicsImageRenderer.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665426A7B84100FF6708 /* SDGraphicsImageRenderer.m */; };
		F307670C26A7B84100FF6708 /* UIImage+GIF.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665526A7B84100FF6708 /* UIImage+GIF.m */; };
		F307670D26A7B84100FF6708 /* SDImageCodersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665626A7B84100FF6708 /* SDImageCodersManager.m */; };
		F307670E26A7B84100FF6708 /* UIImage+MultiFormat.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665926A7B84100FF6708 /* UIImage+MultiFormat.m */; };
		F307670F26A7B84100FF6708 /* SDMemoryCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665A26A7B84100FF6708 /* SDMemoryCache.m */; };
		F307671026A7B84100FF6708 /* SDWebImageCompat.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665C26A7B84100FF6708 /* SDWebImageCompat.m */; };
		F307671126A7B84100FF6708 /* SDImageAPNGCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665D26A7B84100FF6708 /* SDImageAPNGCoder.m */; };
		F307671226A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665E26A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.m */; };
		F307671326A7B84100FF6708 /* SDWebImageError.m in Sources */ = {isa = PBXBuildFile; fileRef = F307665F26A7B84100FF6708 /* SDWebImageError.m */; };
		F307671426A7B84100FF6708 /* SDWebImagePrefetcher.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666026A7B84100FF6708 /* SDWebImagePrefetcher.m */; };
		F307671526A7B84100FF6708 /* SDImageCoderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666126A7B84100FF6708 /* SDImageCoderHelper.m */; };
		F307671626A7B84100FF6708 /* NSButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666626A7B84100FF6708 /* NSButton+WebCache.m */; };
		F307671726A7B84100FF6708 /* SDImageIOCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666826A7B84100FF6708 /* SDImageIOCoder.m */; };
		F307671826A7B84100FF6708 /* SDDiskCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666A26A7B84100FF6708 /* SDDiskCache.m */; };
		F307671926A7B84100FF6708 /* SDAnimatedImageRep.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666C26A7B84100FF6708 /* SDAnimatedImageRep.m */; };
		F307671A26A7B84100FF6708 /* UIImage+ExtendedCacheData.m in Sources */ = {isa = PBXBuildFile; fileRef = F307666E26A7B84100FF6708 /* UIImage+ExtendedCacheData.m */; };
		F307671B26A7B84100FF6708 /* SDWebImageDownloaderConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667026A7B84100FF6708 /* SDWebImageDownloaderConfig.m */; };
		F307671C26A7B84100FF6708 /* SDImageCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667226A7B84100FF6708 /* SDImageCache.m */; };
		F307671D26A7B84100FF6708 /* SDImageGIFCoder.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667326A7B84100FF6708 /* SDImageGIFCoder.m */; };
		F307671E26A7B84100FF6708 /* SDImageFrame.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667526A7B84100FF6708 /* SDImageFrame.m */; };
		F307671F26A7B84100FF6708 /* SDWebImageDownloaderOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667626A7B84100FF6708 /* SDWebImageDownloaderOperation.m */; };
		F307672026A7B84100FF6708 /* SDImageLoadersManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667826A7B84100FF6708 /* SDImageLoadersManager.m */; };
		F307672126A7B84100FF6708 /* SDImageCachesManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667926A7B84100FF6708 /* SDImageCachesManager.m */; };
		F307672226A7B84100FF6708 /* SDWebImageTransition.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667A26A7B84100FF6708 /* SDWebImageTransition.m */; };
		F307672326A7B84100FF6708 /* SDImageTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667C26A7B84100FF6708 /* SDImageTransformer.m */; };
		F307672426A7B84100FF6708 /* NSData+ImageContentType.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667D26A7B84100FF6708 /* NSData+ImageContentType.m */; };
		F307672526A7B84100FF6708 /* UIImageView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307667E26A7B84100FF6708 /* UIImageView+WebCache.m */; };
		F307672626A7B84100FF6708 /* SDImageGraphics.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668126A7B84100FF6708 /* SDImageGraphics.m */; };
		F307672726A7B84100FF6708 /* SDImageCacheDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668626A7B84100FF6708 /* SDImageCacheDefine.m */; };
		F307672826A7B84100FF6708 /* UIImage+MemoryCacheCost.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668826A7B84100FF6708 /* UIImage+MemoryCacheCost.m */; };
		F307672926A7B84100FF6708 /* SDImageCacheConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668B26A7B84100FF6708 /* SDImageCacheConfig.m */; };
		F307672A26A7B84100FF6708 /* SDWebImageCacheKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668C26A7B84100FF6708 /* SDWebImageCacheKeyFilter.m */; };
		F307672B26A7B84100FF6708 /* SDWebImageDefine.m in Sources */ = {isa = PBXBuildFile; fileRef = F307668F26A7B84100FF6708 /* SDWebImageDefine.m */; };
		F307672C26A7B84100FF6708 /* SDWebImageOptionsProcessor.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669126A7B84100FF6708 /* SDWebImageOptionsProcessor.m */; };
		F307672D26A7B84100FF6708 /* UIView+WebCacheOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669226A7B84100FF6708 /* UIView+WebCacheOperation.m */; };
		F307672E26A7B84100FF6708 /* UIView+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669326A7B84100FF6708 /* UIView+WebCache.m */; };
		F307672F26A7B84100FF6708 /* UIImage+Metadata.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669A26A7B84100FF6708 /* UIImage+Metadata.m */; };
		F307673026A7B84100FF6708 /* UIButton+WebCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669C26A7B84100FF6708 /* UIButton+WebCache.m */; };
		F307673126A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.m in Sources */ = {isa = PBXBuildFile; fileRef = F307669D26A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.m */; };
		F307673226A7B84100FF6708 /* UIColor+SDHexString.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766E226A7B84100FF6708 /* UIColor+SDHexString.m */; };
		F307673326A7B84100FF6708 /* SDImageCachesManagerOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766E426A7B84100FF6708 /* SDImageCachesManagerOperation.m */; };
		F307673426A7B84100FF6708 /* SDImageAssetManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766E726A7B84100FF6708 /* SDImageAssetManager.m */; };
		F307673526A7B84100FF6708 /* SDDeviceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766ED26A7B84100FF6708 /* SDDeviceHelper.m */; };
		F307673626A7B84100FF6708 /* SDWeakProxy.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766EE26A7B84100FF6708 /* SDWeakProxy.m */; };
		F307673726A7B84100FF6708 /* SDInternalMacros.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766EF26A7B84100FF6708 /* SDInternalMacros.m */; };
		F307673826A7B84100FF6708 /* SDFileAttributeHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766F026A7B84100FF6708 /* SDFileAttributeHelper.m */; };
		F307673926A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766F226A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.m */; };
		F307673A26A7B84100FF6708 /* SDDisplayLink.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766F426A7B84100FF6708 /* SDDisplayLink.m */; };
		F307673B26A7B84100FF6708 /* SDAsyncBlockOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766F626A7B84100FF6708 /* SDAsyncBlockOperation.m */; };
		F307673C26A7B84100FF6708 /* SDAssociatedObject.m in Sources */ = {isa = PBXBuildFile; fileRef = F30766F726A7B84100FF6708 /* SDAssociatedObject.m */; };
		F307675426A7CA2200FF6708 /* CheckImageHaveFace.m in Sources */ = {isa = PBXBuildFile; fileRef = F307675326A7CA2200FF6708 /* CheckImageHaveFace.m */; };
		F307676F26A80C0E00FF6708 /* ImageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307676E26A80C0E00FF6708 /* ImageManager.m */; };
		F307677B26A81CC700FF6708 /* FileManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307677A26A81CC700FF6708 /* FileManager.m */; };
		F307678126A8201600FF6708 /* TimeTool.m in Sources */ = {isa = PBXBuildFile; fileRef = F307678026A8201600FF6708 /* TimeTool.m */; };
		F307DA2126A987F800E6C02D /* DrawManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F307DA2026A987F800E6C02D /* DrawManager.m */; };
		F320396926CF507F008295B2 /* LanguageManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F320396726CF507D008295B2 /* LanguageManager.m */; };
		F32048F826AFF19F00E0D893 /* VisionImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = F32048F726AFF19F00E0D893 /* VisionImageView.m */; };
		F3286E5626A6D5E000C46DF1 /* PsychologicalViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F3286E5526A6D5E000C46DF1 /* PsychologicalViewController.m */; };
		F3286E5C26A6D66400C46DF1 /* PsychologicalResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = F3286E5B26A6D66400C46DF1 /* PsychologicalResultVC.m */; };
		F3286E6226A6D76700C46DF1 /* MagicViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F3286E6126A6D76700C46DF1 /* MagicViewController.m */; };
		F33007F726BCC39A00CA9E91 /* MagicChangeFaceView.mm in Sources */ = {isa = PBXBuildFile; fileRef = F33007F526BCC39800CA9E91 /* MagicChangeFaceView.mm */; };
		F33007FE26BCC42E00CA9E91 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F362D57D26BB8C5500E38EC6 /* libc++.tbd */; };
		F3301A3026BCDCC800CA9E91 /* MagicResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = F3301A2F26BCDCC800CA9E91 /* MagicResultVC.m */; };
		F3301A9626BD3AD600CA9E91 /* template1.jpg in Resources */ = {isa = PBXBuildFile; fileRef = F3301A9226BD3AD200CA9E91 /* template1.jpg */; };
		F3301A9726BD3AD600CA9E91 /* template3.jpg in Resources */ = {isa = PBXBuildFile; fileRef = F3301A9326BD3AD400CA9E91 /* template3.jpg */; };
		F3301A9826BD3AD600CA9E91 /* template2.jpg in Resources */ = {isa = PBXBuildFile; fileRef = F3301A9426BD3AD600CA9E91 /* template2.jpg */; };
		F34A6BE426A69899006DE0E0 /* CartoonViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6BE326A69899006DE0E0 /* CartoonViewController.m */; };
		F34A6BED26A69D00006DE0E0 /* CartoonResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6BEC26A69D00006DE0E0 /* CartoonResultVC.m */; };
		F34A6C1426A6A121006DE0E0 /* SVRadialGradientLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C0A26A6A121006DE0E0 /* SVRadialGradientLayer.m */; };
		F34A6C1526A6A121006DE0E0 /* SVProgressAnimatedView.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C0D26A6A121006DE0E0 /* SVProgressAnimatedView.m */; };
		F34A6C1626A6A121006DE0E0 /* SVProgressHUD.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C1026A6A121006DE0E0 /* SVProgressHUD.m */; };
		F34A6C1726A6A121006DE0E0 /* SVIndefiniteAnimatedView.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C1126A6A121006DE0E0 /* SVIndefiniteAnimatedView.m */; };
		F34A6C1826A6A121006DE0E0 /* SVProgressHUD.bundle in Resources */ = {isa = PBXBuildFile; fileRef = F34A6C1226A6A121006DE0E0 /* SVProgressHUD.bundle */; };
		F34A6C1E26A6B534006DE0E0 /* UIImage+FixOrientation.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C1D26A6B534006DE0E0 /* UIImage+FixOrientation.m */; };
		F34A6C2426A6BBDE006DE0E0 /* MyImagePickerResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = F34A6C2326A6BBDE006DE0E0 /* MyImagePickerResultVC.m */; };
		F350138626E5F7FD00700B10 /* GPUImageTwoPassTextureSamplingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350122B26E5F7FC00700B10 /* GPUImageTwoPassTextureSamplingFilter.m */; };
		F350138726E5F7FD00700B10 /* GPUImageFilterGroup.m in Sources */ = {isa = PBXBuildFile; fileRef = F350122D26E5F7FC00700B10 /* GPUImageFilterGroup.m */; };
		F350138826E5F7FD00700B10 /* GPUImageFourInputFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350122E26E5F7FC00700B10 /* GPUImageFourInputFilter.m */; };
		F350138926E5F7FD00700B10 /* GPUImageTwoPassFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350122F26E5F7FC00700B10 /* GPUImageTwoPassFilter.m */; };
		F350138A26E5F7FD00700B10 /* GPUImageTwoInputFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123026E5F7FC00700B10 /* GPUImageTwoInputFilter.m */; };
		F350138B26E5F7FD00700B10 /* GPUImageChromaKeyFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123226E5F7FC00700B10 /* GPUImageChromaKeyFilter.m */; };
		F350138C26E5F7FD00700B10 /* GPUImageLuminanceThresholdFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123326E5F7FC00700B10 /* GPUImageLuminanceThresholdFilter.m */; };
		F350138D26E5F7FD00700B10 /* GPUImageSoftEleganceFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123626E5F7FC00700B10 /* GPUImageSoftEleganceFilter.m */; };
		F350138E26E5F7FD00700B10 /* GPUImageColorInvertFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123926E5F7FC00700B10 /* GPUImageColorInvertFilter.m */; };
		F350138F26E5F7FD00700B10 /* GPUImageGrayscaleFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123A26E5F7FC00700B10 /* GPUImageGrayscaleFilter.m */; };
		F350139026E5F7FD00700B10 /* GPUImageLevelsFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123B26E5F7FC00700B10 /* GPUImageLevelsFilter.m */; };
		F350139126E5F7FD00700B10 /* GPUImageSolarizeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123C26E5F7FC00700B10 /* GPUImageSolarizeFilter.m */; };
		F350139226E5F7FD00700B10 /* GPUImageHistogramFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123D26E5F7FC00700B10 /* GPUImageHistogramFilter.m */; };
		F350139326E5F7FD00700B10 /* GPUImageMissEtikateFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123E26E5F7FC00700B10 /* GPUImageMissEtikateFilter.m */; };
		F350139426E5F7FD00700B10 /* GPUImageHistogramEqualizationFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350123F26E5F7FC00700B10 /* GPUImageHistogramEqualizationFilter.m */; };
		F350139526E5F7FD00700B10 /* GPUImageHueFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124026E5F7FC00700B10 /* GPUImageHueFilter.m */; };
		F350139626E5F7FD00700B10 /* GPUImageAmatorkaFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124126E5F7FC00700B10 /* GPUImageAmatorkaFilter.m */; };
		F350139726E5F7FD00700B10 /* GPUImageHistogramGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124326E5F7FC00700B10 /* GPUImageHistogramGenerator.m */; };
		F350139826E5F7FD00700B10 /* GPUImageGammaFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124526E5F7FC00700B10 /* GPUImageGammaFilter.m */; };
		F350139926E5F7FD00700B10 /* GPUImageSolidColorGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124626E5F7FC00700B10 /* GPUImageSolidColorGenerator.m */; };
		F350139A26E5F7FD00700B10 /* GPUImageColorMatrixFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124726E5F7FC00700B10 /* GPUImageColorMatrixFilter.m */; };
		F350139B26E5F7FD00700B10 /* GPUImageAdaptiveThresholdFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124926E5F7FC00700B10 /* GPUImageAdaptiveThresholdFilter.m */; };
		F350139C26E5F7FD00700B10 /* GPUImageSaturationFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124A26E5F7FC00700B10 /* GPUImageSaturationFilter.m */; };
		F350139D26E5F7FD00700B10 /* GPUImageOpacityFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124B26E5F7FC00700B10 /* GPUImageOpacityFilter.m */; };
		F350139E26E5F7FD00700B10 /* GPUImageSepiaFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124D26E5F7FC00700B10 /* GPUImageSepiaFilter.m */; };
		F350139F26E5F7FD00700B10 /* GPUImageBrightnessFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124E26E5F7FC00700B10 /* GPUImageBrightnessFilter.m */; };
		F35013A026E5F7FD00700B10 /* GPUImageAverageLuminanceThresholdFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350124F26E5F7FC00700B10 /* GPUImageAverageLuminanceThresholdFilter.m */; };
		F35013A126E5F7FD00700B10 /* GPUImageRGBFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125126E5F7FC00700B10 /* GPUImageRGBFilter.m */; };
		F35013A226E5F7FD00700B10 /* GPUImageToneCurveFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125526E5F7FC00700B10 /* GPUImageToneCurveFilter.m */; };
		F35013A326E5F7FD00700B10 /* GPUImageLuminosity.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125826E5F7FC00700B10 /* GPUImageLuminosity.m */; };
		F35013A426E5F7FD00700B10 /* GPUImageAverageColor.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125A26E5F7FC00700B10 /* GPUImageAverageColor.m */; };
		F35013A526E5F7FD00700B10 /* GPUImageContrastFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125B26E5F7FC00700B10 /* GPUImageContrastFilter.m */; };
		F35013A626E5F7FD00700B10 /* GPUImageHazeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350125C26E5F7FC00700B10 /* GPUImageHazeFilter.m */; };
		F35013A726E5F7FD00700B10 /* GPUImageWhiteBalanceFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350126026E5F7FC00700B10 /* GPUImageWhiteBalanceFilter.m */; };
		F35013A826E5F7FD00700B10 /* GPUImageMonochromeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350126226E5F7FC00700B10 /* GPUImageMonochromeFilter.m */; };
		F35013A926E5F7FD00700B10 /* GPUImageHSBFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350126C26E5F7FC00700B10 /* GPUImageHSBFilter.m */; };
		F35013AA26E5F7FD00700B10 /* GPUImageHighlightShadowFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350126F26E5F7FC00700B10 /* GPUImageHighlightShadowFilter.m */; };
		F35013AB26E5F7FD00700B10 /* GPUImageExposureFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127326E5F7FC00700B10 /* GPUImageExposureFilter.m */; };
		F35013AC26E5F7FD00700B10 /* GPUImageLuminanceRangeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127426E5F7FC00700B10 /* GPUImageLuminanceRangeFilter.m */; };
		F35013AD26E5F7FD00700B10 /* GPUImageFalseColorFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127526E5F7FC00700B10 /* GPUImageFalseColorFilter.m */; };
		F35013AE26E5F7FD00700B10 /* GPUImageLookupFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127726E5F7FC00700B10 /* GPUImageLookupFilter.m */; };
		F35013AF26E5F7FD00700B10 /* GPUImage3x3TextureSamplingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127B26E5F7FC00700B10 /* GPUImage3x3TextureSamplingFilter.m */; };
		F35013B026E5F7FD00700B10 /* GPUImageUnsharpMaskFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350127F26E5F7FC00700B10 /* GPUImageUnsharpMaskFilter.m */; };
		F35013B126E5F7FD00700B10 /* GPUImageThresholdEdgeDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128126E5F7FC00700B10 /* GPUImageThresholdEdgeDetectionFilter.m */; };
		F35013B226E5F7FD00700B10 /* GPUImageParallelCoordinateLineTransformFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128326E5F7FC00700B10 /* GPUImageParallelCoordinateLineTransformFilter.m */; };
		F35013B326E5F7FD00700B10 /* GPUImageSobelEdgeDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128426E5F7FC00700B10 /* GPUImageSobelEdgeDetectionFilter.m */; };
		F35013B426E5F7FD00700B10 /* GPUImageHighPassFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128626E5F7FC00700B10 /* GPUImageHighPassFilter.m */; };
		F35013B526E5F7FD00700B10 /* GPUImageGaussianBlurPositionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128826E5F7FC00700B10 /* GPUImageGaussianBlurPositionFilter.m */; };
		F35013B626E5F7FD00700B10 /* GPUImageColorPackingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128926E5F7FC00700B10 /* GPUImageColorPackingFilter.m */; };
		F35013B726E5F7FD00700B10 /* GPUImageWeakPixelInclusionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128A26E5F7FC00700B10 /* GPUImageWeakPixelInclusionFilter.m */; };
		F35013B826E5F7FD00700B10 /* GPUImageNonMaximumSuppressionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128B26E5F7FC00700B10 /* GPUImageNonMaximumSuppressionFilter.m */; };
		F35013B926E5F7FD00700B10 /* GPUImageCannyEdgeDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350128F26E5F7FC00700B10 /* GPUImageCannyEdgeDetectionFilter.m */; };
		F35013BA26E5F7FD00700B10 /* GPUImageFASTCornerDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350129026E5F7FC00700B10 /* GPUImageFASTCornerDetectionFilter.m */; };
		F35013BB26E5F7FD00700B10 /* GPUImageiOSBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350129426E5F7FC00700B10 /* GPUImageiOSBlurFilter.m */; };
		F35013BC26E5F7FD00700B10 /* GPUImageTransformFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350129626E5F7FC00700B10 /* GPUImageTransformFilter.m */; };
		F35013BD26E5F7FD00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350129826E5F7FC00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.m */; };
		F35013BE26E5F7FD00700B10 /* GPUImageBilateralFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350129A26E5F7FC00700B10 /* GPUImageBilateralFilter.m */; };
		F35013BF26E5F7FD00700B10 /* GPUImageHarrisCornerDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A026E5F7FC00700B10 /* GPUImageHarrisCornerDetectionFilter.m */; };
		F35013C026E5F7FD00700B10 /* GPUImageZoomBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A226E5F7FC00700B10 /* GPUImageZoomBlurFilter.m */; };
		F35013C126E5F7FD00700B10 /* GPUImageOpeningFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A426E5F7FC00700B10 /* GPUImageOpeningFilter.m */; };
		F35013C226E5F7FD00700B10 /* GPUImageErosionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A526E5F7FC00700B10 /* GPUImageErosionFilter.m */; };
		F35013C326E5F7FD00700B10 /* GPUImageCropFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A626E5F7FC00700B10 /* GPUImageCropFilter.m */; };
		F35013C426E5F7FD00700B10 /* GPUImageRGBClosingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A726E5F7FC00700B10 /* GPUImageRGBClosingFilter.m */; };
		F35013C526E5F7FD00700B10 /* GPUImageLineGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012A826E5F7FC00700B10 /* GPUImageLineGenerator.m */; };
		F35013C626E5F7FD00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012AA26E5F7FC00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.m */; };
		F35013C726E5F7FD00700B10 /* GPUImageLanczosResamplingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012AB26E5F7FC00700B10 /* GPUImageLanczosResamplingFilter.m */; };
		F35013C826E5F7FD00700B10 /* GPUImageXYDerivativeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012AC26E5F7FC00700B10 /* GPUImageXYDerivativeFilter.m */; };
		F35013C926E5F7FD00700B10 /* GPUImageLaplacianFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012AF26E5F7FC00700B10 /* GPUImageLaplacianFilter.m */; };
		F35013CA26E5F7FD00700B10 /* GPUImageBoxBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B026E5F7FC00700B10 /* GPUImageBoxBlurFilter.m */; };
		F35013CB26E5F7FD00700B10 /* GPUImageTiltShiftFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B126E5F7FC00700B10 /* GPUImageTiltShiftFilter.m */; };
		F35013CC26E5F7FD00700B10 /* GPUImageGaussianBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B226E5F7FC00700B10 /* GPUImageGaussianBlurFilter.m */; };
		F35013CD26E5F7FD00700B10 /* GPUImageSharpenFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B426E5F7FC00700B10 /* GPUImageSharpenFilter.m */; };
		F35013CE26E5F7FD00700B10 /* GPUImageGaussianSelectiveBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B526E5F7FC00700B10 /* GPUImageGaussianSelectiveBlurFilter.m */; };
		F35013CF26E5F7FD00700B10 /* GPUImageCrosshairGenerator.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012B926E5F7FC00700B10 /* GPUImageCrosshairGenerator.m */; };
		F35013D026E5F7FD00700B10 /* GPUImageRGBDilationFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012BC26E5F7FC00700B10 /* GPUImageRGBDilationFilter.m */; };
		F35013D126E5F7FD00700B10 /* GPUImageColorLocalBinaryPatternFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012BE26E5F7FC00700B10 /* GPUImageColorLocalBinaryPatternFilter.m */; };
		F35013D226E5F7FD00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C026E5F7FC00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.m */; };
		F35013D326E5F7FD00700B10 /* GPUImageRGBOpeningFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C226E5F7FC00700B10 /* GPUImageRGBOpeningFilter.m */; };
		F35013D426E5F7FD00700B10 /* GPUImageRGBErosionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C326E5F7FC00700B10 /* GPUImageRGBErosionFilter.m */; };
		F35013D526E5F7FD00700B10 /* GPUImageLowPassFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C526E5F7FC00700B10 /* GPUImageLowPassFilter.m */; };
		F35013D626E5F7FD00700B10 /* GPUImageHoughTransformLineDetector.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C726E5F7FC00700B10 /* GPUImageHoughTransformLineDetector.m */; };
		F35013D726E5F7FD00700B10 /* GPUImageMotionDetector.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012C926E5F7FC00700B10 /* GPUImageMotionDetector.m */; };
		F35013D826E5F7FD00700B10 /* GPUImageClosingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012CA26E5F7FC00700B10 /* GPUImageClosingFilter.m */; };
		F35013D926E5F7FD00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012CC26E5F7FC00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.m */; };
		F35013DA26E5F7FD00700B10 /* GPUImageMedianFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012CD26E5F7FC00700B10 /* GPUImageMedianFilter.m */; };
		F35013DB26E5F7FD00700B10 /* GPUImageLocalBinaryPatternFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012CE26E5F7FC00700B10 /* GPUImageLocalBinaryPatternFilter.m */; };
		F35013DC26E5F7FD00700B10 /* GPUImageMotionBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012D026E5F7FC00700B10 /* GPUImageMotionBlurFilter.m */; };
		F35013DD26E5F7FD00700B10 /* GPUImagePrewittEdgeDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012D226E5F7FC00700B10 /* GPUImagePrewittEdgeDetectionFilter.m */; };
		F35013DE26E5F7FD00700B10 /* GPUImageDilationFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012D826E5F7FC00700B10 /* GPUImageDilationFilter.m */; };
		F35013DF26E5F7FD00700B10 /* GPUImage3x3ConvolutionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012DB26E5F7FC00700B10 /* GPUImage3x3ConvolutionFilter.m */; };
		F35013E026E5F7FD00700B10 /* GPUImageNobleCornerDetectionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012DC26E5F7FC00700B10 /* GPUImageNobleCornerDetectionFilter.m */; };
		F35013E126E5F7FD00700B10 /* GPUImageSingleComponentGaussianBlurFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012DD26E5F7FC00700B10 /* GPUImageSingleComponentGaussianBlurFilter.m */; };
		F35013E226E5F7FD00700B10 /* GPUImageColourFASTSamplingOperation.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012E126E5F7FC00700B10 /* GPUImageColourFASTSamplingOperation.m */; };
		F35013E326E5F7FD00700B10 /* GPUImageColourFASTFeatureDetector.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012E326E5F7FC00700B10 /* GPUImageColourFASTFeatureDetector.m */; };
		F35013E426E5F7FD00700B10 /* GPUImageThreeInputFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012EA26E5F7FC00700B10 /* GPUImageThreeInputFilter.m */; };
		F35013E526E5F7FD00700B10 /* GPUImageBuffer.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012EB26E5F7FC00700B10 /* GPUImageBuffer.m */; };
		F35013E626E5F7FD00700B10 /* GPUImageColorBurnBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012ED26E5F7FC00700B10 /* GPUImageColorBurnBlendFilter.m */; };
		F35013E726E5F7FD00700B10 /* GPUImageAlphaBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012F526E5F7FC00700B10 /* GPUImageAlphaBlendFilter.m */; };
		F35013E826E5F7FD00700B10 /* GPUImageExclusionBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012F626E5F7FC00700B10 /* GPUImageExclusionBlendFilter.m */; };
		F35013E926E5F7FD00700B10 /* GPUImageOverlayBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012F726E5F7FC00700B10 /* GPUImageOverlayBlendFilter.m */; };
		F35013EA26E5F7FD00700B10 /* GPUImageMultiplyBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012F826E5F7FC00700B10 /* GPUImageMultiplyBlendFilter.m */; };
		F35013EB26E5F7FD00700B10 /* GPUImageHardLightBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012F926E5F7FC00700B10 /* GPUImageHardLightBlendFilter.m */; };
		F35013EC26E5F7FD00700B10 /* GPUImageDissolveBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012FC26E5F7FC00700B10 /* GPUImageDissolveBlendFilter.m */; };
		F35013ED26E5F7FD00700B10 /* GPUImageDifferenceBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F35012FF26E5F7FC00700B10 /* GPUImageDifferenceBlendFilter.m */; };
		F35013EE26E5F7FD00700B10 /* GPUImageDivideBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130026E5F7FC00700B10 /* GPUImageDivideBlendFilter.m */; };
		F35013EF26E5F7FD00700B10 /* GPUImageColorDodgeBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130226E5F7FC00700B10 /* GPUImageColorDodgeBlendFilter.m */; };
		F35013F026E5F7FD00700B10 /* GPUImageSourceOverBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130326E5F7FC00700B10 /* GPUImageSourceOverBlendFilter.m */; };
		F35013F126E5F7FD00700B10 /* GPUImageSaturationBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130626E5F7FC00700B10 /* GPUImageSaturationBlendFilter.m */; };
		F35013F226E5F7FD00700B10 /* GPUImagePoissonBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130726E5F7FC00700B10 /* GPUImagePoissonBlendFilter.m */; };
		F35013F326E5F7FD00700B10 /* GPUImageMaskFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130826E5F7FC00700B10 /* GPUImageMaskFilter.m */; };
		F35013F426E5F7FD00700B10 /* GPUImageSoftLightBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130926E5F7FC00700B10 /* GPUImageSoftLightBlendFilter.m */; };
		F35013F526E5F7FD00700B10 /* GPUImageSubtractBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130A26E5F7FC00700B10 /* GPUImageSubtractBlendFilter.m */; };
		F35013F626E5F7FD00700B10 /* GPUImageColorBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130B26E5F7FC00700B10 /* GPUImageColorBlendFilter.m */; };
		F35013F726E5F7FD00700B10 /* GPUImageLinearBurnBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130E26E5F7FD00700B10 /* GPUImageLinearBurnBlendFilter.m */; };
		F35013F826E5F7FD00700B10 /* GPUImageScreenBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350130F26E5F7FD00700B10 /* GPUImageScreenBlendFilter.m */; };
		F35013F926E5F7FD00700B10 /* GPUImageHueBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131026E5F7FD00700B10 /* GPUImageHueBlendFilter.m */; };
		F35013FA26E5F7FD00700B10 /* GPUImageAddBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131226E5F7FD00700B10 /* GPUImageAddBlendFilter.m */; };
		F35013FB26E5F7FD00700B10 /* GPUImageNormalBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131326E5F7FD00700B10 /* GPUImageNormalBlendFilter.m */; };
		F35013FC26E5F7FD00700B10 /* GPUImageLuminosityBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131826E5F7FD00700B10 /* GPUImageLuminosityBlendFilter.m */; };
		F35013FD26E5F7FD00700B10 /* GPUImageChromaKeyBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131A26E5F7FD00700B10 /* GPUImageChromaKeyBlendFilter.m */; };
		F35013FE26E5F7FD00700B10 /* GPUImageLightenBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350131D26E5F7FD00700B10 /* GPUImageLightenBlendFilter.m */; };
		F35013FF26E5F7FD00700B10 /* GPUImageDarkenBlendFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132026E5F7FD00700B10 /* GPUImageDarkenBlendFilter.m */; };
		F350140026E5F7FD00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132226E5F7FD00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.m */; };
		F350140126E5F7FD00700B10 /* GPUImageEmbossFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132926E5F7FD00700B10 /* GPUImageEmbossFilter.m */; };
		F350140226E5F7FD00700B10 /* GPUImagePixellatePositionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132C26E5F7FD00700B10 /* GPUImagePixellatePositionFilter.m */; };
		F350140326E5F7FD00700B10 /* GPUImageVignetteFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132D26E5F7FD00700B10 /* GPUImageVignetteFilter.m */; };
		F350140426E5F7FD00700B10 /* GPUImageSketchFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132E26E5F7FD00700B10 /* GPUImageSketchFilter.m */; };
		F350140526E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350132F26E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.m */; };
		F350140626E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133026E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.m */; };
		F350140726E5F7FD00700B10 /* GPUImagePolarPixellateFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133526E5F7FD00700B10 /* GPUImagePolarPixellateFilter.m */; };
		F350140826E5F7FD00700B10 /* GPUImageToonFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133626E5F7FD00700B10 /* GPUImageToonFilter.m */; };
		F350140926E5F7FD00700B10 /* GPUImagePixellateFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133726E5F7FD00700B10 /* GPUImagePixellateFilter.m */; };
		F350140A26E5F7FD00700B10 /* GPUImageSphereRefractionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133926E5F7FD00700B10 /* GPUImageSphereRefractionFilter.m */; };
		F350140B26E5F7FD00700B10 /* GPUImageStretchDistortionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133B26E5F7FD00700B10 /* GPUImageStretchDistortionFilter.m */; };
		F350140C26E5F7FD00700B10 /* GPUImagePosterizeFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350133C26E5F7FD00700B10 /* GPUImagePosterizeFilter.m */; };
		F350140D26E5F7FD00700B10 /* GPUImageSwirlFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134026E5F7FD00700B10 /* GPUImageSwirlFilter.m */; };
		F350140E26E5F7FD00700B10 /* GPUImageHalftoneFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134126E5F7FD00700B10 /* GPUImageHalftoneFilter.m */; };
		F350140F26E5F7FD00700B10 /* GPUImageSmoothToonFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134226E5F7FD00700B10 /* GPUImageSmoothToonFilter.m */; };
		F350141026E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134326E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.m */; };
		F350141126E5F7FD00700B10 /* GPUImageThresholdSketchFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134426E5F7FD00700B10 /* GPUImageThresholdSketchFilter.m */; };
		F350141226E5F7FD00700B10 /* GPUImagePinchDistortionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134A26E5F7FD00700B10 /* GPUImagePinchDistortionFilter.m */; };
		F350141326E5F7FD00700B10 /* GPUImageGlassSphereFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134B26E5F7FD00700B10 /* GPUImageGlassSphereFilter.m */; };
		F350141426E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350134C26E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.m */; };
		F350141526E5F7FD00700B10 /* GPUImageCrosshatchFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135126E5F7FD00700B10 /* GPUImageCrosshatchFilter.m */; };
		F350141626E5F7FD00700B10 /* GPUImageKuwaharaFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135226E5F7FD00700B10 /* GPUImageKuwaharaFilter.m */; };
		F350141726E5F7FD00700B10 /* GPUImageMosaicFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135326E5F7FD00700B10 /* GPUImageMosaicFilter.m */; };
		F350141826E5F7FD00700B10 /* GPUImagePolkaDotFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135426E5F7FD00700B10 /* GPUImagePolkaDotFilter.m */; };
		F350141926E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135526E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.m */; };
		F350141A26E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135726E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.m */; };
		F350141B26E5F7FD00700B10 /* GPUImageFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135926E5F7FD00700B10 /* GPUImageFilter.m */; };
		F350141C26E5F7FD00700B10 /* GPUImageFilterPipeline.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135B26E5F7FD00700B10 /* GPUImageFilterPipeline.m */; };
		F350141D26E5F7FD00700B10 /* GPUImageContext.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135D26E5F7FD00700B10 /* GPUImageContext.m */; };
		F350141E26E5F7FD00700B10 /* GLProgram.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135E26E5F7FD00700B10 /* GLProgram.m */; };
		F350141F26E5F7FD00700B10 /* GPUImageFramebuffer.m in Sources */ = {isa = PBXBuildFile; fileRef = F350135F26E5F7FD00700B10 /* GPUImageFramebuffer.m */; };
		F350142026E5F7FD00700B10 /* GPUImageFramebufferCache.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136026E5F7FD00700B10 /* GPUImageFramebufferCache.m */; };
		F350142126E5F7FD00700B10 /* GPUImageUIElement.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136526E5F7FD00700B10 /* GPUImageUIElement.m */; };
		F350142226E5F7FD00700B10 /* GPUImageMovie.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136626E5F7FD00700B10 /* GPUImageMovie.m */; };
		F350142326E5F7FD00700B10 /* GPUImagePicture.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136826E5F7FD00700B10 /* GPUImagePicture.m */; };
		F350142426E5F7FD00700B10 /* GPUImageTextureInput.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136926E5F7FD00700B10 /* GPUImageTextureInput.m */; };
		F350142526E5F7FD00700B10 /* GPUImageOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136B26E5F7FD00700B10 /* GPUImageOutput.m */; };
		F350142626E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136D26E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.m */; };
		F350142726E5F7FD00700B10 /* GPUImageColorConversion.m in Sources */ = {isa = PBXBuildFile; fileRef = F350136F26E5F7FD00700B10 /* GPUImageColorConversion.m */; };
		F350142826E5F7FD00700B10 /* GPUImageMovieComposition.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137026E5F7FD00700B10 /* GPUImageMovieComposition.m */; };
		F350142926E5F7FD00700B10 /* GPUImageRawDataInput.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137126E5F7FD00700B10 /* GPUImageRawDataInput.m */; };
		F350142A26E5F7FD00700B10 /* GPUImageVideoCamera.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137326E5F7FD00700B10 /* GPUImageVideoCamera.m */; };
		F350142B26E5F7FD00700B10 /* GPUImageStillCamera.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137726E5F7FD00700B10 /* GPUImageStillCamera.m */; };
		F350142C26E5F7FD00700B10 /* GPUImageMovieWriter.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137D26E5F7FD00700B10 /* GPUImageMovieWriter.m */; };
		F350142D26E5F7FD00700B10 /* GPUImageRawDataOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = F350137F26E5F7FD00700B10 /* GPUImageRawDataOutput.m */; };
		F350142E26E5F7FD00700B10 /* GPUImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = F350138026E5F7FD00700B10 /* GPUImageView.m */; };
		F350142F26E5F7FD00700B10 /* GPUImageTextureOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = F350138226E5F7FD00700B10 /* GPUImageTextureOutput.m */; };
		F3581E8B26A9043E001B2DC7 /* FMDatabaseQueue.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E8126A9043E001B2DC7 /* FMDatabaseQueue.m */; };
		F3581E8C26A9043E001B2DC7 /* FMDatabaseAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E8426A9043E001B2DC7 /* FMDatabaseAdditions.m */; };
		F3581E8D26A9043E001B2DC7 /* FMDatabase.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E8526A9043E001B2DC7 /* FMDatabase.m */; };
		F3581E8E26A9043E001B2DC7 /* FMDatabasePool.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E8926A9043E001B2DC7 /* FMDatabasePool.m */; };
		F3581E8F26A9043E001B2DC7 /* FMResultSet.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E8A26A9043E001B2DC7 /* FMResultSet.m */; };
		F3581E9426A9045C001B2DC7 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F3581E9326A9045C001B2DC7 /* libsqlite3.tbd */; };
		F3581E9926A90463001B2DC7 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = F3581E9826A90463001B2DC7 /* libsqlite3.0.tbd */; };
		F3581E9F26A90683001B2DC7 /* DatabaseManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F3581E9E26A90683001B2DC7 /* DatabaseManager.m */; };
		F3620E7B26BBC83100E38EC6 /* shape_predictor_68_face_landmarks.dat in Resources */ = {isa = PBXBuildFile; fileRef = F36207C526BBC82800E38EC6 /* shape_predictor_68_face_landmarks.dat */; };
		F36210CB26BBC9B600E38EC6 /* FacePointsTool.mm in Sources */ = {isa = PBXBuildFile; fileRef = F36210CA26BBC9B600E38EC6 /* FacePointsTool.mm */; };
		F362B30026BB877300E38EC6 /* lock.png in Resources */ = {isa = PBXBuildFile; fileRef = F362B2FE26BB877300E38EC6 /* lock.png */; };
		F362B30826BB880500E38EC6 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F362B30726BB880500E38EC6 /* CoreMedia.framework */; };
		F362B30A26BB880F00E38EC6 /* AssetsLibrary.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F362B30926BB880F00E38EC6 /* AssetsLibrary.framework */; };
		F362B30C26BB881500E38EC6 /* Accelerate.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F362B30B26BB881500E38EC6 /* Accelerate.framework */; };
		F36B7FD126C383ED00A30090 /* libdlib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F36B7FD026C383ED00A30090 /* libdlib.a */; };
		F37D4AB72697D96300A6740F /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AB62697D96300A6740F /* AppDelegate.m */; };
		F37D4ABA2697D96300A6740F /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AB92697D96300A6740F /* SceneDelegate.m */; };
		F37D4AC02697D96300A6740F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F37D4ABE2697D96300A6740F /* Main.storyboard */; };
		F37D4AC22697D96300A6740F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F37D4AC12697D96300A6740F /* Assets.xcassets */; };
		F37D4AC52697D96300A6740F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F37D4AC32697D96300A6740F /* LaunchScreen.storyboard */; };
		F37D4AC82697D96300A6740F /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AC72697D96300A6740F /* main.m */; };
		F37D4AD22697D96400A6740F /* TimeMachineTests.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AD12697D96400A6740F /* TimeMachineTests.m */; };
		F37D4ADD2697D96400A6740F /* TimeMachineUITests.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4ADC2697D96400A6740F /* TimeMachineUITests.m */; };
		F37D4AEF2697D9A300A6740F /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = F37D4AEE2697D9A300A6740F /* AVFoundation.framework */; };
		F37D4B0B2697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AF32697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.m */; };
		F37D4B0C2697D9B200A6740F /* ViewController+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4AFC2697D9B200A6740F /* ViewController+MASAdditions.m */; };
		F37D4B0D2697D9B200A6740F /* MASCompositeConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B002697D9B200A6740F /* MASCompositeConstraint.m */; };
		F37D4B0E2697D9B200A6740F /* MASConstraintMaker.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B012697D9B200A6740F /* MASConstraintMaker.m */; };
		F37D4B0F2697D9B200A6740F /* MASLayoutConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B022697D9B200A6740F /* MASLayoutConstraint.m */; };
		F37D4B102697D9B200A6740F /* NSArray+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B032697D9B200A6740F /* NSArray+MASAdditions.m */; };
		F37D4B112697D9B200A6740F /* View+MASAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B042697D9B200A6740F /* View+MASAdditions.m */; };
		F37D4B122697D9B200A6740F /* MASConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B072697D9B200A6740F /* MASConstraint.m */; };
		F37D4B132697D9B200A6740F /* MASViewConstraint.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B092697D9B200A6740F /* MASViewConstraint.m */; };
		F37D4B142697D9B200A6740F /* MASViewAttribute.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B0A2697D9B200A6740F /* MASViewAttribute.m */; };
		F37D4B1B2697D9C000A6740F /* MyColor.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B192697D9C000A6740F /* MyColor.m */; };
		F37D4B242697D9EB00A6740F /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B222697D9EB00A6740F /* ViewController.m */; };
		F37D4B302697DA0400A6740F /* HomeCell.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B282697DA0400A6740F /* HomeCell.m */; };
		F37D4B312697DA0400A6740F /* MyImagePickerController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B292697DA0400A6740F /* MyImagePickerController.m */; };
		F37D4B322697DA0400A6740F /* BaseViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B2A2697DA0400A6740F /* BaseViewController.m */; };
		F37D4B332697DA0400A6740F /* MyImagePickerMaskView.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B2D2697DA0400A6740F /* MyImagePickerMaskView.m */; };
		F37D4B3C2697DA3100A6740F /* YoungViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B3A2697DA3100A6740F /* YoungViewController.m */; };
		F37D4B612697F60300A6740F /* BabyViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B602697F60300A6740F /* BabyViewController.m */; };
		F37D4B6726981CE400A6740F /* BabyResultVC.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B6626981CE400A6740F /* BabyResultVC.m */; };
		F37D4B6D269826D600A6740F /* iPhoneXTool.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B6C269826D600A6740F /* iPhoneXTool.m */; };
		F37D4B82269845DE00A6740F /* AgednessViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F37D4B81269845DE00A6740F /* AgednessViewController.m */; };
		F3BDC3DF26C4F5610019A0F4 /* ImageTextButton.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3DD26C4F55E0019A0F4 /* ImageTextButton.m */; };
		F3BDC3E226C4F58A0019A0F4 /* MyPageControl.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3E126C4F58A0019A0F4 /* MyPageControl.m */; };
		F3BDC3EC26C502240019A0F4 /* TingFadeOutView.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3E526C5021F0019A0F4 /* TingFadeOutView.m */; };
		F3BDC3ED26C502240019A0F4 /* TingSimpleScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3E726C502220019A0F4 /* TingSimpleScrollView.m */; };
		F3BDC3EE26C502240019A0F4 /* TingCycleScrollView.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3EB26C502240019A0F4 /* TingCycleScrollView.m */; };
		F3BDC3FC26C5027B0019A0F4 /* MyProgressView.m in Sources */ = {isa = PBXBuildFile; fileRef = F3BDC3FB26C5027B0019A0F4 /* MyProgressView.m */; };
		FDC18AAFF28F343E3EC78A4B /* Pods_TimeMachine.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 64803D0B95379B4F80A43CA5 /* Pods_TimeMachine.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		F37D4ACE2697D96300A6740F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F37D4AAA2697D96300A6740F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F37D4AB12697D96300A6740F;
			remoteInfo = TimeMachine;
		};
		F37D4AD92697D96400A6740F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F37D4AAA2697D96300A6740F /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F37D4AB12697D96300A6740F;
			remoteInfo = TimeMachine;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		028C48365ED9935D7D141E02 /* Pods-TimeMachine.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachine.debug.xcconfig"; path = "Target Support Files/Pods-TimeMachine/Pods-TimeMachine.debug.xcconfig"; sourceTree = "<group>"; };
		64803D0B95379B4F80A43CA5 /* Pods_TimeMachine.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_TimeMachine.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6B503A42498A43226238314F /* Pods-TimeMachine-TimeMachineUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachine-TimeMachineUITests.debug.xcconfig"; path = "Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests.debug.xcconfig"; sourceTree = "<group>"; };
		83182782C7C73993F345BC3A /* Pods-TimeMachine.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachine.release.xcconfig"; path = "Target Support Files/Pods-TimeMachine/Pods-TimeMachine.release.xcconfig"; sourceTree = "<group>"; };
		A4179DE226AD354300FBB442 /* PalmsDatabaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsDatabaseManager.h; sourceTree = "<group>"; };
		A4179DE326AD354300FBB442 /* PalmsDatabaseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsDatabaseManager.m; sourceTree = "<group>"; };
		A4179DE526AD355D00FBB442 /* PsychologicalDatabaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PsychologicalDatabaseManager.h; sourceTree = "<group>"; };
		A4179DE626AD355D00FBB442 /* PsychologicalDatabaseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PsychologicalDatabaseManager.m; sourceTree = "<group>"; };
		A418CF7926A51C2F00AA67F8 /* AWPolygonView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AWPolygonView.h; sourceTree = "<group>"; };
		A418CF7A26A51C2F00AA67F8 /* AWPolygonView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AWPolygonView.m; sourceTree = "<group>"; };
		A418CF8526A5622900AA67F8 /* StarsView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StarsView.m; sourceTree = "<group>"; };
		A418CF8626A5622900AA67F8 /* StarView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StarView.m; sourceTree = "<group>"; };
		A418CF8726A5622900AA67F8 /* StarsView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StarsView.h; sourceTree = "<group>"; };
		A418CF8826A5622900AA67F8 /* Star_highlight.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Star_highlight.png; sourceTree = "<group>"; };
		A418CF8926A5622900AA67F8 /* Star.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Star.png; sourceTree = "<group>"; };
		A418CF8A26A5622900AA67F8 /* StarView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StarView.h; sourceTree = "<group>"; };
		A418CF8F26A58EEA00AA67F8 /* PalmsImagePickerVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsImagePickerVC.h; sourceTree = "<group>"; };
		A418CF9026A58EEA00AA67F8 /* PalmsImagePickerVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsImagePickerVC.m; sourceTree = "<group>"; };
		A429D1C626AA5AD700E145FD /* PalmsResultCircleMaskView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsResultCircleMaskView.h; sourceTree = "<group>"; };
		A429D1C726AA5AD700E145FD /* PalmsResultCircleMaskView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsResultCircleMaskView.m; sourceTree = "<group>"; };
		A4686DD726A11FA9005F7A87 /* PalmsViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsViewController.h; sourceTree = "<group>"; };
		A4686DD826A11FA9005F7A87 /* PalmsViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsViewController.m; sourceTree = "<group>"; };
		A4686DDA26A12C77005F7A87 /* PalmsUnscrambleVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsUnscrambleVC.h; sourceTree = "<group>"; };
		A4686DDB26A12C77005F7A87 /* PalmsUnscrambleVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsUnscrambleVC.m; sourceTree = "<group>"; };
		A4686DDD26A163BB005F7A87 /* PalmsResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PalmsResultVC.h; sourceTree = "<group>"; };
		A4686DDE26A163BB005F7A87 /* PalmsResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PalmsResultVC.m; sourceTree = "<group>"; };
		A49042D126C0E1C60083A3D7 /* template1.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = template1.txt; sourceTree = "<group>"; };
		A49042D526C0E2430083A3D7 /* template3.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = template3.txt; sourceTree = "<group>"; };
		A49042D626C0E2460083A3D7 /* template2.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = template2.txt; sourceTree = "<group>"; };
		A49CD67D26C9F62400F19C02 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		A49CD68526C9F6DE00F19C02 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		A49CD68726C9F6F000F19C02 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Main.strings"; sourceTree = "<group>"; };
		A49CD68826C9F6F300F19C02 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		A49CD68926C9F6F600F19C02 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		A49CD68A26C9F6F800F19C02 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		A50B9D662C194E1B002C4109 /* 手相_en.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = "手相_en.json"; sourceTree = "<group>"; };
		A50B9D692C194F34002C4109 /* UpgradeVIPViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpgradeVIPViewController.m; sourceTree = "<group>"; };
		A50B9D6A2C194F34002C4109 /* UpgradeVIPPayButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpgradeVIPPayButton.m; sourceTree = "<group>"; };
		A50B9D6C2C194F34002C4109 /* APPStoreIAPObserver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = APPStoreIAPObserver.m; sourceTree = "<group>"; };
		A50B9D6D2C194F34002C4109 /* NSString+Base64.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+Base64.m"; sourceTree = "<group>"; };
		A50B9D6E2C194F34002C4109 /* APPMakeStoreIAPManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = APPMakeStoreIAPManager.h; sourceTree = "<group>"; };
		A50B9D6F2C194F34002C4109 /* NSDictionary+Value.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSDictionary+Value.h"; sourceTree = "<group>"; };
		A50B9D702C194F34002C4109 /* APPStoreIAPObserver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = APPStoreIAPObserver.h; sourceTree = "<group>"; };
		A50B9D712C194F34002C4109 /* NSString+Base64.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+Base64.h"; sourceTree = "<group>"; };
		A50B9D722C194F34002C4109 /* APPMakeStoreIAPManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = APPMakeStoreIAPManager.m; sourceTree = "<group>"; };
		A50B9D732C194F34002C4109 /* NSDictionary+Value.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSDictionary+Value.m"; sourceTree = "<group>"; };
		A50B9D742C194F34002C4109 /* UpgradeVIPPayButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpgradeVIPPayButton.h; sourceTree = "<group>"; };
		A50B9D752C194F34002C4109 /* UpgradeVIPViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpgradeVIPViewController.h; sourceTree = "<group>"; };
		A50B9D7D2C1991F8002C4109 /* BSLaunchScreenVC.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSLaunchScreenVC.xib; sourceTree = "<group>"; };
		A50B9D7E2C1991F8002C4109 /* EnterViewBackground_en.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = EnterViewBackground_en.png; sourceTree = "<group>"; };
		A50B9D7F2C1991F8002C4109 /* UIView+BSAnimation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+BSAnimation.m"; sourceTree = "<group>"; };
		A50B9D802C1991F8002C4109 /* BSEnterBaseController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSEnterBaseController.m; sourceTree = "<group>"; };
		A50B9D812C1991F8002C4109 /* BSEnterBaseController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BSEnterBaseController.xib; sourceTree = "<group>"; };
		A50B9D822C1991F8002C4109 /* BSUserDefaultManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSUserDefaultManager.h; sourceTree = "<group>"; };
		A50B9D832C1991F8002C4109 /* BSLaunchScreenVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSLaunchScreenVC.m; sourceTree = "<group>"; };
		A50B9D842C1991F8002C4109 /* IntroduceBackground04.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = IntroduceBackground04.png; sourceTree = "<group>"; };
		A50B9D862C1991F8002C4109 /* IntroduceBackground02.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = IntroduceBackground02.png; sourceTree = "<group>"; };
		A50B9D872C1991F8002C4109 /* IntroduceBackground03.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = IntroduceBackground03.png; sourceTree = "<group>"; };
		A50B9D882C1991F8002C4109 /* IntroduceBackground01.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = IntroduceBackground01.png; sourceTree = "<group>"; };
		A50B9D8A2C1991F8002C4109 /* ProgressViewUserInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ProgressViewUserInfo.m; sourceTree = "<group>"; };
		A50B9D8B2C1991F8002C4109 /* BSWaitingAnimationView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSWaitingAnimationView.m; sourceTree = "<group>"; };
		A50B9D8C2C1991F8002C4109 /* BSIntroduceView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSIntroduceView.h; sourceTree = "<group>"; };
		A50B9D8D2C1991F8002C4109 /* BSEtDetailsView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSEtDetailsView.m; sourceTree = "<group>"; };
		A50B9D8E2C1991F8002C4109 /* BSSubscribeRadio.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSSubscribeRadio.m; sourceTree = "<group>"; };
		A50B9D8F2C1991F8002C4109 /* BSAgreementView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSAgreementView.m; sourceTree = "<group>"; };
		A50B9D902C1991F8002C4109 /* BSEtWebUrlView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSEtWebUrlView.m; sourceTree = "<group>"; };
		A50B9D912C1991F8002C4109 /* BSWaitingAnimationView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSWaitingAnimationView.h; sourceTree = "<group>"; };
		A50B9D922C1991F8002C4109 /* ProgressViewUserInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ProgressViewUserInfo.h; sourceTree = "<group>"; };
		A50B9D932C1991F8002C4109 /* BSEtDetailsView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSEtDetailsView.h; sourceTree = "<group>"; };
		A50B9D942C1991F8002C4109 /* BSSubscribeRadio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSSubscribeRadio.h; sourceTree = "<group>"; };
		A50B9D952C1991F8002C4109 /* BSIntroduceView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSIntroduceView.m; sourceTree = "<group>"; };
		A50B9D962C1991F8002C4109 /* BSEtWebUrlView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSEtWebUrlView.h; sourceTree = "<group>"; };
		A50B9D972C1991F8002C4109 /* BSAgreementView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSAgreementView.h; sourceTree = "<group>"; };
		A50B9D982C1991F8002C4109 /* UIView+BSAnimation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+BSAnimation.h"; sourceTree = "<group>"; };
		A50B9D992C1991F8002C4109 /* EnterViewBackground.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = EnterViewBackground.png; sourceTree = "<group>"; };
		A50B9D9A2C1991F8002C4109 /* BSLaunchScreenVC.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSLaunchScreenVC.h; sourceTree = "<group>"; };
		A50B9D9B2C1991F8002C4109 /* BSUserDefaultManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BSUserDefaultManager.m; sourceTree = "<group>"; };
		A50B9D9C2C1991F8002C4109 /* BSEnterBaseController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BSEnterBaseController.h; sourceTree = "<group>"; };
		A50C5E972D75A10900A601FC /* TMYearViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TMYearViewController.h; sourceTree = "<group>"; };
		A50C5E982D75A10900A601FC /* TMYearViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TMYearViewController.m; sourceTree = "<group>"; };
		A51C5B322D79965600266A15 /* UIButton+CenterImageAndTitle.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "UIButton+CenterImageAndTitle.h"; sourceTree = "<group>"; };
		A51C5B332D79965600266A15 /* UIButton+CenterImageAndTitle.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = "UIButton+CenterImageAndTitle.m"; sourceTree = "<group>"; };
		A51C5B352D799D8300266A15 /* TMZhanbuController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TMZhanbuController.h; sourceTree = "<group>"; };
		A51C5B362D799D8300266A15 /* TMZhanbuController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TMZhanbuController.m; sourceTree = "<group>"; };
		A51C5C192D79A13D00266A15 /* TiDi.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TiDi.h; sourceTree = "<group>"; };
		A51C5C1A2D79A13D00266A15 /* TiDi.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TiDi.m; sourceTree = "<group>"; };
		A51C5C1C2D79A29500266A15 /* DaYun.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DaYun.swift; sourceTree = "<group>"; };
		A51C5C1D2D79A29500266A15 /* EightChar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EightChar.swift; sourceTree = "<group>"; };
		A51C5C1E2D79A29500266A15 /* Foto.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Foto.swift; sourceTree = "<group>"; };
		A51C5C1F2D79A29500266A15 /* FotoFestival.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FotoFestival.swift; sourceTree = "<group>"; };
		A51C5C202D79A29500266A15 /* FotoUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FotoUtil.swift; sourceTree = "<group>"; };
		A51C5C212D79A29500266A15 /* Fu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Fu.swift; sourceTree = "<group>"; };
		A51C5C222D79A29500266A15 /* Holiday.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Holiday.swift; sourceTree = "<group>"; };
		A51C5C232D79A29500266A15 /* HolidayUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HolidayUtil.swift; sourceTree = "<group>"; };
		A51C5C242D79A29500266A15 /* JieQi.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JieQi.swift; sourceTree = "<group>"; };
		A51C5C252D79A29500266A15 /* LiuNian.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LiuNian.swift; sourceTree = "<group>"; };
		A51C5C262D79A29500266A15 /* LiuYue.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LiuYue.swift; sourceTree = "<group>"; };
		A51C5C272D79A29500266A15 /* Lunar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Lunar.swift; sourceTree = "<group>"; };
		A51C5C282D79A29500266A15 /* LunarMonth.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LunarMonth.swift; sourceTree = "<group>"; };
		A51C5C292D79A29500266A15 /* LunarTime.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LunarTime.swift; sourceTree = "<group>"; };
		A51C5C2A2D79A29500266A15 /* LunarUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LunarUtil.swift; sourceTree = "<group>"; };
		A51C5C2B2D79A29500266A15 /* LunarYear.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LunarYear.swift; sourceTree = "<group>"; };
		A51C5C2C2D79A29500266A15 /* NineStar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NineStar.swift; sourceTree = "<group>"; };
		A51C5C2D2D79A29500266A15 /* ShouXingUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShouXingUtil.swift; sourceTree = "<group>"; };
		A51C5C2E2D79A29500266A15 /* ShuJiu.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShuJiu.swift; sourceTree = "<group>"; };
		A51C5C2F2D79A29500266A15 /* Solar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Solar.swift; sourceTree = "<group>"; };
		A51C5C302D79A29500266A15 /* SolarHalfYear.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarHalfYear.swift; sourceTree = "<group>"; };
		A51C5C312D79A29500266A15 /* SolarMonth.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarMonth.swift; sourceTree = "<group>"; };
		A51C5C322D79A29500266A15 /* SolarSeason.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarSeason.swift; sourceTree = "<group>"; };
		A51C5C332D79A29500266A15 /* SolarUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarUtil.swift; sourceTree = "<group>"; };
		A51C5C342D79A29500266A15 /* SolarWeek.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarWeek.swift; sourceTree = "<group>"; };
		A51C5C352D79A29500266A15 /* SolarYear.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SolarYear.swift; sourceTree = "<group>"; };
		A51C5C362D79A29500266A15 /* Tao.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tao.swift; sourceTree = "<group>"; };
		A51C5C372D79A29500266A15 /* TaoFestival.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaoFestival.swift; sourceTree = "<group>"; };
		A51C5C382D79A29500266A15 /* TaoUtil.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaoUtil.swift; sourceTree = "<group>"; };
		A51C5C392D79A29500266A15 /* XiaoYun.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = XiaoYun.swift; sourceTree = "<group>"; };
		A51C5C3A2D79A29500266A15 /* Yun.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Yun.swift; sourceTree = "<group>"; };
		A570F6292C103F9D00614266 /* 手相.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = "手相.json"; sourceTree = "<group>"; };
		A5C295462D7C183400B3F394 /* OSSUploadImageTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = OSSUploadImageTool.h; sourceTree = "<group>"; };
		A5C295472D7C183400B3F394 /* OSSUploadImageTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = OSSUploadImageTool.m; sourceTree = "<group>"; };
		A5D334582A58FE7400F67392 /* TimeMachine-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "TimeMachine-Bridging-Header.h"; sourceTree = "<group>"; };
		A5D334592A58FE7500F67392 /* File.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = File.swift; sourceTree = "<group>"; };
		A5D3345E2A59110700F67392 /* UILabel+createLabels.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UILabel+createLabels.m"; sourceTree = "<group>"; };
		A5D334612A59110700F67392 /* PopAnimationTool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopAnimationTool.m; sourceTree = "<group>"; };
		A5D334622A59110700F67392 /* PopView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PopView.m; sourceTree = "<group>"; };
		A5D334632A59110700F67392 /* PopAnimationTool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopAnimationTool.h; sourceTree = "<group>"; };
		A5D334642A59110700F67392 /* PopView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PopView.h; sourceTree = "<group>"; };
		A5D334652A59110700F67392 /* UIImageView+Create.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+Create.h"; sourceTree = "<group>"; };
		A5D334662A59110700F67392 /* UIButton+Create.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+Create.m"; sourceTree = "<group>"; };
		A5D334672A59110700F67392 /* UpdateManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpdateManager.h; sourceTree = "<group>"; };
		A5D334682A59110700F67392 /* UIColor+Hex.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+Hex.m"; sourceTree = "<group>"; };
		A5D334692A59110700F67392 /* UIView+Extension.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Extension.h"; sourceTree = "<group>"; };
		A5D3346A2A59110700F67392 /* ZYEUpdateView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYEUpdateView.h; sourceTree = "<group>"; };
		A5D3346B2A59110700F67392 /* ZYEButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYEButton.h; sourceTree = "<group>"; };
		A5D3346C2A59110700F67392 /* ZYELabel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYELabel.m; sourceTree = "<group>"; };
		A5D3346D2A59110700F67392 /* UIImageView+Create.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+Create.m"; sourceTree = "<group>"; };
		A5D3346E2A59110700F67392 /* UILabel+createLabels.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UILabel+createLabels.h"; sourceTree = "<group>"; };
		A5D3346F2A59110700F67392 /* UIColor+Hex.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+Hex.h"; sourceTree = "<group>"; };
		A5D334702A59110700F67392 /* UpdateManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpdateManager.m; sourceTree = "<group>"; };
		A5D334712A59110700F67392 /* UIButton+Create.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+Create.h"; sourceTree = "<group>"; };
		A5D334722A59110700F67392 /* UIView+Extension.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Extension.m"; sourceTree = "<group>"; };
		A5D334732A59110700F67392 /* ZYELabel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ZYELabel.h; sourceTree = "<group>"; };
		A5D334742A59110700F67392 /* ZYEButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYEButton.m; sourceTree = "<group>"; };
		A5D334752A59110700F67392 /* ZYEUpdateView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ZYEUpdateView.m; sourceTree = "<group>"; };
		A5D334762A59110700F67392 /* CTDConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CTDConfig.h; sourceTree = "<group>"; };
		A5D334832A59117800F67392 /* Reachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Reachability.m; sourceTree = "<group>"; };
		A5D334842A59117800F67392 /* Reachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Reachability.h; sourceTree = "<group>"; };
		A5D334862A59123E00F67392 /* MyAdManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyAdManager.m; sourceTree = "<group>"; };
		A5D334872A59123E00F67392 /* MyAdManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyAdManager.h; sourceTree = "<group>"; };
		A5D334892A59131F00F67392 /* IPChangeIconView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = IPChangeIconView.m; sourceTree = "<group>"; };
		A5D3348A2A59132000F67392 /* IPChangeIconView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = IPChangeIconView.h; sourceTree = "<group>"; };
		A5DC045A2CB7693F006E281C /* FacePalmController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FacePalmController.h; sourceTree = "<group>"; };
		A5DC045B2CB7693F006E281C /* FacePalmController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FacePalmController.m; sourceTree = "<group>"; };
		A5DD99512D6C241900412F3C /* DeepSeekStreamClient.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DeepSeekStreamClient.h; sourceTree = "<group>"; };
		A5DD99522D6C241900412F3C /* DeepSeekStreamClient.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DeepSeekStreamClient.m; sourceTree = "<group>"; };
		A5E87C552CBD127B0039716D /* face_en.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = face_en.json; sourceTree = "<group>"; };
		A5E87C572CBD12B40039716D /* face.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = face.json; sourceTree = "<group>"; };
		A5FDEB4E2CBCF52E00F3D63A /* FaceResultController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FaceResultController.h; sourceTree = "<group>"; };
		A5FDEB4F2CBCF52E00F3D63A /* FaceResultController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FaceResultController.m; sourceTree = "<group>"; };
		AB8901B52DBF234700C17211 /* CardView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CardView.h; sourceTree = "<group>"; };
		AB8901B62DBF234700C17211 /* CardView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CardView.m; sourceTree = "<group>"; };
		AB8901B82DBF279C00C17211 /* CardSliderView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CardSliderView.h; sourceTree = "<group>"; };
		AB8901B92DBF279C00C17211 /* CardSliderView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CardSliderView.m; sourceTree = "<group>"; };
		AB8901BB2DBF758300C17211 /* AllFuncShowView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AllFuncShowView.h; sourceTree = "<group>"; };
		AB8901BC2DBF758300C17211 /* AllFuncShowView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AllFuncShowView.m; sourceTree = "<group>"; };
		AB8DB0972DBE05410052A79B /* MainTabbarVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MainTabbarVC.h; sourceTree = "<group>"; };
		AB8DB0982DBE05410052A79B /* MainTabbarVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MainTabbarVC.m; sourceTree = "<group>"; };
		AB8DB09A2DBE0A070052A79B /* MentalTestVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MentalTestVC.h; sourceTree = "<group>"; };
		AB8DB09B2DBE0A070052A79B /* MentalTestVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MentalTestVC.m; sourceTree = "<group>"; };
		AB9BE91D2DC0ABDD00D4CE8B /* FaceAnalysisVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FaceAnalysisVC.h; sourceTree = "<group>"; };
		AB9BE91E2DC0ABDD00D4CE8B /* FaceAnalysisVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FaceAnalysisVC.m; sourceTree = "<group>"; };
		ABA367A02DCB091D00E23A5C /* CopyLabel.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CopyLabel.h; sourceTree = "<group>"; };
		ABA367A12DCB091D00E23A5C /* CopyLabel.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CopyLabel.m; sourceTree = "<group>"; };
		ABE8B6162DC1BCC0009C13D3 /* FaceAnalysisResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FaceAnalysisResultVC.h; sourceTree = "<group>"; };
		ABE8B6172DC1BCC0009C13D3 /* FaceAnalysisResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FaceAnalysisResultVC.m; sourceTree = "<group>"; };
		ABE8B6192DC1BFB5009C13D3 /* FaceAnalysisTabVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FaceAnalysisTabVC.h; sourceTree = "<group>"; };
		ABE8B61A2DC1BFB5009C13D3 /* FaceAnalysisTabVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FaceAnalysisTabVC.m; sourceTree = "<group>"; };
		B82C7FF7DD7EE20455599C0D /* Pods_TimeMachine_TimeMachineUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_TimeMachine_TimeMachineUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		BC0CF8C846A6428437E749E2 /* Pods-TimeMachineTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachineTests.debug.xcconfig"; path = "Target Support Files/Pods-TimeMachineTests/Pods-TimeMachineTests.debug.xcconfig"; sourceTree = "<group>"; };
		D4EE2D4F5B446E3291182DA2 /* Pods-TimeMachineTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachineTests.release.xcconfig"; path = "Target Support Files/Pods-TimeMachineTests/Pods-TimeMachineTests.release.xcconfig"; sourceTree = "<group>"; };
		EED86555F594696A0F39EE57 /* Pods-TimeMachine-TimeMachineUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TimeMachine-TimeMachineUITests.release.xcconfig"; path = "Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests.release.xcconfig"; sourceTree = "<group>"; };
		F307651826A7B25E00FF6708 /* ESBFWaitView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ESBFWaitView.m; sourceTree = "<group>"; };
		F307651926A7B25E00FF6708 /* RSA.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RSA.m; sourceTree = "<group>"; };
		F307651A26A7B25E00FF6708 /* ESBFWaitView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ESBFWaitView.h; sourceTree = "<group>"; };
		F307651B26A7B25E00FF6708 /* RSA.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RSA.h; sourceTree = "<group>"; };
		F307652226A7B26E00FF6708 /* UIView+Layout.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+Layout.m"; sourceTree = "<group>"; };
		F307652326A7B26E00FF6708 /* UIView+Layout.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+Layout.h"; sourceTree = "<group>"; };
		F307652C26A7B53E00FF6708 /* AFSecurityPolicy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFSecurityPolicy.h; sourceTree = "<group>"; };
		F307652D26A7B53E00FF6708 /* AFNetworkReachabilityManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFNetworkReachabilityManager.h; sourceTree = "<group>"; };
		F307652E26A7B53E00FF6708 /* AFURLSessionManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFURLSessionManager.h; sourceTree = "<group>"; };
		F307652F26A7B53E00FF6708 /* AFURLRequestSerialization.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFURLRequestSerialization.h; sourceTree = "<group>"; };
		F307653026A7B53E00FF6708 /* AFURLResponseSerialization.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFURLResponseSerialization.m; sourceTree = "<group>"; };
		F307653126A7B53E00FF6708 /* AFHTTPSessionManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFHTTPSessionManager.m; sourceTree = "<group>"; };
		F307653226A7B53E00FF6708 /* AFURLResponseSerialization.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFURLResponseSerialization.h; sourceTree = "<group>"; };
		F307653326A7B53E00FF6708 /* AFURLSessionManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFURLSessionManager.m; sourceTree = "<group>"; };
		F307653426A7B53E00FF6708 /* AFURLRequestSerialization.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFURLRequestSerialization.m; sourceTree = "<group>"; };
		F307653526A7B53E00FF6708 /* AFNetworking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFNetworking.h; sourceTree = "<group>"; };
		F307653626A7B53E00FF6708 /* AFNetworkReachabilityManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFNetworkReachabilityManager.m; sourceTree = "<group>"; };
		F307653726A7B53E00FF6708 /* AFSecurityPolicy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AFSecurityPolicy.m; sourceTree = "<group>"; };
		F307653826A7B53E00FF6708 /* AFCompatibilityMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFCompatibilityMacros.h; sourceTree = "<group>"; };
		F307653926A7B53E00FF6708 /* AFHTTPSessionManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AFHTTPSessionManager.h; sourceTree = "<group>"; };
		F307662C26A7B84100FF6708 /* UIImage+ExtendedCacheData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+ExtendedCacheData.h"; sourceTree = "<group>"; };
		F307662D26A7B84100FF6708 /* SDImageHEICCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageHEICCoder.m; sourceTree = "<group>"; };
		F307662E26A7B84100FF6708 /* SDAnimatedImageRep.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageRep.h; sourceTree = "<group>"; };
		F307662F26A7B84100FF6708 /* NSImage+Compatibility.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSImage+Compatibility.m"; sourceTree = "<group>"; };
		F307663026A7B84100FF6708 /* SDDiskCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDDiskCache.h; sourceTree = "<group>"; };
		F307663126A7B84100FF6708 /* SDAnimatedImageView+WebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "SDAnimatedImageView+WebCache.m"; sourceTree = "<group>"; };
		F307663226A7B84100FF6708 /* SDImageIOCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageIOCoder.h; sourceTree = "<group>"; };
		F307663326A7B84100FF6708 /* SDImageCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCoder.m; sourceTree = "<group>"; };
		F307663426A7B84100FF6708 /* NSButton+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSButton+WebCache.h"; sourceTree = "<group>"; };
		F307663526A7B84100FF6708 /* SDImageGraphics.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageGraphics.h; sourceTree = "<group>"; };
		F307663626A7B84100FF6708 /* SDWebImageOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOperation.m; sourceTree = "<group>"; };
		F307663726A7B84100FF6708 /* UIImageView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+WebCache.h"; sourceTree = "<group>"; };
		F307663826A7B84100FF6708 /* NSData+ImageContentType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSData+ImageContentType.h"; sourceTree = "<group>"; };
		F307663926A7B84100FF6708 /* SDWebImageDownloaderDecryptor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderDecryptor.m; sourceTree = "<group>"; };
		F307663A26A7B84100FF6708 /* SDImageTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageTransformer.h; sourceTree = "<group>"; };
		F307663B26A7B84100FF6708 /* SDImageCachesManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManager.h; sourceTree = "<group>"; };
		F307663C26A7B84100FF6708 /* SDWebImageTransition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransition.h; sourceTree = "<group>"; };
		F307663D26A7B84100FF6708 /* SDWebImageManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageManager.m; sourceTree = "<group>"; };
		F307663E26A7B84100FF6708 /* SDImageLoadersManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageLoadersManager.h; sourceTree = "<group>"; };
		F307663F26A7B84100FF6708 /* SDImageIOAnimatedCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageIOAnimatedCoder.m; sourceTree = "<group>"; };
		F307664026A7B84100FF6708 /* SDWebImageDownloaderOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderOperation.h; sourceTree = "<group>"; };
		F307664126A7B84100FF6708 /* UIImageView+HighlightedWebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+HighlightedWebCache.m"; sourceTree = "<group>"; };
		F307664226A7B84100FF6708 /* SDImageFrame.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageFrame.h; sourceTree = "<group>"; };
		F307664326A7B84100FF6708 /* SDImageGIFCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageGIFCoder.h; sourceTree = "<group>"; };
		F307664426A7B84100FF6708 /* SDImageCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCache.h; sourceTree = "<group>"; };
		F307664526A7B84100FF6708 /* SDWebImageDownloaderConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderConfig.h; sourceTree = "<group>"; };
		F307664626A7B84100FF6708 /* SDAnimatedImage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImage.m; sourceTree = "<group>"; };
		F307664726A7B84100FF6708 /* SDAnimatedImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageView.m; sourceTree = "<group>"; };
		F307664826A7B84100FF6708 /* UIImage+ForceDecode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ForceDecode.m"; sourceTree = "<group>"; };
		F307664926A7B84100FF6708 /* SDImageCacheConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCacheConfig.h; sourceTree = "<group>"; };
		F307664A26A7B84100FF6708 /* SDImageAWebPCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageAWebPCoder.m; sourceTree = "<group>"; };
		F307664B26A7B84100FF6708 /* SDWebImageCacheKeyFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheKeyFilter.h; sourceTree = "<group>"; };
		F307664C26A7B84100FF6708 /* SDAnimatedImagePlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImagePlayer.m; sourceTree = "<group>"; };
		F307664D26A7B84100FF6708 /* UIImage+Transform.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Transform.m"; sourceTree = "<group>"; };
		F307664E26A7B84100FF6708 /* UIImage+MemoryCacheCost.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+MemoryCacheCost.h"; sourceTree = "<group>"; };
		F307664F26A7B84100FF6708 /* SDWebImageDownloader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloader.m; sourceTree = "<group>"; };
		F307665026A7B84100FF6708 /* SDImageCacheDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCacheDefine.h; sourceTree = "<group>"; };
		F307665126A7B84100FF6708 /* SDImageLoader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageLoader.m; sourceTree = "<group>"; };
		F307665226A7B84100FF6708 /* SDWebImageCacheSerializer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheSerializer.m; sourceTree = "<group>"; };
		F307665326A7B84100FF6708 /* SDWebImageIndicator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageIndicator.m; sourceTree = "<group>"; };
		F307665426A7B84100FF6708 /* SDGraphicsImageRenderer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDGraphicsImageRenderer.m; sourceTree = "<group>"; };
		F307665526A7B84100FF6708 /* UIImage+GIF.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+GIF.m"; sourceTree = "<group>"; };
		F307665626A7B84100FF6708 /* SDImageCodersManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCodersManager.m; sourceTree = "<group>"; };
		F307665726A7B84100FF6708 /* UIButton+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIButton+WebCache.h"; sourceTree = "<group>"; };
		F307665826A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderRequestModifier.h; sourceTree = "<group>"; };
		F307665926A7B84100FF6708 /* UIImage+MultiFormat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MultiFormat.m"; sourceTree = "<group>"; };
		F307665A26A7B84100FF6708 /* SDMemoryCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDMemoryCache.m; sourceTree = "<group>"; };
		F307665B26A7B84100FF6708 /* UIImage+Metadata.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Metadata.h"; sourceTree = "<group>"; };
		F307665C26A7B84100FF6708 /* SDWebImageCompat.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCompat.m; sourceTree = "<group>"; };
		F307665D26A7B84100FF6708 /* SDImageAPNGCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageAPNGCoder.m; sourceTree = "<group>"; };
		F307665E26A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderResponseModifier.m; sourceTree = "<group>"; };
		F307665F26A7B84100FF6708 /* SDWebImageError.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageError.m; sourceTree = "<group>"; };
		F307666026A7B84100FF6708 /* SDWebImagePrefetcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImagePrefetcher.m; sourceTree = "<group>"; };
		F307666126A7B84100FF6708 /* SDImageCoderHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCoderHelper.m; sourceTree = "<group>"; };
		F307666226A7B84100FF6708 /* SDWebImageOptionsProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageOptionsProcessor.h; sourceTree = "<group>"; };
		F307666326A7B84100FF6708 /* UIView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCache.h"; sourceTree = "<group>"; };
		F307666426A7B84100FF6708 /* UIView+WebCacheOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIView+WebCacheOperation.h"; sourceTree = "<group>"; };
		F307666526A7B84100FF6708 /* SDWebImageDefine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDefine.h; sourceTree = "<group>"; };
		F307666626A7B84100FF6708 /* NSButton+WebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSButton+WebCache.m"; sourceTree = "<group>"; };
		F307666726A7B84100FF6708 /* SDImageCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCoder.h; sourceTree = "<group>"; };
		F307666826A7B84100FF6708 /* SDImageIOCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageIOCoder.m; sourceTree = "<group>"; };
		F307666926A7B84100FF6708 /* SDAnimatedImageView+WebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SDAnimatedImageView+WebCache.h"; sourceTree = "<group>"; };
		F307666A26A7B84100FF6708 /* SDDiskCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDDiskCache.m; sourceTree = "<group>"; };
		F307666B26A7B84100FF6708 /* NSImage+Compatibility.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSImage+Compatibility.h"; sourceTree = "<group>"; };
		F307666C26A7B84100FF6708 /* SDAnimatedImageRep.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAnimatedImageRep.m; sourceTree = "<group>"; };
		F307666D26A7B84100FF6708 /* SDImageHEICCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageHEICCoder.h; sourceTree = "<group>"; };
		F307666E26A7B84100FF6708 /* UIImage+ExtendedCacheData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+ExtendedCacheData.m"; sourceTree = "<group>"; };
		F307666F26A7B84100FF6708 /* SDAnimatedImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImageView.h; sourceTree = "<group>"; };
		F307667026A7B84100FF6708 /* SDWebImageDownloaderConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderConfig.m; sourceTree = "<group>"; };
		F307667126A7B84100FF6708 /* SDAnimatedImage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImage.h; sourceTree = "<group>"; };
		F307667226A7B84100FF6708 /* SDImageCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCache.m; sourceTree = "<group>"; };
		F307667326A7B84100FF6708 /* SDImageGIFCoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageGIFCoder.m; sourceTree = "<group>"; };
		F307667426A7B84100FF6708 /* UIImageView+HighlightedWebCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImageView+HighlightedWebCache.h"; sourceTree = "<group>"; };
		F307667526A7B84100FF6708 /* SDImageFrame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageFrame.m; sourceTree = "<group>"; };
		F307667626A7B84100FF6708 /* SDWebImageDownloaderOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderOperation.m; sourceTree = "<group>"; };
		F307667726A7B84100FF6708 /* SDImageIOAnimatedCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoder.h; sourceTree = "<group>"; };
		F307667826A7B84100FF6708 /* SDImageLoadersManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageLoadersManager.m; sourceTree = "<group>"; };
		F307667926A7B84100FF6708 /* SDImageCachesManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManager.m; sourceTree = "<group>"; };
		F307667A26A7B84100FF6708 /* SDWebImageTransition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageTransition.m; sourceTree = "<group>"; };
		F307667B26A7B84100FF6708 /* SDWebImageManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageManager.h; sourceTree = "<group>"; };
		F307667C26A7B84100FF6708 /* SDImageTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageTransformer.m; sourceTree = "<group>"; };
		F307667D26A7B84100FF6708 /* NSData+ImageContentType.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSData+ImageContentType.m"; sourceTree = "<group>"; };
		F307667E26A7B84100FF6708 /* UIImageView+WebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImageView+WebCache.m"; sourceTree = "<group>"; };
		F307667F26A7B84100FF6708 /* SDWebImageDownloaderDecryptor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderDecryptor.h; sourceTree = "<group>"; };
		F307668026A7B84100FF6708 /* SDWebImageOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageOperation.h; sourceTree = "<group>"; };
		F307668126A7B84100FF6708 /* SDImageGraphics.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageGraphics.m; sourceTree = "<group>"; };
		F307668226A7B84100FF6708 /* SDGraphicsImageRenderer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDGraphicsImageRenderer.h; sourceTree = "<group>"; };
		F307668326A7B84100FF6708 /* SDWebImageIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageIndicator.h; sourceTree = "<group>"; };
		F307668426A7B84100FF6708 /* SDWebImageCacheSerializer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCacheSerializer.h; sourceTree = "<group>"; };
		F307668526A7B84100FF6708 /* SDImageLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageLoader.h; sourceTree = "<group>"; };
		F307668626A7B84100FF6708 /* SDImageCacheDefine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheDefine.m; sourceTree = "<group>"; };
		F307668726A7B84100FF6708 /* SDWebImageDownloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloader.h; sourceTree = "<group>"; };
		F307668826A7B84100FF6708 /* UIImage+MemoryCacheCost.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+MemoryCacheCost.m"; sourceTree = "<group>"; };
		F307668926A7B84100FF6708 /* UIImage+Transform.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+Transform.h"; sourceTree = "<group>"; };
		F307668A26A7B84100FF6708 /* SDAnimatedImagePlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAnimatedImagePlayer.h; sourceTree = "<group>"; };
		F307668B26A7B84100FF6708 /* SDImageCacheConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCacheConfig.m; sourceTree = "<group>"; };
		F307668C26A7B84100FF6708 /* SDWebImageCacheKeyFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageCacheKeyFilter.m; sourceTree = "<group>"; };
		F307668D26A7B84100FF6708 /* SDImageAWebPCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAWebPCoder.h; sourceTree = "<group>"; };
		F307668E26A7B84100FF6708 /* UIImage+ForceDecode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+ForceDecode.h"; sourceTree = "<group>"; };
		F307668F26A7B84100FF6708 /* SDWebImageDefine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDefine.m; sourceTree = "<group>"; };
		F307669026A7B84100FF6708 /* SDImageCoderHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCoderHelper.h; sourceTree = "<group>"; };
		F307669126A7B84100FF6708 /* SDWebImageOptionsProcessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageOptionsProcessor.m; sourceTree = "<group>"; };
		F307669226A7B84100FF6708 /* UIView+WebCacheOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCacheOperation.m"; sourceTree = "<group>"; };
		F307669326A7B84100FF6708 /* UIView+WebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIView+WebCache.m"; sourceTree = "<group>"; };
		F307669426A7B84100FF6708 /* SDWebImagePrefetcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImagePrefetcher.h; sourceTree = "<group>"; };
		F307669526A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageDownloaderResponseModifier.h; sourceTree = "<group>"; };
		F307669626A7B84100FF6708 /* SDImageAPNGCoder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAPNGCoder.h; sourceTree = "<group>"; };
		F307669726A7B84100FF6708 /* SDWebImageError.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageError.h; sourceTree = "<group>"; };
		F307669826A7B84100FF6708 /* SDWebImageCompat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageCompat.h; sourceTree = "<group>"; };
		F307669926A7B84100FF6708 /* SDMemoryCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDMemoryCache.h; sourceTree = "<group>"; };
		F307669A26A7B84100FF6708 /* UIImage+Metadata.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+Metadata.m"; sourceTree = "<group>"; };
		F307669B26A7B84100FF6708 /* UIImage+MultiFormat.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+MultiFormat.h"; sourceTree = "<group>"; };
		F307669C26A7B84100FF6708 /* UIButton+WebCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIButton+WebCache.m"; sourceTree = "<group>"; };
		F307669D26A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWebImageDownloaderRequestModifier.m; sourceTree = "<group>"; };
		F307669E26A7B84100FF6708 /* SDImageCodersManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCodersManager.h; sourceTree = "<group>"; };
		F307669F26A7B84100FF6708 /* UIImage+GIF.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+GIF.h"; sourceTree = "<group>"; };
		F30766DF26A7B84100FF6708 /* SDDeviceHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDDeviceHelper.h; sourceTree = "<group>"; };
		F30766E026A7B84100FF6708 /* SDWeakProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWeakProxy.h; sourceTree = "<group>"; };
		F30766E126A7B84100FF6708 /* SDWebImageTransitionInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDWebImageTransitionInternal.h; sourceTree = "<group>"; };
		F30766E226A7B84100FF6708 /* UIColor+SDHexString.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIColor+SDHexString.m"; sourceTree = "<group>"; };
		F30766E326A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSBezierPath+SDRoundedCorners.h"; sourceTree = "<group>"; };
		F30766E426A7B84100FF6708 /* SDImageCachesManagerOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageCachesManagerOperation.m; sourceTree = "<group>"; };
		F30766E526A7B84100FF6708 /* SDFileAttributeHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDFileAttributeHelper.h; sourceTree = "<group>"; };
		F30766E626A7B84100FF6708 /* SDInternalMacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDInternalMacros.h; sourceTree = "<group>"; };
		F30766E726A7B84100FF6708 /* SDImageAssetManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDImageAssetManager.m; sourceTree = "<group>"; };
		F30766E826A7B84100FF6708 /* SDAsyncBlockOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAsyncBlockOperation.h; sourceTree = "<group>"; };
		F30766E926A7B84100FF6708 /* SDDisplayLink.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDDisplayLink.h; sourceTree = "<group>"; };
		F30766EA26A7B84100FF6708 /* SDAssociatedObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDAssociatedObject.h; sourceTree = "<group>"; };
		F30766EB26A7B84100FF6708 /* SDmetamacros.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDmetamacros.h; sourceTree = "<group>"; };
		F30766EC26A7B84100FF6708 /* SDImageIOAnimatedCoderInternal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageIOAnimatedCoderInternal.h; sourceTree = "<group>"; };
		F30766ED26A7B84100FF6708 /* SDDeviceHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDDeviceHelper.m; sourceTree = "<group>"; };
		F30766EE26A7B84100FF6708 /* SDWeakProxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDWeakProxy.m; sourceTree = "<group>"; };
		F30766EF26A7B84100FF6708 /* SDInternalMacros.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDInternalMacros.m; sourceTree = "<group>"; };
		F30766F026A7B84100FF6708 /* SDFileAttributeHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDFileAttributeHelper.m; sourceTree = "<group>"; };
		F30766F126A7B84100FF6708 /* SDImageCachesManagerOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageCachesManagerOperation.h; sourceTree = "<group>"; };
		F30766F226A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSBezierPath+SDRoundedCorners.m"; sourceTree = "<group>"; };
		F30766F326A7B84100FF6708 /* UIColor+SDHexString.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIColor+SDHexString.h"; sourceTree = "<group>"; };
		F30766F426A7B84100FF6708 /* SDDisplayLink.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDDisplayLink.m; sourceTree = "<group>"; };
		F30766F526A7B84100FF6708 /* SDImageAssetManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SDImageAssetManager.h; sourceTree = "<group>"; };
		F30766F626A7B84100FF6708 /* SDAsyncBlockOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAsyncBlockOperation.m; sourceTree = "<group>"; };
		F30766F726A7B84100FF6708 /* SDAssociatedObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SDAssociatedObject.m; sourceTree = "<group>"; };
		F307674626A7B96B00FF6708 /* SDWebImage.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SDWebImage.h; sourceTree = "<group>"; };
		F307675226A7CA2200FF6708 /* CheckImageHaveFace.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CheckImageHaveFace.h; sourceTree = "<group>"; };
		F307675326A7CA2200FF6708 /* CheckImageHaveFace.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CheckImageHaveFace.m; sourceTree = "<group>"; };
		F307676D26A80C0E00FF6708 /* ImageManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ImageManager.h; sourceTree = "<group>"; };
		F307676E26A80C0E00FF6708 /* ImageManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ImageManager.m; sourceTree = "<group>"; };
		F307677926A81CC700FF6708 /* FileManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FileManager.h; sourceTree = "<group>"; };
		F307677A26A81CC700FF6708 /* FileManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FileManager.m; sourceTree = "<group>"; };
		F307677F26A8201600FF6708 /* TimeTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = TimeTool.h; sourceTree = "<group>"; };
		F307678026A8201600FF6708 /* TimeTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeTool.m; sourceTree = "<group>"; };
		F307DA1F26A987F800E6C02D /* DrawManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DrawManager.h; sourceTree = "<group>"; };
		F307DA2026A987F800E6C02D /* DrawManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DrawManager.m; sourceTree = "<group>"; };
		F320396726CF507D008295B2 /* LanguageManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LanguageManager.m; sourceTree = "<group>"; };
		F320396826CF507F008295B2 /* LanguageManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LanguageManager.h; sourceTree = "<group>"; };
		F32048F626AFF19F00E0D893 /* VisionImageView.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VisionImageView.h; sourceTree = "<group>"; };
		F32048F726AFF19F00E0D893 /* VisionImageView.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VisionImageView.m; sourceTree = "<group>"; };
		F3286E5426A6D5E000C46DF1 /* PsychologicalViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PsychologicalViewController.h; sourceTree = "<group>"; };
		F3286E5526A6D5E000C46DF1 /* PsychologicalViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PsychologicalViewController.m; sourceTree = "<group>"; };
		F3286E5A26A6D66400C46DF1 /* PsychologicalResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = PsychologicalResultVC.h; sourceTree = "<group>"; };
		F3286E5B26A6D66400C46DF1 /* PsychologicalResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = PsychologicalResultVC.m; sourceTree = "<group>"; };
		F3286E6026A6D76700C46DF1 /* MagicViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MagicViewController.h; sourceTree = "<group>"; };
		F3286E6126A6D76700C46DF1 /* MagicViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MagicViewController.m; sourceTree = "<group>"; };
		F3286E6926A6D7BC00C46DF1 /* MagicResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MagicResultVC.h; sourceTree = "<group>"; };
		F33007F526BCC39800CA9E91 /* MagicChangeFaceView.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; path = MagicChangeFaceView.mm; sourceTree = "<group>"; };
		F33007F626BCC39A00CA9E91 /* MagicChangeFaceView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MagicChangeFaceView.h; sourceTree = "<group>"; };
		F3301A2F26BCDCC800CA9E91 /* MagicResultVC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MagicResultVC.m; sourceTree = "<group>"; };
		F3301A9226BD3AD200CA9E91 /* template1.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = template1.jpg; sourceTree = "<group>"; };
		F3301A9326BD3AD400CA9E91 /* template3.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = template3.jpg; sourceTree = "<group>"; };
		F3301A9426BD3AD600CA9E91 /* template2.jpg */ = {isa = PBXFileReference; lastKnownFileType = image.jpeg; path = template2.jpg; sourceTree = "<group>"; };
		F332A16B26CE283A00D4C1D9 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Main.strings; sourceTree = "<group>"; };
		F332A16C26CE283C00D4C1D9 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A16D26CE283F00D4C1D9 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A16E26CE284100D4C1D9 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F332A16F26CE2DC900D4C1D9 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Main.strings; sourceTree = "<group>"; };
		F332A17026CE2DCB00D4C1D9 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A17126CE2DCE00D4C1D9 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A17226CE2DD000D4C1D9 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F332A17326CE33C400D4C1D9 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Main.strings; sourceTree = "<group>"; };
		F332A17426CE33C700D4C1D9 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A17526CE33C900D4C1D9 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A17626CE33CC00D4C1D9 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F332A17726CE3B6500D4C1D9 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Main.strings; sourceTree = "<group>"; };
		F332A17826CE3B6700D4C1D9 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A17926CE3B6A00D4C1D9 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A17A26CE3B6D00D4C1D9 /* ko */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ko; path = ko.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F332A17B26CE46BA00D4C1D9 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Main.strings"; sourceTree = "<group>"; };
		F332A17C26CE46BD00D4C1D9 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/LaunchScreen.strings"; sourceTree = "<group>"; };
		F332A17D26CE46BF00D4C1D9 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		F332A17E26CE46C200D4C1D9 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		F332A17F26CE4F9300D4C1D9 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Main.strings; sourceTree = "<group>"; };
		F332A18026CE4F9600D4C1D9 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A18126CE4F9800D4C1D9 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A18226CE4F9B00D4C1D9 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F332A18326CE556D00D4C1D9 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Main.strings; sourceTree = "<group>"; };
		F332A18426CE556F00D4C1D9 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		F332A18526CE557200D4C1D9 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		F332A18626CE557400D4C1D9 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		F34A6BE226A69899006DE0E0 /* CartoonViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CartoonViewController.h; sourceTree = "<group>"; };
		F34A6BE326A69899006DE0E0 /* CartoonViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CartoonViewController.m; sourceTree = "<group>"; };
		F34A6BEB26A69D00006DE0E0 /* CartoonResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CartoonResultVC.h; sourceTree = "<group>"; };
		F34A6BEC26A69D00006DE0E0 /* CartoonResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CartoonResultVC.m; sourceTree = "<group>"; };
		F34A6C0A26A6A121006DE0E0 /* SVRadialGradientLayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVRadialGradientLayer.m; sourceTree = "<group>"; };
		F34A6C0B26A6A121006DE0E0 /* SVIndefiniteAnimatedView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVIndefiniteAnimatedView.h; sourceTree = "<group>"; };
		F34A6C0C26A6A121006DE0E0 /* SVProgressHUD.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVProgressHUD.h; sourceTree = "<group>"; };
		F34A6C0D26A6A121006DE0E0 /* SVProgressAnimatedView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVProgressAnimatedView.m; sourceTree = "<group>"; };
		F34A6C0E26A6A121006DE0E0 /* SVProgressHUD-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "SVProgressHUD-Prefix.pch"; sourceTree = "<group>"; };
		F34A6C0F26A6A121006DE0E0 /* SVRadialGradientLayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVRadialGradientLayer.h; sourceTree = "<group>"; };
		F34A6C1026A6A121006DE0E0 /* SVProgressHUD.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVProgressHUD.m; sourceTree = "<group>"; };
		F34A6C1126A6A121006DE0E0 /* SVIndefiniteAnimatedView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SVIndefiniteAnimatedView.m; sourceTree = "<group>"; };
		F34A6C1226A6A121006DE0E0 /* SVProgressHUD.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = SVProgressHUD.bundle; sourceTree = "<group>"; };
		F34A6C1326A6A121006DE0E0 /* SVProgressAnimatedView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SVProgressAnimatedView.h; sourceTree = "<group>"; };
		F34A6C1C26A6B534006DE0E0 /* UIImage+FixOrientation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UIImage+FixOrientation.h"; sourceTree = "<group>"; };
		F34A6C1D26A6B534006DE0E0 /* UIImage+FixOrientation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UIImage+FixOrientation.m"; sourceTree = "<group>"; };
		F34A6C2226A6BBDE006DE0E0 /* MyImagePickerResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyImagePickerResultVC.h; sourceTree = "<group>"; };
		F34A6C2326A6BBDE006DE0E0 /* MyImagePickerResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyImagePickerResultVC.m; sourceTree = "<group>"; };
		F350122A26E5F7FC00700B10 /* GPUImageBuffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageBuffer.h; sourceTree = "<group>"; };
		F350122B26E5F7FC00700B10 /* GPUImageTwoPassTextureSamplingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTwoPassTextureSamplingFilter.m; sourceTree = "<group>"; };
		F350122C26E5F7FC00700B10 /* GPUImageThreeInputFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageThreeInputFilter.h; sourceTree = "<group>"; };
		F350122D26E5F7FC00700B10 /* GPUImageFilterGroup.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFilterGroup.m; sourceTree = "<group>"; };
		F350122E26E5F7FC00700B10 /* GPUImageFourInputFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFourInputFilter.m; sourceTree = "<group>"; };
		F350122F26E5F7FC00700B10 /* GPUImageTwoPassFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTwoPassFilter.m; sourceTree = "<group>"; };
		F350123026E5F7FC00700B10 /* GPUImageTwoInputFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTwoInputFilter.m; sourceTree = "<group>"; };
		F350123226E5F7FC00700B10 /* GPUImageChromaKeyFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageChromaKeyFilter.m; sourceTree = "<group>"; };
		F350123326E5F7FC00700B10 /* GPUImageLuminanceThresholdFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLuminanceThresholdFilter.m; sourceTree = "<group>"; };
		F350123426E5F7FC00700B10 /* GPUImageContrastFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageContrastFilter.h; sourceTree = "<group>"; };
		F350123526E5F7FC00700B10 /* GPUImageHazeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHazeFilter.h; sourceTree = "<group>"; };
		F350123626E5F7FC00700B10 /* GPUImageSoftEleganceFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSoftEleganceFilter.m; sourceTree = "<group>"; };
		F350123726E5F7FC00700B10 /* GPUImageAverageColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAverageColor.h; sourceTree = "<group>"; };
		F350123826E5F7FC00700B10 /* GPUImageLuminosity.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLuminosity.h; sourceTree = "<group>"; };
		F350123926E5F7FC00700B10 /* GPUImageColorInvertFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorInvertFilter.m; sourceTree = "<group>"; };
		F350123A26E5F7FC00700B10 /* GPUImageGrayscaleFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGrayscaleFilter.m; sourceTree = "<group>"; };
		F350123B26E5F7FC00700B10 /* GPUImageLevelsFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLevelsFilter.m; sourceTree = "<group>"; };
		F350123C26E5F7FC00700B10 /* GPUImageSolarizeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSolarizeFilter.m; sourceTree = "<group>"; };
		F350123D26E5F7FC00700B10 /* GPUImageHistogramFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHistogramFilter.m; sourceTree = "<group>"; };
		F350123E26E5F7FC00700B10 /* GPUImageMissEtikateFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMissEtikateFilter.m; sourceTree = "<group>"; };
		F350123F26E5F7FC00700B10 /* GPUImageHistogramEqualizationFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHistogramEqualizationFilter.m; sourceTree = "<group>"; };
		F350124026E5F7FC00700B10 /* GPUImageHueFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHueFilter.m; sourceTree = "<group>"; };
		F350124126E5F7FC00700B10 /* GPUImageAmatorkaFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAmatorkaFilter.m; sourceTree = "<group>"; };
		F350124226E5F7FC00700B10 /* GPUImageMonochromeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMonochromeFilter.h; sourceTree = "<group>"; };
		F350124326E5F7FC00700B10 /* GPUImageHistogramGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHistogramGenerator.m; sourceTree = "<group>"; };
		F350124426E5F7FC00700B10 /* GPUImageWhiteBalanceFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageWhiteBalanceFilter.h; sourceTree = "<group>"; };
		F350124526E5F7FC00700B10 /* GPUImageGammaFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGammaFilter.m; sourceTree = "<group>"; };
		F350124626E5F7FC00700B10 /* GPUImageSolidColorGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSolidColorGenerator.m; sourceTree = "<group>"; };
		F350124726E5F7FC00700B10 /* GPUImageColorMatrixFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorMatrixFilter.m; sourceTree = "<group>"; };
		F350124826E5F7FC00700B10 /* GPUImageHighlightShadowFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHighlightShadowFilter.h; sourceTree = "<group>"; };
		F350124926E5F7FC00700B10 /* GPUImageAdaptiveThresholdFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAdaptiveThresholdFilter.m; sourceTree = "<group>"; };
		F350124A26E5F7FC00700B10 /* GPUImageSaturationFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSaturationFilter.m; sourceTree = "<group>"; };
		F350124B26E5F7FC00700B10 /* GPUImageOpacityFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageOpacityFilter.m; sourceTree = "<group>"; };
		F350124C26E5F7FC00700B10 /* GPUImageHSBFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHSBFilter.h; sourceTree = "<group>"; };
		F350124D26E5F7FC00700B10 /* GPUImageSepiaFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSepiaFilter.m; sourceTree = "<group>"; };
		F350124E26E5F7FC00700B10 /* GPUImageBrightnessFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageBrightnessFilter.m; sourceTree = "<group>"; };
		F350124F26E5F7FC00700B10 /* GPUImageAverageLuminanceThresholdFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAverageLuminanceThresholdFilter.m; sourceTree = "<group>"; };
		F350125026E5F7FC00700B10 /* GPUImageLookupFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLookupFilter.h; sourceTree = "<group>"; };
		F350125126E5F7FC00700B10 /* GPUImageRGBFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRGBFilter.m; sourceTree = "<group>"; };
		F350125226E5F7FC00700B10 /* GPUImageFalseColorFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFalseColorFilter.h; sourceTree = "<group>"; };
		F350125326E5F7FC00700B10 /* GPUImageLuminanceRangeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLuminanceRangeFilter.h; sourceTree = "<group>"; };
		F350125426E5F7FC00700B10 /* GPUImageExposureFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageExposureFilter.h; sourceTree = "<group>"; };
		F350125526E5F7FC00700B10 /* GPUImageToneCurveFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageToneCurveFilter.m; sourceTree = "<group>"; };
		F350125626E5F7FC00700B10 /* GPUImageColorInvertFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorInvertFilter.h; sourceTree = "<group>"; };
		F350125726E5F7FC00700B10 /* GPUImageGrayscaleFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGrayscaleFilter.h; sourceTree = "<group>"; };
		F350125826E5F7FC00700B10 /* GPUImageLuminosity.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLuminosity.m; sourceTree = "<group>"; };
		F350125926E5F7FC00700B10 /* GPUImageSoftEleganceFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSoftEleganceFilter.h; sourceTree = "<group>"; };
		F350125A26E5F7FC00700B10 /* GPUImageAverageColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAverageColor.m; sourceTree = "<group>"; };
		F350125B26E5F7FC00700B10 /* GPUImageContrastFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageContrastFilter.m; sourceTree = "<group>"; };
		F350125C26E5F7FC00700B10 /* GPUImageHazeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHazeFilter.m; sourceTree = "<group>"; };
		F350125D26E5F7FC00700B10 /* GPUImageLuminanceThresholdFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLuminanceThresholdFilter.h; sourceTree = "<group>"; };
		F350125E26E5F7FC00700B10 /* GPUImageChromaKeyFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageChromaKeyFilter.h; sourceTree = "<group>"; };
		F350125F26E5F7FC00700B10 /* GPUImageGammaFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGammaFilter.h; sourceTree = "<group>"; };
		F350126026E5F7FC00700B10 /* GPUImageWhiteBalanceFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageWhiteBalanceFilter.m; sourceTree = "<group>"; };
		F350126126E5F7FC00700B10 /* GPUImageHistogramGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHistogramGenerator.h; sourceTree = "<group>"; };
		F350126226E5F7FC00700B10 /* GPUImageMonochromeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMonochromeFilter.m; sourceTree = "<group>"; };
		F350126326E5F7FC00700B10 /* GPUImageAmatorkaFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAmatorkaFilter.h; sourceTree = "<group>"; };
		F350126426E5F7FC00700B10 /* GPUImageHueFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHueFilter.h; sourceTree = "<group>"; };
		F350126526E5F7FC00700B10 /* GPUImageHistogramEqualizationFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHistogramEqualizationFilter.h; sourceTree = "<group>"; };
		F350126626E5F7FC00700B10 /* GPUImageMissEtikateFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMissEtikateFilter.h; sourceTree = "<group>"; };
		F350126726E5F7FC00700B10 /* GPUImageHistogramFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHistogramFilter.h; sourceTree = "<group>"; };
		F350126826E5F7FC00700B10 /* GPUImageSolarizeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSolarizeFilter.h; sourceTree = "<group>"; };
		F350126926E5F7FC00700B10 /* GPUImageLevelsFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLevelsFilter.h; sourceTree = "<group>"; };
		F350126A26E5F7FC00700B10 /* GPUImageSepiaFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSepiaFilter.h; sourceTree = "<group>"; };
		F350126B26E5F7FC00700B10 /* GPUImageOpacityFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageOpacityFilter.h; sourceTree = "<group>"; };
		F350126C26E5F7FC00700B10 /* GPUImageHSBFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHSBFilter.m; sourceTree = "<group>"; };
		F350126D26E5F7FC00700B10 /* GPUImageSaturationFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSaturationFilter.h; sourceTree = "<group>"; };
		F350126E26E5F7FC00700B10 /* GPUImageAdaptiveThresholdFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAdaptiveThresholdFilter.h; sourceTree = "<group>"; };
		F350126F26E5F7FC00700B10 /* GPUImageHighlightShadowFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHighlightShadowFilter.m; sourceTree = "<group>"; };
		F350127026E5F7FC00700B10 /* GPUImageColorMatrixFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorMatrixFilter.h; sourceTree = "<group>"; };
		F350127126E5F7FC00700B10 /* GPUImageSolidColorGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSolidColorGenerator.h; sourceTree = "<group>"; };
		F350127226E5F7FC00700B10 /* GPUImageToneCurveFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageToneCurveFilter.h; sourceTree = "<group>"; };
		F350127326E5F7FC00700B10 /* GPUImageExposureFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageExposureFilter.m; sourceTree = "<group>"; };
		F350127426E5F7FC00700B10 /* GPUImageLuminanceRangeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLuminanceRangeFilter.m; sourceTree = "<group>"; };
		F350127526E5F7FC00700B10 /* GPUImageFalseColorFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFalseColorFilter.m; sourceTree = "<group>"; };
		F350127626E5F7FC00700B10 /* GPUImageRGBFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRGBFilter.h; sourceTree = "<group>"; };
		F350127726E5F7FC00700B10 /* GPUImageLookupFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLookupFilter.m; sourceTree = "<group>"; };
		F350127826E5F7FC00700B10 /* GPUImageAverageLuminanceThresholdFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAverageLuminanceThresholdFilter.h; sourceTree = "<group>"; };
		F350127926E5F7FC00700B10 /* GPUImageBrightnessFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageBrightnessFilter.h; sourceTree = "<group>"; };
		F350127A26E5F7FC00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTwoInputCrossTextureSamplingFilter.h; sourceTree = "<group>"; };
		F350127B26E5F7FC00700B10 /* GPUImage3x3TextureSamplingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImage3x3TextureSamplingFilter.m; sourceTree = "<group>"; };
		F350127C26E5F7FC00700B10 /* GPUImageFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFilter.h; sourceTree = "<group>"; };
		F350127D26E5F7FC00700B10 /* GPUImageTwoInputFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTwoInputFilter.h; sourceTree = "<group>"; };
		F350127F26E5F7FC00700B10 /* GPUImageUnsharpMaskFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageUnsharpMaskFilter.m; sourceTree = "<group>"; };
		F350128026E5F7FC00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageShiTomasiFeatureDetectionFilter.h; sourceTree = "<group>"; };
		F350128126E5F7FC00700B10 /* GPUImageThresholdEdgeDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageThresholdEdgeDetectionFilter.m; sourceTree = "<group>"; };
		F350128226E5F7FC00700B10 /* GPUImageColorLocalBinaryPatternFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorLocalBinaryPatternFilter.h; sourceTree = "<group>"; };
		F350128326E5F7FC00700B10 /* GPUImageParallelCoordinateLineTransformFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageParallelCoordinateLineTransformFilter.m; sourceTree = "<group>"; };
		F350128426E5F7FC00700B10 /* GPUImageSobelEdgeDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSobelEdgeDetectionFilter.m; sourceTree = "<group>"; };
		F350128526E5F7FC00700B10 /* GPUImageRGBDilationFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRGBDilationFilter.h; sourceTree = "<group>"; };
		F350128626E5F7FC00700B10 /* GPUImageHighPassFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHighPassFilter.m; sourceTree = "<group>"; };
		F350128726E5F7FC00700B10 /* GPUImageCrosshairGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageCrosshairGenerator.h; sourceTree = "<group>"; };
		F350128826E5F7FC00700B10 /* GPUImageGaussianBlurPositionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGaussianBlurPositionFilter.m; sourceTree = "<group>"; };
		F350128926E5F7FC00700B10 /* GPUImageColorPackingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorPackingFilter.m; sourceTree = "<group>"; };
		F350128A26E5F7FC00700B10 /* GPUImageWeakPixelInclusionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageWeakPixelInclusionFilter.m; sourceTree = "<group>"; };
		F350128B26E5F7FC00700B10 /* GPUImageNonMaximumSuppressionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageNonMaximumSuppressionFilter.m; sourceTree = "<group>"; };
		F350128C26E5F7FC00700B10 /* GPUImageGaussianSelectiveBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGaussianSelectiveBlurFilter.h; sourceTree = "<group>"; };
		F350128D26E5F7FC00700B10 /* GPUImageSharpenFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSharpenFilter.h; sourceTree = "<group>"; };
		F350128E26E5F7FC00700B10 /* GPUImageMotionBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMotionBlurFilter.h; sourceTree = "<group>"; };
		F350128F26E5F7FC00700B10 /* GPUImageCannyEdgeDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageCannyEdgeDetectionFilter.m; sourceTree = "<group>"; };
		F350129026E5F7FC00700B10 /* GPUImageFASTCornerDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFASTCornerDetectionFilter.m; sourceTree = "<group>"; };
		F350129126E5F7FC00700B10 /* GPUImageMedianFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMedianFilter.h; sourceTree = "<group>"; };
		F350129226E5F7FC00700B10 /* GPUImageLocalBinaryPatternFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLocalBinaryPatternFilter.h; sourceTree = "<group>"; };
		F350129326E5F7FC00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDirectionalNonMaximumSuppressionFilter.h; sourceTree = "<group>"; };
		F350129426E5F7FC00700B10 /* GPUImageiOSBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageiOSBlurFilter.m; sourceTree = "<group>"; };
		F350129526E5F7FC00700B10 /* GPUImageClosingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageClosingFilter.h; sourceTree = "<group>"; };
		F350129626E5F7FC00700B10 /* GPUImageTransformFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTransformFilter.m; sourceTree = "<group>"; };
		F350129726E5F7FC00700B10 /* GPUImageMotionDetector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMotionDetector.h; sourceTree = "<group>"; };
		F350129826E5F7FC00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageThresholdedNonMaximumSuppressionFilter.m; sourceTree = "<group>"; };
		F350129926E5F7FC00700B10 /* GPUImageHoughTransformLineDetector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHoughTransformLineDetector.h; sourceTree = "<group>"; };
		F350129A26E5F7FC00700B10 /* GPUImageBilateralFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageBilateralFilter.m; sourceTree = "<group>"; };
		F350129B26E5F7FC00700B10 /* GPUImageRGBErosionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRGBErosionFilter.h; sourceTree = "<group>"; };
		F350129C26E5F7FC00700B10 /* GPUImageLowPassFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLowPassFilter.h; sourceTree = "<group>"; };
		F350129D26E5F7FC00700B10 /* GPUImageRGBOpeningFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRGBOpeningFilter.h; sourceTree = "<group>"; };
		F350129E26E5F7FC00700B10 /* GPUImageSingleComponentGaussianBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSingleComponentGaussianBlurFilter.h; sourceTree = "<group>"; };
		F350129F26E5F7FC00700B10 /* GPUImageNobleCornerDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageNobleCornerDetectionFilter.h; sourceTree = "<group>"; };
		F35012A026E5F7FC00700B10 /* GPUImageHarrisCornerDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHarrisCornerDetectionFilter.m; sourceTree = "<group>"; };
		F35012A126E5F7FC00700B10 /* GPUImage3x3ConvolutionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImage3x3ConvolutionFilter.h; sourceTree = "<group>"; };
		F35012A226E5F7FC00700B10 /* GPUImageZoomBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageZoomBlurFilter.m; sourceTree = "<group>"; };
		F35012A326E5F7FC00700B10 /* GPUImageDilationFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDilationFilter.h; sourceTree = "<group>"; };
		F35012A426E5F7FC00700B10 /* GPUImageOpeningFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageOpeningFilter.m; sourceTree = "<group>"; };
		F35012A526E5F7FC00700B10 /* GPUImageErosionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageErosionFilter.m; sourceTree = "<group>"; };
		F35012A626E5F7FC00700B10 /* GPUImageCropFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageCropFilter.m; sourceTree = "<group>"; };
		F35012A726E5F7FC00700B10 /* GPUImageRGBClosingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRGBClosingFilter.m; sourceTree = "<group>"; };
		F35012A826E5F7FC00700B10 /* GPUImageLineGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLineGenerator.m; sourceTree = "<group>"; };
		F35012A926E5F7FC00700B10 /* GPUImagePrewittEdgeDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePrewittEdgeDetectionFilter.h; sourceTree = "<group>"; };
		F35012AA26E5F7FC00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDirectionalSobelEdgeDetectionFilter.m; sourceTree = "<group>"; };
		F35012AB26E5F7FC00700B10 /* GPUImageLanczosResamplingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLanczosResamplingFilter.m; sourceTree = "<group>"; };
		F35012AC26E5F7FC00700B10 /* GPUImageXYDerivativeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageXYDerivativeFilter.m; sourceTree = "<group>"; };
		F35012AD26E5F7FC00700B10 /* GPUImageColourFASTFeatureDetector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColourFASTFeatureDetector.h; sourceTree = "<group>"; };
		F35012AE26E5F7FC00700B10 /* GPUImageColourFASTSamplingOperation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColourFASTSamplingOperation.h; sourceTree = "<group>"; };
		F35012AF26E5F7FC00700B10 /* GPUImageLaplacianFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLaplacianFilter.m; sourceTree = "<group>"; };
		F35012B026E5F7FC00700B10 /* GPUImageBoxBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageBoxBlurFilter.m; sourceTree = "<group>"; };
		F35012B126E5F7FC00700B10 /* GPUImageTiltShiftFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTiltShiftFilter.m; sourceTree = "<group>"; };
		F35012B226E5F7FC00700B10 /* GPUImageGaussianBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGaussianBlurFilter.m; sourceTree = "<group>"; };
		F35012B326E5F7FC00700B10 /* GPUImageNonMaximumSuppressionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageNonMaximumSuppressionFilter.h; sourceTree = "<group>"; };
		F35012B426E5F7FC00700B10 /* GPUImageSharpenFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSharpenFilter.m; sourceTree = "<group>"; };
		F35012B526E5F7FC00700B10 /* GPUImageGaussianSelectiveBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGaussianSelectiveBlurFilter.m; sourceTree = "<group>"; };
		F35012B626E5F7FC00700B10 /* GPUImageWeakPixelInclusionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageWeakPixelInclusionFilter.h; sourceTree = "<group>"; };
		F35012B726E5F7FC00700B10 /* GPUImageColorPackingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorPackingFilter.h; sourceTree = "<group>"; };
		F35012B826E5F7FC00700B10 /* GPUImageGaussianBlurPositionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGaussianBlurPositionFilter.h; sourceTree = "<group>"; };
		F35012B926E5F7FC00700B10 /* GPUImageCrosshairGenerator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageCrosshairGenerator.m; sourceTree = "<group>"; };
		F35012BA26E5F7FC00700B10 /* GPUImageHighPassFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHighPassFilter.h; sourceTree = "<group>"; };
		F35012BB26E5F7FC00700B10 /* GPUImageSobelEdgeDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSobelEdgeDetectionFilter.h; sourceTree = "<group>"; };
		F35012BC26E5F7FC00700B10 /* GPUImageRGBDilationFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRGBDilationFilter.m; sourceTree = "<group>"; };
		F35012BD26E5F7FC00700B10 /* GPUImageParallelCoordinateLineTransformFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageParallelCoordinateLineTransformFilter.h; sourceTree = "<group>"; };
		F35012BE26E5F7FC00700B10 /* GPUImageColorLocalBinaryPatternFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorLocalBinaryPatternFilter.m; sourceTree = "<group>"; };
		F35012BF26E5F7FC00700B10 /* GPUImageThresholdEdgeDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageThresholdEdgeDetectionFilter.h; sourceTree = "<group>"; };
		F35012C026E5F7FC00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageShiTomasiFeatureDetectionFilter.m; sourceTree = "<group>"; };
		F35012C126E5F7FC00700B10 /* GPUImageUnsharpMaskFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageUnsharpMaskFilter.h; sourceTree = "<group>"; };
		F35012C226E5F7FC00700B10 /* GPUImageRGBOpeningFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRGBOpeningFilter.m; sourceTree = "<group>"; };
		F35012C326E5F7FC00700B10 /* GPUImageRGBErosionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRGBErosionFilter.m; sourceTree = "<group>"; };
		F35012C426E5F7FC00700B10 /* GPUImageBilateralFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageBilateralFilter.h; sourceTree = "<group>"; };
		F35012C526E5F7FC00700B10 /* GPUImageLowPassFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLowPassFilter.m; sourceTree = "<group>"; };
		F35012C626E5F7FC00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageThresholdedNonMaximumSuppressionFilter.h; sourceTree = "<group>"; };
		F35012C726E5F7FC00700B10 /* GPUImageHoughTransformLineDetector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHoughTransformLineDetector.m; sourceTree = "<group>"; };
		F35012C826E5F7FC00700B10 /* GPUImageTransformFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTransformFilter.h; sourceTree = "<group>"; };
		F35012C926E5F7FC00700B10 /* GPUImageMotionDetector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMotionDetector.m; sourceTree = "<group>"; };
		F35012CA26E5F7FC00700B10 /* GPUImageClosingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageClosingFilter.m; sourceTree = "<group>"; };
		F35012CB26E5F7FC00700B10 /* GPUImageiOSBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageiOSBlurFilter.h; sourceTree = "<group>"; };
		F35012CC26E5F7FC00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDirectionalNonMaximumSuppressionFilter.m; sourceTree = "<group>"; };
		F35012CD26E5F7FC00700B10 /* GPUImageMedianFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMedianFilter.m; sourceTree = "<group>"; };
		F35012CE26E5F7FC00700B10 /* GPUImageLocalBinaryPatternFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLocalBinaryPatternFilter.m; sourceTree = "<group>"; };
		F35012CF26E5F7FC00700B10 /* GPUImageFASTCornerDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFASTCornerDetectionFilter.h; sourceTree = "<group>"; };
		F35012D026E5F7FC00700B10 /* GPUImageMotionBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMotionBlurFilter.m; sourceTree = "<group>"; };
		F35012D126E5F7FC00700B10 /* GPUImageCannyEdgeDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageCannyEdgeDetectionFilter.h; sourceTree = "<group>"; };
		F35012D226E5F7FC00700B10 /* GPUImagePrewittEdgeDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePrewittEdgeDetectionFilter.m; sourceTree = "<group>"; };
		F35012D326E5F7FC00700B10 /* GPUImageLineGenerator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLineGenerator.h; sourceTree = "<group>"; };
		F35012D426E5F7FC00700B10 /* GPUImageRGBClosingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRGBClosingFilter.h; sourceTree = "<group>"; };
		F35012D526E5F7FC00700B10 /* GPUImageCropFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageCropFilter.h; sourceTree = "<group>"; };
		F35012D626E5F7FC00700B10 /* GPUImageErosionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageErosionFilter.h; sourceTree = "<group>"; };
		F35012D726E5F7FC00700B10 /* GPUImageOpeningFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageOpeningFilter.h; sourceTree = "<group>"; };
		F35012D826E5F7FC00700B10 /* GPUImageDilationFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDilationFilter.m; sourceTree = "<group>"; };
		F35012D926E5F7FC00700B10 /* GPUImageZoomBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageZoomBlurFilter.h; sourceTree = "<group>"; };
		F35012DA26E5F7FC00700B10 /* GPUImageHarrisCornerDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHarrisCornerDetectionFilter.h; sourceTree = "<group>"; };
		F35012DB26E5F7FC00700B10 /* GPUImage3x3ConvolutionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImage3x3ConvolutionFilter.m; sourceTree = "<group>"; };
		F35012DC26E5F7FC00700B10 /* GPUImageNobleCornerDetectionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageNobleCornerDetectionFilter.m; sourceTree = "<group>"; };
		F35012DD26E5F7FC00700B10 /* GPUImageSingleComponentGaussianBlurFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSingleComponentGaussianBlurFilter.m; sourceTree = "<group>"; };
		F35012DE26E5F7FC00700B10 /* GPUImageGaussianBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGaussianBlurFilter.h; sourceTree = "<group>"; };
		F35012DF26E5F7FC00700B10 /* GPUImageTiltShiftFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTiltShiftFilter.h; sourceTree = "<group>"; };
		F35012E026E5F7FC00700B10 /* GPUImageBoxBlurFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageBoxBlurFilter.h; sourceTree = "<group>"; };
		F35012E126E5F7FC00700B10 /* GPUImageColourFASTSamplingOperation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColourFASTSamplingOperation.m; sourceTree = "<group>"; };
		F35012E226E5F7FC00700B10 /* GPUImageLaplacianFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLaplacianFilter.h; sourceTree = "<group>"; };
		F35012E326E5F7FC00700B10 /* GPUImageColourFASTFeatureDetector.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColourFASTFeatureDetector.m; sourceTree = "<group>"; };
		F35012E426E5F7FC00700B10 /* GPUImageLanczosResamplingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLanczosResamplingFilter.h; sourceTree = "<group>"; };
		F35012E526E5F7FC00700B10 /* GPUImageXYDerivativeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageXYDerivativeFilter.h; sourceTree = "<group>"; };
		F35012E626E5F7FC00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDirectionalSobelEdgeDetectionFilter.h; sourceTree = "<group>"; };
		F35012E726E5F7FC00700B10 /* GPUImageTwoPassFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTwoPassFilter.h; sourceTree = "<group>"; };
		F35012E826E5F7FC00700B10 /* GPUImageFilterGroup.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFilterGroup.h; sourceTree = "<group>"; };
		F35012E926E5F7FC00700B10 /* GPUImageFourInputFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFourInputFilter.h; sourceTree = "<group>"; };
		F35012EA26E5F7FC00700B10 /* GPUImageThreeInputFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageThreeInputFilter.m; sourceTree = "<group>"; };
		F35012EB26E5F7FC00700B10 /* GPUImageBuffer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageBuffer.m; sourceTree = "<group>"; };
		F35012ED26E5F7FC00700B10 /* GPUImageColorBurnBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorBurnBlendFilter.m; sourceTree = "<group>"; };
		F35012EE26E5F7FC00700B10 /* GPUImageColorBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorBlendFilter.h; sourceTree = "<group>"; };
		F35012EF26E5F7FC00700B10 /* GPUImageSubtractBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSubtractBlendFilter.h; sourceTree = "<group>"; };
		F35012F026E5F7FC00700B10 /* GPUImageMaskFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMaskFilter.h; sourceTree = "<group>"; };
		F35012F126E5F7FC00700B10 /* GPUImageSoftLightBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSoftLightBlendFilter.h; sourceTree = "<group>"; };
		F35012F226E5F7FC00700B10 /* GPUImagePoissonBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePoissonBlendFilter.h; sourceTree = "<group>"; };
		F35012F326E5F7FC00700B10 /* GPUImageScreenBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageScreenBlendFilter.h; sourceTree = "<group>"; };
		F35012F426E5F7FC00700B10 /* GPUImageLinearBurnBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLinearBurnBlendFilter.h; sourceTree = "<group>"; };
		F35012F526E5F7FC00700B10 /* GPUImageAlphaBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAlphaBlendFilter.m; sourceTree = "<group>"; };
		F35012F626E5F7FC00700B10 /* GPUImageExclusionBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageExclusionBlendFilter.m; sourceTree = "<group>"; };
		F35012F726E5F7FC00700B10 /* GPUImageOverlayBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageOverlayBlendFilter.m; sourceTree = "<group>"; };
		F35012F826E5F7FC00700B10 /* GPUImageMultiplyBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMultiplyBlendFilter.m; sourceTree = "<group>"; };
		F35012F926E5F7FC00700B10 /* GPUImageHardLightBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHardLightBlendFilter.m; sourceTree = "<group>"; };
		F35012FA26E5F7FC00700B10 /* GPUImageNormalBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageNormalBlendFilter.h; sourceTree = "<group>"; };
		F35012FB26E5F7FC00700B10 /* GPUImageAddBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAddBlendFilter.h; sourceTree = "<group>"; };
		F35012FC26E5F7FC00700B10 /* GPUImageDissolveBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDissolveBlendFilter.m; sourceTree = "<group>"; };
		F35012FD26E5F7FC00700B10 /* GPUImageHueBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHueBlendFilter.h; sourceTree = "<group>"; };
		F35012FE26E5F7FC00700B10 /* GPUImageDarkenBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDarkenBlendFilter.h; sourceTree = "<group>"; };
		F35012FF26E5F7FC00700B10 /* GPUImageDifferenceBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDifferenceBlendFilter.m; sourceTree = "<group>"; };
		F350130026E5F7FC00700B10 /* GPUImageDivideBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDivideBlendFilter.m; sourceTree = "<group>"; };
		F350130126E5F7FC00700B10 /* GPUImageLightenBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLightenBlendFilter.h; sourceTree = "<group>"; };
		F350130226E5F7FC00700B10 /* GPUImageColorDodgeBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorDodgeBlendFilter.m; sourceTree = "<group>"; };
		F350130326E5F7FC00700B10 /* GPUImageSourceOverBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSourceOverBlendFilter.m; sourceTree = "<group>"; };
		F350130426E5F7FC00700B10 /* GPUImageChromaKeyBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageChromaKeyBlendFilter.h; sourceTree = "<group>"; };
		F350130526E5F7FC00700B10 /* GPUImageLuminosityBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageLuminosityBlendFilter.h; sourceTree = "<group>"; };
		F350130626E5F7FC00700B10 /* GPUImageSaturationBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSaturationBlendFilter.m; sourceTree = "<group>"; };
		F350130726E5F7FC00700B10 /* GPUImagePoissonBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePoissonBlendFilter.m; sourceTree = "<group>"; };
		F350130826E5F7FC00700B10 /* GPUImageMaskFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMaskFilter.m; sourceTree = "<group>"; };
		F350130926E5F7FC00700B10 /* GPUImageSoftLightBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSoftLightBlendFilter.m; sourceTree = "<group>"; };
		F350130A26E5F7FC00700B10 /* GPUImageSubtractBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSubtractBlendFilter.m; sourceTree = "<group>"; };
		F350130B26E5F7FC00700B10 /* GPUImageColorBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorBlendFilter.m; sourceTree = "<group>"; };
		F350130C26E5F7FC00700B10 /* GPUImageColorBurnBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorBurnBlendFilter.h; sourceTree = "<group>"; };
		F350130D26E5F7FD00700B10 /* GPUImageAlphaBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageAlphaBlendFilter.h; sourceTree = "<group>"; };
		F350130E26E5F7FD00700B10 /* GPUImageLinearBurnBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLinearBurnBlendFilter.m; sourceTree = "<group>"; };
		F350130F26E5F7FD00700B10 /* GPUImageScreenBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageScreenBlendFilter.m; sourceTree = "<group>"; };
		F350131026E5F7FD00700B10 /* GPUImageHueBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHueBlendFilter.m; sourceTree = "<group>"; };
		F350131126E5F7FD00700B10 /* GPUImageDissolveBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDissolveBlendFilter.h; sourceTree = "<group>"; };
		F350131226E5F7FD00700B10 /* GPUImageAddBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageAddBlendFilter.m; sourceTree = "<group>"; };
		F350131326E5F7FD00700B10 /* GPUImageNormalBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageNormalBlendFilter.m; sourceTree = "<group>"; };
		F350131426E5F7FD00700B10 /* GPUImageHardLightBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHardLightBlendFilter.h; sourceTree = "<group>"; };
		F350131526E5F7FD00700B10 /* GPUImageMultiplyBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMultiplyBlendFilter.h; sourceTree = "<group>"; };
		F350131626E5F7FD00700B10 /* GPUImageOverlayBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageOverlayBlendFilter.h; sourceTree = "<group>"; };
		F350131726E5F7FD00700B10 /* GPUImageExclusionBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageExclusionBlendFilter.h; sourceTree = "<group>"; };
		F350131826E5F7FD00700B10 /* GPUImageLuminosityBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLuminosityBlendFilter.m; sourceTree = "<group>"; };
		F350131926E5F7FD00700B10 /* GPUImageSaturationBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSaturationBlendFilter.h; sourceTree = "<group>"; };
		F350131A26E5F7FD00700B10 /* GPUImageChromaKeyBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageChromaKeyBlendFilter.m; sourceTree = "<group>"; };
		F350131B26E5F7FD00700B10 /* GPUImageSourceOverBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSourceOverBlendFilter.h; sourceTree = "<group>"; };
		F350131C26E5F7FD00700B10 /* GPUImageColorDodgeBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorDodgeBlendFilter.h; sourceTree = "<group>"; };
		F350131D26E5F7FD00700B10 /* GPUImageLightenBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageLightenBlendFilter.m; sourceTree = "<group>"; };
		F350131E26E5F7FD00700B10 /* GPUImageDifferenceBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDifferenceBlendFilter.h; sourceTree = "<group>"; };
		F350131F26E5F7FD00700B10 /* GPUImageDivideBlendFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageDivideBlendFilter.h; sourceTree = "<group>"; };
		F350132026E5F7FD00700B10 /* GPUImageDarkenBlendFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageDarkenBlendFilter.m; sourceTree = "<group>"; };
		F350132126E5F7FD00700B10 /* GPUImageTwoPassTextureSamplingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTwoPassTextureSamplingFilter.h; sourceTree = "<group>"; };
		F350132226E5F7FD00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTwoInputCrossTextureSamplingFilter.m; sourceTree = "<group>"; };
		F350132326E5F7FD00700B10 /* GPUImage3x3TextureSamplingFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImage3x3TextureSamplingFilter.h; sourceTree = "<group>"; };
		F350132526E5F7FD00700B10 /* GPUImageSmoothToonFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSmoothToonFilter.h; sourceTree = "<group>"; };
		F350132626E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageVoronoiConsumerFilter.h; sourceTree = "<group>"; };
		F350132726E5F7FD00700B10 /* GPUImageHalftoneFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageHalftoneFilter.h; sourceTree = "<group>"; };
		F350132826E5F7FD00700B10 /* GPUImageSwirlFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSwirlFilter.h; sourceTree = "<group>"; };
		F350132926E5F7FD00700B10 /* GPUImageEmbossFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageEmbossFilter.m; sourceTree = "<group>"; };
		F350132A26E5F7FD00700B10 /* GPUImageGlassSphereFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageGlassSphereFilter.h; sourceTree = "<group>"; };
		F350132B26E5F7FD00700B10 /* GPUImagePinchDistortionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePinchDistortionFilter.h; sourceTree = "<group>"; };
		F350132C26E5F7FD00700B10 /* GPUImagePixellatePositionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePixellatePositionFilter.m; sourceTree = "<group>"; };
		F350132D26E5F7FD00700B10 /* GPUImageVignetteFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageVignetteFilter.m; sourceTree = "<group>"; };
		F350132E26E5F7FD00700B10 /* GPUImageSketchFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSketchFilter.m; sourceTree = "<group>"; };
		F350132F26E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageJFAVoronoiFilter.m; sourceTree = "<group>"; };
		F350133026E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageKuwaharaRadius3Filter.m; sourceTree = "<group>"; };
		F350133126E5F7FD00700B10 /* GPUImageThresholdSketchFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageThresholdSketchFilter.h; sourceTree = "<group>"; };
		F350133226E5F7FD00700B10 /* GPUImageMosaicFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMosaicFilter.h; sourceTree = "<group>"; };
		F350133326E5F7FD00700B10 /* GPUImageKuwaharaFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageKuwaharaFilter.h; sourceTree = "<group>"; };
		F350133426E5F7FD00700B10 /* GPUImageCrosshatchFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageCrosshatchFilter.h; sourceTree = "<group>"; };
		F350133526E5F7FD00700B10 /* GPUImagePolarPixellateFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePolarPixellateFilter.m; sourceTree = "<group>"; };
		F350133626E5F7FD00700B10 /* GPUImageToonFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageToonFilter.m; sourceTree = "<group>"; };
		F350133726E5F7FD00700B10 /* GPUImagePixellateFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePixellateFilter.m; sourceTree = "<group>"; };
		F350133826E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePerlinNoiseFilter.h; sourceTree = "<group>"; };
		F350133926E5F7FD00700B10 /* GPUImageSphereRefractionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSphereRefractionFilter.m; sourceTree = "<group>"; };
		F350133A26E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageCGAColorspaceFilter.h; sourceTree = "<group>"; };
		F350133B26E5F7FD00700B10 /* GPUImageStretchDistortionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageStretchDistortionFilter.m; sourceTree = "<group>"; };
		F350133C26E5F7FD00700B10 /* GPUImagePosterizeFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePosterizeFilter.m; sourceTree = "<group>"; };
		F350133D26E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageBulgeDistortionFilter.h; sourceTree = "<group>"; };
		F350133E26E5F7FD00700B10 /* GPUImagePolkaDotFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePolkaDotFilter.h; sourceTree = "<group>"; };
		F350133F26E5F7FD00700B10 /* GPUImageEmbossFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageEmbossFilter.h; sourceTree = "<group>"; };
		F350134026E5F7FD00700B10 /* GPUImageSwirlFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSwirlFilter.m; sourceTree = "<group>"; };
		F350134126E5F7FD00700B10 /* GPUImageHalftoneFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageHalftoneFilter.m; sourceTree = "<group>"; };
		F350134226E5F7FD00700B10 /* GPUImageSmoothToonFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageSmoothToonFilter.m; sourceTree = "<group>"; };
		F350134326E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageVoronoiConsumerFilter.m; sourceTree = "<group>"; };
		F350134426E5F7FD00700B10 /* GPUImageThresholdSketchFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageThresholdSketchFilter.m; sourceTree = "<group>"; };
		F350134526E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageKuwaharaRadius3Filter.h; sourceTree = "<group>"; };
		F350134626E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageJFAVoronoiFilter.h; sourceTree = "<group>"; };
		F350134726E5F7FD00700B10 /* GPUImageSketchFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSketchFilter.h; sourceTree = "<group>"; };
		F350134826E5F7FD00700B10 /* GPUImageVignetteFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageVignetteFilter.h; sourceTree = "<group>"; };
		F350134926E5F7FD00700B10 /* GPUImagePixellatePositionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePixellatePositionFilter.h; sourceTree = "<group>"; };
		F350134A26E5F7FD00700B10 /* GPUImagePinchDistortionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePinchDistortionFilter.m; sourceTree = "<group>"; };
		F350134B26E5F7FD00700B10 /* GPUImageGlassSphereFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageGlassSphereFilter.m; sourceTree = "<group>"; };
		F350134C26E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePerlinNoiseFilter.m; sourceTree = "<group>"; };
		F350134D26E5F7FD00700B10 /* GPUImageSphereRefractionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageSphereRefractionFilter.h; sourceTree = "<group>"; };
		F350134E26E5F7FD00700B10 /* GPUImagePixellateFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePixellateFilter.h; sourceTree = "<group>"; };
		F350134F26E5F7FD00700B10 /* GPUImageToonFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageToonFilter.h; sourceTree = "<group>"; };
		F350135026E5F7FD00700B10 /* GPUImagePolarPixellateFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePolarPixellateFilter.h; sourceTree = "<group>"; };
		F350135126E5F7FD00700B10 /* GPUImageCrosshatchFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageCrosshatchFilter.m; sourceTree = "<group>"; };
		F350135226E5F7FD00700B10 /* GPUImageKuwaharaFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageKuwaharaFilter.m; sourceTree = "<group>"; };
		F350135326E5F7FD00700B10 /* GPUImageMosaicFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMosaicFilter.m; sourceTree = "<group>"; };
		F350135426E5F7FD00700B10 /* GPUImagePolkaDotFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePolkaDotFilter.m; sourceTree = "<group>"; };
		F350135526E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageBulgeDistortionFilter.m; sourceTree = "<group>"; };
		F350135626E5F7FD00700B10 /* GPUImagePosterizeFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePosterizeFilter.h; sourceTree = "<group>"; };
		F350135726E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageCGAColorspaceFilter.m; sourceTree = "<group>"; };
		F350135826E5F7FD00700B10 /* GPUImageStretchDistortionFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageStretchDistortionFilter.h; sourceTree = "<group>"; };
		F350135926E5F7FD00700B10 /* GPUImageFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFilter.m; sourceTree = "<group>"; };
		F350135B26E5F7FD00700B10 /* GPUImageFilterPipeline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFilterPipeline.m; sourceTree = "<group>"; };
		F350135C26E5F7FD00700B10 /* GPUImageFilterPipeline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFilterPipeline.h; sourceTree = "<group>"; };
		F350135D26E5F7FD00700B10 /* GPUImageContext.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageContext.m; sourceTree = "<group>"; };
		F350135E26E5F7FD00700B10 /* GLProgram.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GLProgram.m; sourceTree = "<group>"; };
		F350135F26E5F7FD00700B10 /* GPUImageFramebuffer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFramebuffer.m; sourceTree = "<group>"; };
		F350136026E5F7FD00700B10 /* GPUImageFramebufferCache.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageFramebufferCache.m; sourceTree = "<group>"; };
		F350136126E5F7FD00700B10 /* GPUImageContext.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageContext.h; sourceTree = "<group>"; };
		F350136326E5F7FD00700B10 /* GPUImageMovieComposition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMovieComposition.h; sourceTree = "<group>"; };
		F350136426E5F7FD00700B10 /* GPUImageColorConversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageColorConversion.h; sourceTree = "<group>"; };
		F350136526E5F7FD00700B10 /* GPUImageUIElement.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageUIElement.m; sourceTree = "<group>"; };
		F350136626E5F7FD00700B10 /* GPUImageMovie.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMovie.m; sourceTree = "<group>"; };
		F350136726E5F7FD00700B10 /* GPUImageRawDataInput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRawDataInput.h; sourceTree = "<group>"; };
		F350136826E5F7FD00700B10 /* GPUImagePicture.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImagePicture.m; sourceTree = "<group>"; };
		F350136926E5F7FD00700B10 /* GPUImageTextureInput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTextureInput.m; sourceTree = "<group>"; };
		F350136A26E5F7FD00700B10 /* GPUImageVideoCamera.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageVideoCamera.h; sourceTree = "<group>"; };
		F350136B26E5F7FD00700B10 /* GPUImageOutput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageOutput.m; sourceTree = "<group>"; };
		F350136C26E5F7FD00700B10 /* GPUImageStillCamera.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageStillCamera.h; sourceTree = "<group>"; };
		F350136D26E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "GPUImagePicture+TextureSubimage.m"; sourceTree = "<group>"; };
		F350136E26E5F7FD00700B10 /* GPUImageUIElement.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageUIElement.h; sourceTree = "<group>"; };
		F350136F26E5F7FD00700B10 /* GPUImageColorConversion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageColorConversion.m; sourceTree = "<group>"; };
		F350137026E5F7FD00700B10 /* GPUImageMovieComposition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMovieComposition.m; sourceTree = "<group>"; };
		F350137126E5F7FD00700B10 /* GPUImageRawDataInput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRawDataInput.m; sourceTree = "<group>"; };
		F350137226E5F7FD00700B10 /* GPUImageMovie.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMovie.h; sourceTree = "<group>"; };
		F350137326E5F7FD00700B10 /* GPUImageVideoCamera.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageVideoCamera.m; sourceTree = "<group>"; };
		F350137426E5F7FD00700B10 /* GPUImageTextureInput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTextureInput.h; sourceTree = "<group>"; };
		F350137526E5F7FD00700B10 /* GPUImagePicture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImagePicture.h; sourceTree = "<group>"; };
		F350137626E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "GPUImagePicture+TextureSubimage.h"; sourceTree = "<group>"; };
		F350137726E5F7FD00700B10 /* GPUImageStillCamera.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageStillCamera.m; sourceTree = "<group>"; };
		F350137826E5F7FD00700B10 /* GPUImageOutput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageOutput.h; sourceTree = "<group>"; };
		F350137926E5F7FD00700B10 /* GPUImageFramebufferCache.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFramebufferCache.h; sourceTree = "<group>"; };
		F350137B26E5F7FD00700B10 /* GPUImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageView.h; sourceTree = "<group>"; };
		F350137C26E5F7FD00700B10 /* GPUImageRawDataOutput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageRawDataOutput.h; sourceTree = "<group>"; };
		F350137D26E5F7FD00700B10 /* GPUImageMovieWriter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageMovieWriter.m; sourceTree = "<group>"; };
		F350137E26E5F7FD00700B10 /* GPUImageTextureOutput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageTextureOutput.h; sourceTree = "<group>"; };
		F350137F26E5F7FD00700B10 /* GPUImageRawDataOutput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageRawDataOutput.m; sourceTree = "<group>"; };
		F350138026E5F7FD00700B10 /* GPUImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageView.m; sourceTree = "<group>"; };
		F350138126E5F7FD00700B10 /* GPUImageMovieWriter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageMovieWriter.h; sourceTree = "<group>"; };
		F350138226E5F7FD00700B10 /* GPUImageTextureOutput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GPUImageTextureOutput.m; sourceTree = "<group>"; };
		F350138326E5F7FD00700B10 /* GPUImage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImage.h; sourceTree = "<group>"; };
		F350138426E5F7FD00700B10 /* GPUImageFramebuffer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GPUImageFramebuffer.h; sourceTree = "<group>"; };
		F350138526E5F7FD00700B10 /* GLProgram.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GLProgram.h; sourceTree = "<group>"; };
		F3581E8026A9043E001B2DC7 /* FMDatabase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMDatabase.h; sourceTree = "<group>"; };
		F3581E8126A9043E001B2DC7 /* FMDatabaseQueue.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FMDatabaseQueue.m; sourceTree = "<group>"; };
		F3581E8226A9043E001B2DC7 /* FMResultSet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMResultSet.h; sourceTree = "<group>"; };
		F3581E8326A9043E001B2DC7 /* FMDatabasePool.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMDatabasePool.h; sourceTree = "<group>"; };
		F3581E8426A9043E001B2DC7 /* FMDatabaseAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FMDatabaseAdditions.m; sourceTree = "<group>"; };
		F3581E8526A9043E001B2DC7 /* FMDatabase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FMDatabase.m; sourceTree = "<group>"; };
		F3581E8626A9043E001B2DC7 /* FMDatabaseQueue.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMDatabaseQueue.h; sourceTree = "<group>"; };
		F3581E8726A9043E001B2DC7 /* FMDB.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMDB.h; sourceTree = "<group>"; };
		F3581E8826A9043E001B2DC7 /* FMDatabaseAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FMDatabaseAdditions.h; sourceTree = "<group>"; };
		F3581E8926A9043E001B2DC7 /* FMDatabasePool.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FMDatabasePool.m; sourceTree = "<group>"; };
		F3581E8A26A9043E001B2DC7 /* FMResultSet.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FMResultSet.m; sourceTree = "<group>"; };
		F3581E9326A9045C001B2DC7 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		F3581E9826A90463001B2DC7 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		F3581E9D26A90683001B2DC7 /* DatabaseManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = DatabaseManager.h; sourceTree = "<group>"; };
		F3581E9E26A90683001B2DC7 /* DatabaseManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DatabaseManager.m; sourceTree = "<group>"; };
		F36207C526BBC82800E38EC6 /* shape_predictor_68_face_landmarks.dat */ = {isa = PBXFileReference; lastKnownFileType = file; path = shape_predictor_68_face_landmarks.dat; sourceTree = "<group>"; };
		F36210C926BBC9B600E38EC6 /* FacePointsTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FacePointsTool.h; sourceTree = "<group>"; };
		F36210CA26BBC9B600E38EC6 /* FacePointsTool.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = FacePointsTool.mm; sourceTree = "<group>"; };
		F362B2FE26BB877300E38EC6 /* lock.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = lock.png; sourceTree = "<group>"; };
		F362B30726BB880500E38EC6 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		F362B30926BB880F00E38EC6 /* AssetsLibrary.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AssetsLibrary.framework; path = System/Library/Frameworks/AssetsLibrary.framework; sourceTree = SDKROOT; };
		F362B30B26BB881500E38EC6 /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		F362D57D26BB8C5500E38EC6 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		F36B7FD026C383ED00A30090 /* libdlib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libdlib.a; sourceTree = "<group>"; };
		F37D4AB22697D96300A6740F /* TimeMachine.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TimeMachine.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F37D4AB52697D96300A6740F /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		F37D4AB62697D96300A6740F /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		F37D4AB82697D96300A6740F /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		F37D4AB92697D96300A6740F /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		F37D4ABF2697D96300A6740F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		F37D4AC12697D96300A6740F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F37D4AC42697D96300A6740F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		F37D4AC62697D96300A6740F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F37D4AC72697D96300A6740F /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		F37D4ACD2697D96300A6740F /* TimeMachineTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TimeMachineTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F37D4AD12697D96400A6740F /* TimeMachineTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeMachineTests.m; sourceTree = "<group>"; };
		F37D4AD32697D96400A6740F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F37D4AD82697D96400A6740F /* TimeMachineUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TimeMachineUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F37D4ADC2697D96400A6740F /* TimeMachineUITests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = TimeMachineUITests.m; sourceTree = "<group>"; };
		F37D4ADE2697D96400A6740F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		F37D4AEE2697D9A300A6740F /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		F37D4AF22697D9B200A6740F /* MASCompositeConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASCompositeConstraint.h; sourceTree = "<group>"; };
		F37D4AF32697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSLayoutConstraint+MASDebugAdditions.m"; sourceTree = "<group>"; };
		F37D4AF42697D9B200A6740F /* MASConstraint+Private.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "MASConstraint+Private.h"; sourceTree = "<group>"; };
		F37D4AF52697D9B200A6740F /* MASLayoutConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASLayoutConstraint.h; sourceTree = "<group>"; };
		F37D4AF62697D9B200A6740F /* NSArray+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		F37D4AF72697D9B200A6740F /* MASConstraintMaker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASConstraintMaker.h; sourceTree = "<group>"; };
		F37D4AF82697D9B200A6740F /* View+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "View+MASAdditions.h"; sourceTree = "<group>"; };
		F37D4AF92697D9B200A6740F /* NSArray+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+MASAdditions.h"; sourceTree = "<group>"; };
		F37D4AFA2697D9B200A6740F /* MASUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASUtilities.h; sourceTree = "<group>"; };
		F37D4AFB2697D9B200A6740F /* MASViewAttribute.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASViewAttribute.h; sourceTree = "<group>"; };
		F37D4AFC2697D9B200A6740F /* ViewController+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "ViewController+MASAdditions.m"; sourceTree = "<group>"; };
		F37D4AFD2697D9B200A6740F /* MASViewConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASViewConstraint.h; sourceTree = "<group>"; };
		F37D4AFE2697D9B200A6740F /* MASConstraint.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MASConstraint.h; sourceTree = "<group>"; };
		F37D4AFF2697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSLayoutConstraint+MASDebugAdditions.h"; sourceTree = "<group>"; };
		F37D4B002697D9B200A6740F /* MASCompositeConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASCompositeConstraint.m; sourceTree = "<group>"; };
		F37D4B012697D9B200A6740F /* MASConstraintMaker.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASConstraintMaker.m; sourceTree = "<group>"; };
		F37D4B022697D9B200A6740F /* MASLayoutConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASLayoutConstraint.m; sourceTree = "<group>"; };
		F37D4B032697D9B200A6740F /* NSArray+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+MASAdditions.m"; sourceTree = "<group>"; };
		F37D4B042697D9B200A6740F /* View+MASAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "View+MASAdditions.m"; sourceTree = "<group>"; };
		F37D4B052697D9B200A6740F /* View+MASShorthandAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "View+MASShorthandAdditions.h"; sourceTree = "<group>"; };
		F37D4B062697D9B200A6740F /* Masonry.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Masonry.h; sourceTree = "<group>"; };
		F37D4B072697D9B200A6740F /* MASConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASConstraint.m; sourceTree = "<group>"; };
		F37D4B082697D9B200A6740F /* ViewController+MASAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "ViewController+MASAdditions.h"; sourceTree = "<group>"; };
		F37D4B092697D9B200A6740F /* MASViewConstraint.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASViewConstraint.m; sourceTree = "<group>"; };
		F37D4B0A2697D9B200A6740F /* MASViewAttribute.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MASViewAttribute.m; sourceTree = "<group>"; };
		F37D4B192697D9C000A6740F /* MyColor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyColor.m; sourceTree = "<group>"; };
		F37D4B1A2697D9C000A6740F /* MyColor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyColor.h; sourceTree = "<group>"; };
		F37D4B222697D9EB00A6740F /* ViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		F37D4B232697D9EB00A6740F /* ViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		F37D4B282697DA0400A6740F /* HomeCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HomeCell.m; sourceTree = "<group>"; };
		F37D4B292697DA0400A6740F /* MyImagePickerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyImagePickerController.m; sourceTree = "<group>"; };
		F37D4B2A2697DA0400A6740F /* BaseViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = BaseViewController.m; sourceTree = "<group>"; };
		F37D4B2B2697DA0400A6740F /* MyImagePickerMaskView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyImagePickerMaskView.h; sourceTree = "<group>"; };
		F37D4B2C2697DA0400A6740F /* HomeCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HomeCell.h; sourceTree = "<group>"; };
		F37D4B2D2697DA0400A6740F /* MyImagePickerMaskView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyImagePickerMaskView.m; sourceTree = "<group>"; };
		F37D4B2E2697DA0400A6740F /* BaseViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = BaseViewController.h; sourceTree = "<group>"; };
		F37D4B2F2697DA0400A6740F /* MyImagePickerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyImagePickerController.h; sourceTree = "<group>"; };
		F37D4B392697DA3100A6740F /* YoungViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = YoungViewController.h; sourceTree = "<group>"; };
		F37D4B3A2697DA3100A6740F /* YoungViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = YoungViewController.m; sourceTree = "<group>"; };
		F37D4B402697DAA600A6740F /* PrefixHeader.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PrefixHeader.pch; sourceTree = "<group>"; };
		F37D4B5F2697F60300A6740F /* BabyViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BabyViewController.h; sourceTree = "<group>"; };
		F37D4B602697F60300A6740F /* BabyViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BabyViewController.m; sourceTree = "<group>"; };
		F37D4B6526981CE400A6740F /* BabyResultVC.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = BabyResultVC.h; sourceTree = "<group>"; };
		F37D4B6626981CE400A6740F /* BabyResultVC.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BabyResultVC.m; sourceTree = "<group>"; };
		F37D4B6B269826D600A6740F /* iPhoneXTool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = iPhoneXTool.h; sourceTree = "<group>"; };
		F37D4B6C269826D600A6740F /* iPhoneXTool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = iPhoneXTool.m; sourceTree = "<group>"; };
		F37D4B80269845DE00A6740F /* AgednessViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AgednessViewController.h; sourceTree = "<group>"; };
		F37D4B81269845DE00A6740F /* AgednessViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AgednessViewController.m; sourceTree = "<group>"; };
		F3BDC3DD26C4F55E0019A0F4 /* ImageTextButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImageTextButton.m; sourceTree = "<group>"; };
		F3BDC3DE26C4F5610019A0F4 /* ImageTextButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImageTextButton.h; sourceTree = "<group>"; };
		F3BDC3E026C4F58A0019A0F4 /* MyPageControl.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = MyPageControl.h; sourceTree = "<group>"; };
		F3BDC3E126C4F58A0019A0F4 /* MyPageControl.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = MyPageControl.m; sourceTree = "<group>"; };
		F3BDC3E426C5021F0019A0F4 /* TingFadeOutView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TingFadeOutView.h; sourceTree = "<group>"; };
		F3BDC3E526C5021F0019A0F4 /* TingFadeOutView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TingFadeOutView.m; sourceTree = "<group>"; };
		F3BDC3E726C502220019A0F4 /* TingSimpleScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TingSimpleScrollView.m; sourceTree = "<group>"; };
		F3BDC3E826C502220019A0F4 /* TingSimpleScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TingSimpleScrollView.h; sourceTree = "<group>"; };
		F3BDC3EA26C502240019A0F4 /* TingCycleScrollView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TingCycleScrollView.h; sourceTree = "<group>"; };
		F3BDC3EB26C502240019A0F4 /* TingCycleScrollView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TingCycleScrollView.m; sourceTree = "<group>"; };
		F3BDC3FA26C5027B0019A0F4 /* MyProgressView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MyProgressView.h; sourceTree = "<group>"; };
		F3BDC3FB26C5027B0019A0F4 /* MyProgressView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MyProgressView.m; sourceTree = "<group>"; };
		FB734F328401C6FCF513BFB2 /* Pods_TimeMachineTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_TimeMachineTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		F37D4AAF2697D96300A6740F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F33007FE26BCC42E00CA9E91 /* libc++.tbd in Frameworks */,
				F37D4AEF2697D9A300A6740F /* AVFoundation.framework in Frameworks */,
				F36B7FD126C383ED00A30090 /* libdlib.a in Frameworks */,
				F362B30C26BB881500E38EC6 /* Accelerate.framework in Frameworks */,
				F362B30A26BB880F00E38EC6 /* AssetsLibrary.framework in Frameworks */,
				F362B30826BB880500E38EC6 /* CoreMedia.framework in Frameworks */,
				F3581E9926A90463001B2DC7 /* libsqlite3.0.tbd in Frameworks */,
				F3581E9426A9045C001B2DC7 /* libsqlite3.tbd in Frameworks */,
				FDC18AAFF28F343E3EC78A4B /* Pods_TimeMachine.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4ACA2697D96300A6740F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				00ADBDD91314E13FE9E99739 /* Pods_TimeMachineTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4AD52697D96400A6740F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0D37E6A874457D820B2BC701 /* Pods_TimeMachine_TimeMachineUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		897AAEC4159148D8BBD33D36 /* Pods */ = {
			isa = PBXGroup;
			children = (
				028C48365ED9935D7D141E02 /* Pods-TimeMachine.debug.xcconfig */,
				83182782C7C73993F345BC3A /* Pods-TimeMachine.release.xcconfig */,
				6B503A42498A43226238314F /* Pods-TimeMachine-TimeMachineUITests.debug.xcconfig */,
				EED86555F594696A0F39EE57 /* Pods-TimeMachine-TimeMachineUITests.release.xcconfig */,
				BC0CF8C846A6428437E749E2 /* Pods-TimeMachineTests.debug.xcconfig */,
				D4EE2D4F5B446E3291182DA2 /* Pods-TimeMachineTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		A418CF8426A5622900AA67F8 /* StarView */ = {
			isa = PBXGroup;
			children = (
				A418CF8A26A5622900AA67F8 /* StarView.h */,
				A418CF8526A5622900AA67F8 /* StarsView.m */,
				A418CF8726A5622900AA67F8 /* StarsView.h */,
				A418CF8626A5622900AA67F8 /* StarView.m */,
				A418CF8826A5622900AA67F8 /* Star_highlight.png */,
				A418CF8926A5622900AA67F8 /* Star.png */,
			);
			path = StarView;
			sourceTree = "<group>";
		};
		A4686DD626A11F87005F7A87 /* Palms */ = {
			isa = PBXGroup;
			children = (
				A4686DD726A11FA9005F7A87 /* PalmsViewController.h */,
				A4686DD826A11FA9005F7A87 /* PalmsViewController.m */,
				A4686DDA26A12C77005F7A87 /* PalmsUnscrambleVC.h */,
				A4686DDB26A12C77005F7A87 /* PalmsUnscrambleVC.m */,
				A4686DDD26A163BB005F7A87 /* PalmsResultVC.h */,
				A4686DDE26A163BB005F7A87 /* PalmsResultVC.m */,
				A429D1C626AA5AD700E145FD /* PalmsResultCircleMaskView.h */,
				A429D1C726AA5AD700E145FD /* PalmsResultCircleMaskView.m */,
			);
			path = Palms;
			sourceTree = "<group>";
		};
		A4686DE026A18144005F7A87 /* OtherView */ = {
			isa = PBXGroup;
			children = (
				A5D3348A2A59132000F67392 /* IPChangeIconView.h */,
				A5D334892A59131F00F67392 /* IPChangeIconView.m */,
				A418CF7926A51C2F00AA67F8 /* AWPolygonView.h */,
				A418CF7A26A51C2F00AA67F8 /* AWPolygonView.m */,
				F32048F626AFF19F00E0D893 /* VisionImageView.h */,
				F32048F726AFF19F00E0D893 /* VisionImageView.m */,
				F3BDC3DE26C4F5610019A0F4 /* ImageTextButton.h */,
				F3BDC3DD26C4F55E0019A0F4 /* ImageTextButton.m */,
				F3BDC3E026C4F58A0019A0F4 /* MyPageControl.h */,
				F3BDC3E126C4F58A0019A0F4 /* MyPageControl.m */,
			);
			path = OtherView;
			sourceTree = "<group>";
		};
		A50B9D682C194F34002C4109 /* UpgradeVIP */ = {
			isa = PBXGroup;
			children = (
				A50B9D692C194F34002C4109 /* UpgradeVIPViewController.m */,
				A50B9D6A2C194F34002C4109 /* UpgradeVIPPayButton.m */,
				A50B9D6B2C194F34002C4109 /* APPStoreIAP */,
				A50B9D742C194F34002C4109 /* UpgradeVIPPayButton.h */,
				A50B9D752C194F34002C4109 /* UpgradeVIPViewController.h */,
			);
			path = UpgradeVIP;
			sourceTree = "<group>";
		};
		A50B9D6B2C194F34002C4109 /* APPStoreIAP */ = {
			isa = PBXGroup;
			children = (
				A50B9D6E2C194F34002C4109 /* APPMakeStoreIAPManager.h */,
				A50B9D722C194F34002C4109 /* APPMakeStoreIAPManager.m */,
				A50B9D702C194F34002C4109 /* APPStoreIAPObserver.h */,
				A50B9D6C2C194F34002C4109 /* APPStoreIAPObserver.m */,
				A50B9D6F2C194F34002C4109 /* NSDictionary+Value.h */,
				A50B9D732C194F34002C4109 /* NSDictionary+Value.m */,
				A50B9D712C194F34002C4109 /* NSString+Base64.h */,
				A50B9D6D2C194F34002C4109 /* NSString+Base64.m */,
			);
			path = APPStoreIAP;
			sourceTree = "<group>";
		};
		A50B9D7C2C1991F8002C4109 /* guide */ = {
			isa = PBXGroup;
			children = (
				A50B9D9C2C1991F8002C4109 /* BSEnterBaseController.h */,
				A50B9D802C1991F8002C4109 /* BSEnterBaseController.m */,
				A50B9D812C1991F8002C4109 /* BSEnterBaseController.xib */,
				A50B9D9A2C1991F8002C4109 /* BSLaunchScreenVC.h */,
				A50B9D832C1991F8002C4109 /* BSLaunchScreenVC.m */,
				A50B9D7D2C1991F8002C4109 /* BSLaunchScreenVC.xib */,
				A50B9D822C1991F8002C4109 /* BSUserDefaultManager.h */,
				A50B9D9B2C1991F8002C4109 /* BSUserDefaultManager.m */,
				A50B9D7E2C1991F8002C4109 /* EnterViewBackground_en.png */,
				A50B9D992C1991F8002C4109 /* EnterViewBackground.png */,
				A50B9D882C1991F8002C4109 /* IntroduceBackground01.png */,
				A50B9D862C1991F8002C4109 /* IntroduceBackground02.png */,
				A50B9D872C1991F8002C4109 /* IntroduceBackground03.png */,
				A50B9D842C1991F8002C4109 /* IntroduceBackground04.png */,
				A50B9D982C1991F8002C4109 /* UIView+BSAnimation.h */,
				A50B9D7F2C1991F8002C4109 /* UIView+BSAnimation.m */,
				A50B9D892C1991F8002C4109 /* view */,
			);
			path = guide;
			sourceTree = "<group>";
		};
		A50B9D892C1991F8002C4109 /* view */ = {
			isa = PBXGroup;
			children = (
				A50B9D972C1991F8002C4109 /* BSAgreementView.h */,
				A50B9D8F2C1991F8002C4109 /* BSAgreementView.m */,
				A50B9D932C1991F8002C4109 /* BSEtDetailsView.h */,
				A50B9D8D2C1991F8002C4109 /* BSEtDetailsView.m */,
				A50B9D962C1991F8002C4109 /* BSEtWebUrlView.h */,
				A50B9D902C1991F8002C4109 /* BSEtWebUrlView.m */,
				A50B9D8C2C1991F8002C4109 /* BSIntroduceView.h */,
				A50B9D952C1991F8002C4109 /* BSIntroduceView.m */,
				A50B9D942C1991F8002C4109 /* BSSubscribeRadio.h */,
				A50B9D8E2C1991F8002C4109 /* BSSubscribeRadio.m */,
				A50B9D912C1991F8002C4109 /* BSWaitingAnimationView.h */,
				A50B9D8B2C1991F8002C4109 /* BSWaitingAnimationView.m */,
				A50B9D922C1991F8002C4109 /* ProgressViewUserInfo.h */,
				A50B9D8A2C1991F8002C4109 /* ProgressViewUserInfo.m */,
			);
			path = view;
			sourceTree = "<group>";
		};
		A50C5E962D75A0E300A601FC /* year */ = {
			isa = PBXGroup;
			children = (
				A51C5C192D79A13D00266A15 /* TiDi.h */,
				A51C5C1A2D79A13D00266A15 /* TiDi.m */,
				A50C5E972D75A10900A601FC /* TMYearViewController.h */,
				A50C5E982D75A10900A601FC /* TMYearViewController.m */,
				A51C5B352D799D8300266A15 /* TMZhanbuController.h */,
				A51C5B362D799D8300266A15 /* TMZhanbuController.m */,
			);
			path = year;
			sourceTree = "<group>";
		};
		A51C5C3B2D79A29500266A15 /* LunarSwift */ = {
			isa = PBXGroup;
			children = (
				A51C5C1C2D79A29500266A15 /* DaYun.swift */,
				A51C5C1D2D79A29500266A15 /* EightChar.swift */,
				A51C5C1E2D79A29500266A15 /* Foto.swift */,
				A51C5C1F2D79A29500266A15 /* FotoFestival.swift */,
				A51C5C202D79A29500266A15 /* FotoUtil.swift */,
				A51C5C212D79A29500266A15 /* Fu.swift */,
				A51C5C222D79A29500266A15 /* Holiday.swift */,
				A51C5C232D79A29500266A15 /* HolidayUtil.swift */,
				A51C5C242D79A29500266A15 /* JieQi.swift */,
				A51C5C252D79A29500266A15 /* LiuNian.swift */,
				A51C5C262D79A29500266A15 /* LiuYue.swift */,
				A51C5C272D79A29500266A15 /* Lunar.swift */,
				A51C5C282D79A29500266A15 /* LunarMonth.swift */,
				A51C5C292D79A29500266A15 /* LunarTime.swift */,
				A51C5C2A2D79A29500266A15 /* LunarUtil.swift */,
				A51C5C2B2D79A29500266A15 /* LunarYear.swift */,
				A51C5C2C2D79A29500266A15 /* NineStar.swift */,
				A51C5C2D2D79A29500266A15 /* ShouXingUtil.swift */,
				A51C5C2E2D79A29500266A15 /* ShuJiu.swift */,
				A51C5C2F2D79A29500266A15 /* Solar.swift */,
				A51C5C302D79A29500266A15 /* SolarHalfYear.swift */,
				A51C5C312D79A29500266A15 /* SolarMonth.swift */,
				A51C5C322D79A29500266A15 /* SolarSeason.swift */,
				A51C5C332D79A29500266A15 /* SolarUtil.swift */,
				A51C5C342D79A29500266A15 /* SolarWeek.swift */,
				A51C5C352D79A29500266A15 /* SolarYear.swift */,
				A51C5C362D79A29500266A15 /* Tao.swift */,
				A51C5C372D79A29500266A15 /* TaoFestival.swift */,
				A51C5C382D79A29500266A15 /* TaoUtil.swift */,
				A51C5C392D79A29500266A15 /* XiaoYun.swift */,
				A51C5C3A2D79A29500266A15 /* Yun.swift */,
			);
			path = LunarSwift;
			sourceTree = "<group>";
		};
		A5D3345D2A59110700F67392 /* update */ = {
			isa = PBXGroup;
			children = (
				A5D334762A59110700F67392 /* CTDConfig.h */,
				A5D3345F2A59110700F67392 /* thrid */,
				A5D334712A59110700F67392 /* UIButton+Create.h */,
				A5D334662A59110700F67392 /* UIButton+Create.m */,
				A5D3346F2A59110700F67392 /* UIColor+Hex.h */,
				A5D334682A59110700F67392 /* UIColor+Hex.m */,
				A5D334652A59110700F67392 /* UIImageView+Create.h */,
				A5D3346D2A59110700F67392 /* UIImageView+Create.m */,
				A5D3346E2A59110700F67392 /* UILabel+createLabels.h */,
				A5D3345E2A59110700F67392 /* UILabel+createLabels.m */,
				A5D334692A59110700F67392 /* UIView+Extension.h */,
				A5D334722A59110700F67392 /* UIView+Extension.m */,
				A5D334672A59110700F67392 /* UpdateManager.h */,
				A5D334702A59110700F67392 /* UpdateManager.m */,
				A5D3346B2A59110700F67392 /* ZYEButton.h */,
				A5D334742A59110700F67392 /* ZYEButton.m */,
				A5D334732A59110700F67392 /* ZYELabel.h */,
				A5D3346C2A59110700F67392 /* ZYELabel.m */,
				A5D3346A2A59110700F67392 /* ZYEUpdateView.h */,
				A5D334752A59110700F67392 /* ZYEUpdateView.m */,
			);
			path = update;
			sourceTree = "<group>";
		};
		A5D3345F2A59110700F67392 /* thrid */ = {
			isa = PBXGroup;
			children = (
				A5D334602A59110700F67392 /* popView */,
			);
			path = thrid;
			sourceTree = "<group>";
		};
		A5D334602A59110700F67392 /* popView */ = {
			isa = PBXGroup;
			children = (
				A5D334612A59110700F67392 /* PopAnimationTool.m */,
				A5D334622A59110700F67392 /* PopView.m */,
				A5D334632A59110700F67392 /* PopAnimationTool.h */,
				A5D334642A59110700F67392 /* PopView.h */,
			);
			path = popView;
			sourceTree = "<group>";
		};
		A5D334822A59117800F67392 /* Reachability */ = {
			isa = PBXGroup;
			children = (
				A5D334832A59117800F67392 /* Reachability.m */,
				A5D334842A59117800F67392 /* Reachability.h */,
			);
			path = Reachability;
			sourceTree = "<group>";
		};
		A5DC04592CB76915006E281C /* faceVc */ = {
			isa = PBXGroup;
			children = (
				A5E87C572CBD12B40039716D /* face.json */,
				A5E87C552CBD127B0039716D /* face_en.json */,
				A5DC045A2CB7693F006E281C /* FacePalmController.h */,
				A5DC045B2CB7693F006E281C /* FacePalmController.m */,
				A5FDEB4E2CBCF52E00F3D63A /* FaceResultController.h */,
				A5FDEB4F2CBCF52E00F3D63A /* FaceResultController.m */,
			);
			path = faceVc;
			sourceTree = "<group>";
		};
		AB8DB0942DBE002B0052A79B /* v2.1 */ = {
			isa = PBXGroup;
			children = (
				AB8DB0972DBE05410052A79B /* MainTabbarVC.h */,
				AB8DB0982DBE05410052A79B /* MainTabbarVC.m */,
				AB8DB09A2DBE0A070052A79B /* MentalTestVC.h */,
				AB8DB09B2DBE0A070052A79B /* MentalTestVC.m */,
				AB8901B52DBF234700C17211 /* CardView.h */,
				AB8901B62DBF234700C17211 /* CardView.m */,
				AB8901B82DBF279C00C17211 /* CardSliderView.h */,
				AB8901B92DBF279C00C17211 /* CardSliderView.m */,
				AB8901BB2DBF758300C17211 /* AllFuncShowView.h */,
				AB8901BC2DBF758300C17211 /* AllFuncShowView.m */,
				AB9BE91D2DC0ABDD00D4CE8B /* FaceAnalysisVC.h */,
				AB9BE91E2DC0ABDD00D4CE8B /* FaceAnalysisVC.m */,
				ABE8B6162DC1BCC0009C13D3 /* FaceAnalysisResultVC.h */,
				ABE8B6172DC1BCC0009C13D3 /* FaceAnalysisResultVC.m */,
				ABE8B6192DC1BFB5009C13D3 /* FaceAnalysisTabVC.h */,
				ABE8B61A2DC1BFB5009C13D3 /* FaceAnalysisTabVC.m */,
				ABA367A02DCB091D00E23A5C /* CopyLabel.h */,
				ABA367A12DCB091D00E23A5C /* CopyLabel.m */,
			);
			path = v2.1;
			sourceTree = "<group>";
		};
		F307651726A7B25E00FF6708 /* ToolMethod */ = {
			isa = PBXGroup;
			children = (
				F307651826A7B25E00FF6708 /* ESBFWaitView.m */,
				F307651926A7B25E00FF6708 /* RSA.m */,
				F307651A26A7B25E00FF6708 /* ESBFWaitView.h */,
				F307651B26A7B25E00FF6708 /* RSA.h */,
			);
			path = ToolMethod;
			sourceTree = "<group>";
		};
		F307652126A7B26E00FF6708 /* Extend */ = {
			isa = PBXGroup;
			children = (
				F307652226A7B26E00FF6708 /* UIView+Layout.m */,
				F307652326A7B26E00FF6708 /* UIView+Layout.h */,
			);
			path = Extend;
			sourceTree = "<group>";
		};
		F307652B26A7B53E00FF6708 /* AFNetworking */ = {
			isa = PBXGroup;
			children = (
				F307652C26A7B53E00FF6708 /* AFSecurityPolicy.h */,
				F307652D26A7B53E00FF6708 /* AFNetworkReachabilityManager.h */,
				F307652E26A7B53E00FF6708 /* AFURLSessionManager.h */,
				F307652F26A7B53E00FF6708 /* AFURLRequestSerialization.h */,
				F307653026A7B53E00FF6708 /* AFURLResponseSerialization.m */,
				F307653126A7B53E00FF6708 /* AFHTTPSessionManager.m */,
				F307653226A7B53E00FF6708 /* AFURLResponseSerialization.h */,
				F307653326A7B53E00FF6708 /* AFURLSessionManager.m */,
				F307653426A7B53E00FF6708 /* AFURLRequestSerialization.m */,
				F307653526A7B53E00FF6708 /* AFNetworking.h */,
				F307653626A7B53E00FF6708 /* AFNetworkReachabilityManager.m */,
				F307653726A7B53E00FF6708 /* AFSecurityPolicy.m */,
				F307653826A7B53E00FF6708 /* AFCompatibilityMacros.h */,
				F307653926A7B53E00FF6708 /* AFHTTPSessionManager.h */,
			);
			path = AFNetworking;
			sourceTree = "<group>";
		};
		F307662A26A7B84100FF6708 /* SDWebImage */ = {
			isa = PBXGroup;
			children = (
				F307674626A7B96B00FF6708 /* SDWebImage.h */,
				F307662B26A7B84100FF6708 /* Core */,
				F30766DE26A7B84100FF6708 /* Private */,
			);
			path = SDWebImage;
			sourceTree = "<group>";
		};
		F307662B26A7B84100FF6708 /* Core */ = {
			isa = PBXGroup;
			children = (
				F307662C26A7B84100FF6708 /* UIImage+ExtendedCacheData.h */,
				F307662D26A7B84100FF6708 /* SDImageHEICCoder.m */,
				F307662E26A7B84100FF6708 /* SDAnimatedImageRep.h */,
				F307662F26A7B84100FF6708 /* NSImage+Compatibility.m */,
				F307663026A7B84100FF6708 /* SDDiskCache.h */,
				F307663126A7B84100FF6708 /* SDAnimatedImageView+WebCache.m */,
				F307663226A7B84100FF6708 /* SDImageIOCoder.h */,
				F307663326A7B84100FF6708 /* SDImageCoder.m */,
				F307663426A7B84100FF6708 /* NSButton+WebCache.h */,
				F307663526A7B84100FF6708 /* SDImageGraphics.h */,
				F307663626A7B84100FF6708 /* SDWebImageOperation.m */,
				F307663726A7B84100FF6708 /* UIImageView+WebCache.h */,
				F307663826A7B84100FF6708 /* NSData+ImageContentType.h */,
				F307663926A7B84100FF6708 /* SDWebImageDownloaderDecryptor.m */,
				F307663A26A7B84100FF6708 /* SDImageTransformer.h */,
				F307663B26A7B84100FF6708 /* SDImageCachesManager.h */,
				F307663C26A7B84100FF6708 /* SDWebImageTransition.h */,
				F307663D26A7B84100FF6708 /* SDWebImageManager.m */,
				F307663E26A7B84100FF6708 /* SDImageLoadersManager.h */,
				F307663F26A7B84100FF6708 /* SDImageIOAnimatedCoder.m */,
				F307664026A7B84100FF6708 /* SDWebImageDownloaderOperation.h */,
				F307664126A7B84100FF6708 /* UIImageView+HighlightedWebCache.m */,
				F307664226A7B84100FF6708 /* SDImageFrame.h */,
				F307664326A7B84100FF6708 /* SDImageGIFCoder.h */,
				F307664426A7B84100FF6708 /* SDImageCache.h */,
				F307664526A7B84100FF6708 /* SDWebImageDownloaderConfig.h */,
				F307664626A7B84100FF6708 /* SDAnimatedImage.m */,
				F307664726A7B84100FF6708 /* SDAnimatedImageView.m */,
				F307664826A7B84100FF6708 /* UIImage+ForceDecode.m */,
				F307664926A7B84100FF6708 /* SDImageCacheConfig.h */,
				F307664A26A7B84100FF6708 /* SDImageAWebPCoder.m */,
				F307664B26A7B84100FF6708 /* SDWebImageCacheKeyFilter.h */,
				F307664C26A7B84100FF6708 /* SDAnimatedImagePlayer.m */,
				F307664D26A7B84100FF6708 /* UIImage+Transform.m */,
				F307664E26A7B84100FF6708 /* UIImage+MemoryCacheCost.h */,
				F307664F26A7B84100FF6708 /* SDWebImageDownloader.m */,
				F307665026A7B84100FF6708 /* SDImageCacheDefine.h */,
				F307665126A7B84100FF6708 /* SDImageLoader.m */,
				F307665226A7B84100FF6708 /* SDWebImageCacheSerializer.m */,
				F307665326A7B84100FF6708 /* SDWebImageIndicator.m */,
				F307665426A7B84100FF6708 /* SDGraphicsImageRenderer.m */,
				F307665526A7B84100FF6708 /* UIImage+GIF.m */,
				F307665626A7B84100FF6708 /* SDImageCodersManager.m */,
				F307665726A7B84100FF6708 /* UIButton+WebCache.h */,
				F307665826A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.h */,
				F307665926A7B84100FF6708 /* UIImage+MultiFormat.m */,
				F307665A26A7B84100FF6708 /* SDMemoryCache.m */,
				F307665B26A7B84100FF6708 /* UIImage+Metadata.h */,
				F307665C26A7B84100FF6708 /* SDWebImageCompat.m */,
				F307665D26A7B84100FF6708 /* SDImageAPNGCoder.m */,
				F307665E26A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.m */,
				F307665F26A7B84100FF6708 /* SDWebImageError.m */,
				F307666026A7B84100FF6708 /* SDWebImagePrefetcher.m */,
				F307666126A7B84100FF6708 /* SDImageCoderHelper.m */,
				F307666226A7B84100FF6708 /* SDWebImageOptionsProcessor.h */,
				F307666326A7B84100FF6708 /* UIView+WebCache.h */,
				F307666426A7B84100FF6708 /* UIView+WebCacheOperation.h */,
				F307666526A7B84100FF6708 /* SDWebImageDefine.h */,
				F307666626A7B84100FF6708 /* NSButton+WebCache.m */,
				F307666726A7B84100FF6708 /* SDImageCoder.h */,
				F307666826A7B84100FF6708 /* SDImageIOCoder.m */,
				F307666926A7B84100FF6708 /* SDAnimatedImageView+WebCache.h */,
				F307666A26A7B84100FF6708 /* SDDiskCache.m */,
				F307666B26A7B84100FF6708 /* NSImage+Compatibility.h */,
				F307666C26A7B84100FF6708 /* SDAnimatedImageRep.m */,
				F307666D26A7B84100FF6708 /* SDImageHEICCoder.h */,
				F307666E26A7B84100FF6708 /* UIImage+ExtendedCacheData.m */,
				F307666F26A7B84100FF6708 /* SDAnimatedImageView.h */,
				F307667026A7B84100FF6708 /* SDWebImageDownloaderConfig.m */,
				F307667126A7B84100FF6708 /* SDAnimatedImage.h */,
				F307667226A7B84100FF6708 /* SDImageCache.m */,
				F307667326A7B84100FF6708 /* SDImageGIFCoder.m */,
				F307667426A7B84100FF6708 /* UIImageView+HighlightedWebCache.h */,
				F307667526A7B84100FF6708 /* SDImageFrame.m */,
				F307667626A7B84100FF6708 /* SDWebImageDownloaderOperation.m */,
				F307667726A7B84100FF6708 /* SDImageIOAnimatedCoder.h */,
				F307667826A7B84100FF6708 /* SDImageLoadersManager.m */,
				F307667926A7B84100FF6708 /* SDImageCachesManager.m */,
				F307667A26A7B84100FF6708 /* SDWebImageTransition.m */,
				F307667B26A7B84100FF6708 /* SDWebImageManager.h */,
				F307667C26A7B84100FF6708 /* SDImageTransformer.m */,
				F307667D26A7B84100FF6708 /* NSData+ImageContentType.m */,
				F307667E26A7B84100FF6708 /* UIImageView+WebCache.m */,
				F307667F26A7B84100FF6708 /* SDWebImageDownloaderDecryptor.h */,
				F307668026A7B84100FF6708 /* SDWebImageOperation.h */,
				F307668126A7B84100FF6708 /* SDImageGraphics.m */,
				F307668226A7B84100FF6708 /* SDGraphicsImageRenderer.h */,
				F307668326A7B84100FF6708 /* SDWebImageIndicator.h */,
				F307668426A7B84100FF6708 /* SDWebImageCacheSerializer.h */,
				F307668526A7B84100FF6708 /* SDImageLoader.h */,
				F307668626A7B84100FF6708 /* SDImageCacheDefine.m */,
				F307668726A7B84100FF6708 /* SDWebImageDownloader.h */,
				F307668826A7B84100FF6708 /* UIImage+MemoryCacheCost.m */,
				F307668926A7B84100FF6708 /* UIImage+Transform.h */,
				F307668A26A7B84100FF6708 /* SDAnimatedImagePlayer.h */,
				F307668B26A7B84100FF6708 /* SDImageCacheConfig.m */,
				F307668C26A7B84100FF6708 /* SDWebImageCacheKeyFilter.m */,
				F307668D26A7B84100FF6708 /* SDImageAWebPCoder.h */,
				F307668E26A7B84100FF6708 /* UIImage+ForceDecode.h */,
				F307668F26A7B84100FF6708 /* SDWebImageDefine.m */,
				F307669026A7B84100FF6708 /* SDImageCoderHelper.h */,
				F307669126A7B84100FF6708 /* SDWebImageOptionsProcessor.m */,
				F307669226A7B84100FF6708 /* UIView+WebCacheOperation.m */,
				F307669326A7B84100FF6708 /* UIView+WebCache.m */,
				F307669426A7B84100FF6708 /* SDWebImagePrefetcher.h */,
				F307669526A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.h */,
				F307669626A7B84100FF6708 /* SDImageAPNGCoder.h */,
				F307669726A7B84100FF6708 /* SDWebImageError.h */,
				F307669826A7B84100FF6708 /* SDWebImageCompat.h */,
				F307669926A7B84100FF6708 /* SDMemoryCache.h */,
				F307669A26A7B84100FF6708 /* UIImage+Metadata.m */,
				F307669B26A7B84100FF6708 /* UIImage+MultiFormat.h */,
				F307669C26A7B84100FF6708 /* UIButton+WebCache.m */,
				F307669D26A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.m */,
				F307669E26A7B84100FF6708 /* SDImageCodersManager.h */,
				F307669F26A7B84100FF6708 /* UIImage+GIF.h */,
			);
			path = Core;
			sourceTree = "<group>";
		};
		F30766DE26A7B84100FF6708 /* Private */ = {
			isa = PBXGroup;
			children = (
				F30766DF26A7B84100FF6708 /* SDDeviceHelper.h */,
				F30766E026A7B84100FF6708 /* SDWeakProxy.h */,
				F30766E126A7B84100FF6708 /* SDWebImageTransitionInternal.h */,
				F30766E226A7B84100FF6708 /* UIColor+SDHexString.m */,
				F30766E326A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.h */,
				F30766E426A7B84100FF6708 /* SDImageCachesManagerOperation.m */,
				F30766E526A7B84100FF6708 /* SDFileAttributeHelper.h */,
				F30766E626A7B84100FF6708 /* SDInternalMacros.h */,
				F30766E726A7B84100FF6708 /* SDImageAssetManager.m */,
				F30766E826A7B84100FF6708 /* SDAsyncBlockOperation.h */,
				F30766E926A7B84100FF6708 /* SDDisplayLink.h */,
				F30766EA26A7B84100FF6708 /* SDAssociatedObject.h */,
				F30766EB26A7B84100FF6708 /* SDmetamacros.h */,
				F30766EC26A7B84100FF6708 /* SDImageIOAnimatedCoderInternal.h */,
				F30766ED26A7B84100FF6708 /* SDDeviceHelper.m */,
				F30766EE26A7B84100FF6708 /* SDWeakProxy.m */,
				F30766EF26A7B84100FF6708 /* SDInternalMacros.m */,
				F30766F026A7B84100FF6708 /* SDFileAttributeHelper.m */,
				F30766F126A7B84100FF6708 /* SDImageCachesManagerOperation.h */,
				F30766F226A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.m */,
				F30766F326A7B84100FF6708 /* UIColor+SDHexString.h */,
				F30766F426A7B84100FF6708 /* SDDisplayLink.m */,
				F30766F526A7B84100FF6708 /* SDImageAssetManager.h */,
				F30766F626A7B84100FF6708 /* SDAsyncBlockOperation.m */,
				F30766F726A7B84100FF6708 /* SDAssociatedObject.m */,
			);
			path = Private;
			sourceTree = "<group>";
		};
		F3286E5226A6D58800C46DF1 /* Magic */ = {
			isa = PBXGroup;
			children = (
				F3286E6026A6D76700C46DF1 /* MagicViewController.h */,
				F3286E6126A6D76700C46DF1 /* MagicViewController.m */,
				F3286E6926A6D7BC00C46DF1 /* MagicResultVC.h */,
				F3301A2F26BCDCC800CA9E91 /* MagicResultVC.m */,
				F33007F626BCC39A00CA9E91 /* MagicChangeFaceView.h */,
				F33007F526BCC39800CA9E91 /* MagicChangeFaceView.mm */,
				F36210C926BBC9B600E38EC6 /* FacePointsTool.h */,
				F36210CA26BBC9B600E38EC6 /* FacePointsTool.mm */,
			);
			path = Magic;
			sourceTree = "<group>";
		};
		F3286E5326A6D5B000C46DF1 /* Psychological */ = {
			isa = PBXGroup;
			children = (
				A570F6292C103F9D00614266 /* 手相.json */,
				A50B9D662C194E1B002C4109 /* 手相_en.json */,
				F3286E5426A6D5E000C46DF1 /* PsychologicalViewController.h */,
				F3286E5526A6D5E000C46DF1 /* PsychologicalViewController.m */,
				F3286E5A26A6D66400C46DF1 /* PsychologicalResultVC.h */,
				F3286E5B26A6D66400C46DF1 /* PsychologicalResultVC.m */,
			);
			path = Psychological;
			sourceTree = "<group>";
		};
		F34A6BE126A69889006DE0E0 /* Cartoon */ = {
			isa = PBXGroup;
			children = (
				F34A6BE226A69899006DE0E0 /* CartoonViewController.h */,
				F34A6BE326A69899006DE0E0 /* CartoonViewController.m */,
				F34A6BEB26A69D00006DE0E0 /* CartoonResultVC.h */,
				F34A6BEC26A69D00006DE0E0 /* CartoonResultVC.m */,
			);
			path = Cartoon;
			sourceTree = "<group>";
		};
		F34A6C0926A6A121006DE0E0 /* SVProgressHUD */ = {
			isa = PBXGroup;
			children = (
				F34A6C0A26A6A121006DE0E0 /* SVRadialGradientLayer.m */,
				F34A6C0B26A6A121006DE0E0 /* SVIndefiniteAnimatedView.h */,
				F34A6C0C26A6A121006DE0E0 /* SVProgressHUD.h */,
				F34A6C0D26A6A121006DE0E0 /* SVProgressAnimatedView.m */,
				F34A6C0E26A6A121006DE0E0 /* SVProgressHUD-Prefix.pch */,
				F34A6C0F26A6A121006DE0E0 /* SVRadialGradientLayer.h */,
				F34A6C1026A6A121006DE0E0 /* SVProgressHUD.m */,
				F34A6C1126A6A121006DE0E0 /* SVIndefiniteAnimatedView.m */,
				F34A6C1226A6A121006DE0E0 /* SVProgressHUD.bundle */,
				F34A6C1326A6A121006DE0E0 /* SVProgressAnimatedView.h */,
			);
			path = SVProgressHUD;
			sourceTree = "<group>";
		};
		F350122826E5F7FC00700B10 /* GPUImage */ = {
			isa = PBXGroup;
			children = (
				F350122926E5F7FC00700B10 /* Filters */,
				F350135A26E5F7FD00700B10 /* Pipeline */,
				F350135D26E5F7FD00700B10 /* GPUImageContext.m */,
				F350135E26E5F7FD00700B10 /* GLProgram.m */,
				F350135F26E5F7FD00700B10 /* GPUImageFramebuffer.m */,
				F350136026E5F7FD00700B10 /* GPUImageFramebufferCache.m */,
				F350136126E5F7FD00700B10 /* GPUImageContext.h */,
				F350136226E5F7FD00700B10 /* Sources */,
				F350137926E5F7FD00700B10 /* GPUImageFramebufferCache.h */,
				F350137A26E5F7FD00700B10 /* Outputs */,
				F350138326E5F7FD00700B10 /* GPUImage.h */,
				F350138426E5F7FD00700B10 /* GPUImageFramebuffer.h */,
				F350138526E5F7FD00700B10 /* GLProgram.h */,
			);
			path = GPUImage;
			sourceTree = "<group>";
		};
		F350122926E5F7FC00700B10 /* Filters */ = {
			isa = PBXGroup;
			children = (
				F350122A26E5F7FC00700B10 /* GPUImageBuffer.h */,
				F350122B26E5F7FC00700B10 /* GPUImageTwoPassTextureSamplingFilter.m */,
				F350122C26E5F7FC00700B10 /* GPUImageThreeInputFilter.h */,
				F350122D26E5F7FC00700B10 /* GPUImageFilterGroup.m */,
				F350122E26E5F7FC00700B10 /* GPUImageFourInputFilter.m */,
				F350122F26E5F7FC00700B10 /* GPUImageTwoPassFilter.m */,
				F350123026E5F7FC00700B10 /* GPUImageTwoInputFilter.m */,
				F350123126E5F7FC00700B10 /* ColorProcessing */,
				F350127A26E5F7FC00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.h */,
				F350127B26E5F7FC00700B10 /* GPUImage3x3TextureSamplingFilter.m */,
				F350127C26E5F7FC00700B10 /* GPUImageFilter.h */,
				F350127D26E5F7FC00700B10 /* GPUImageTwoInputFilter.h */,
				F350127E26E5F7FC00700B10 /* ImageProcessing */,
				F35012E726E5F7FC00700B10 /* GPUImageTwoPassFilter.h */,
				F35012E826E5F7FC00700B10 /* GPUImageFilterGroup.h */,
				F35012E926E5F7FC00700B10 /* GPUImageFourInputFilter.h */,
				F35012EA26E5F7FC00700B10 /* GPUImageThreeInputFilter.m */,
				F35012EB26E5F7FC00700B10 /* GPUImageBuffer.m */,
				F35012EC26E5F7FC00700B10 /* Blends */,
				F350132126E5F7FD00700B10 /* GPUImageTwoPassTextureSamplingFilter.h */,
				F350132226E5F7FD00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.m */,
				F350132326E5F7FD00700B10 /* GPUImage3x3TextureSamplingFilter.h */,
				F350132426E5F7FD00700B10 /* Effects */,
				F350135926E5F7FD00700B10 /* GPUImageFilter.m */,
			);
			path = Filters;
			sourceTree = "<group>";
		};
		F350123126E5F7FC00700B10 /* ColorProcessing */ = {
			isa = PBXGroup;
			children = (
				F350123226E5F7FC00700B10 /* GPUImageChromaKeyFilter.m */,
				F350123326E5F7FC00700B10 /* GPUImageLuminanceThresholdFilter.m */,
				F350123426E5F7FC00700B10 /* GPUImageContrastFilter.h */,
				F350123526E5F7FC00700B10 /* GPUImageHazeFilter.h */,
				F350123626E5F7FC00700B10 /* GPUImageSoftEleganceFilter.m */,
				F350123726E5F7FC00700B10 /* GPUImageAverageColor.h */,
				F350123826E5F7FC00700B10 /* GPUImageLuminosity.h */,
				F350123926E5F7FC00700B10 /* GPUImageColorInvertFilter.m */,
				F350123A26E5F7FC00700B10 /* GPUImageGrayscaleFilter.m */,
				F350123B26E5F7FC00700B10 /* GPUImageLevelsFilter.m */,
				F350123C26E5F7FC00700B10 /* GPUImageSolarizeFilter.m */,
				F350123D26E5F7FC00700B10 /* GPUImageHistogramFilter.m */,
				F350123E26E5F7FC00700B10 /* GPUImageMissEtikateFilter.m */,
				F350123F26E5F7FC00700B10 /* GPUImageHistogramEqualizationFilter.m */,
				F350124026E5F7FC00700B10 /* GPUImageHueFilter.m */,
				F350124126E5F7FC00700B10 /* GPUImageAmatorkaFilter.m */,
				F350124226E5F7FC00700B10 /* GPUImageMonochromeFilter.h */,
				F350124326E5F7FC00700B10 /* GPUImageHistogramGenerator.m */,
				F350124426E5F7FC00700B10 /* GPUImageWhiteBalanceFilter.h */,
				F350124526E5F7FC00700B10 /* GPUImageGammaFilter.m */,
				F350124626E5F7FC00700B10 /* GPUImageSolidColorGenerator.m */,
				F350124726E5F7FC00700B10 /* GPUImageColorMatrixFilter.m */,
				F350124826E5F7FC00700B10 /* GPUImageHighlightShadowFilter.h */,
				F350124926E5F7FC00700B10 /* GPUImageAdaptiveThresholdFilter.m */,
				F350124A26E5F7FC00700B10 /* GPUImageSaturationFilter.m */,
				F350124B26E5F7FC00700B10 /* GPUImageOpacityFilter.m */,
				F350124C26E5F7FC00700B10 /* GPUImageHSBFilter.h */,
				F350124D26E5F7FC00700B10 /* GPUImageSepiaFilter.m */,
				F350124E26E5F7FC00700B10 /* GPUImageBrightnessFilter.m */,
				F350124F26E5F7FC00700B10 /* GPUImageAverageLuminanceThresholdFilter.m */,
				F350125026E5F7FC00700B10 /* GPUImageLookupFilter.h */,
				F350125126E5F7FC00700B10 /* GPUImageRGBFilter.m */,
				F350125226E5F7FC00700B10 /* GPUImageFalseColorFilter.h */,
				F350125326E5F7FC00700B10 /* GPUImageLuminanceRangeFilter.h */,
				F350125426E5F7FC00700B10 /* GPUImageExposureFilter.h */,
				F350125526E5F7FC00700B10 /* GPUImageToneCurveFilter.m */,
				F350125626E5F7FC00700B10 /* GPUImageColorInvertFilter.h */,
				F350125726E5F7FC00700B10 /* GPUImageGrayscaleFilter.h */,
				F350125826E5F7FC00700B10 /* GPUImageLuminosity.m */,
				F350125926E5F7FC00700B10 /* GPUImageSoftEleganceFilter.h */,
				F350125A26E5F7FC00700B10 /* GPUImageAverageColor.m */,
				F350125B26E5F7FC00700B10 /* GPUImageContrastFilter.m */,
				F350125C26E5F7FC00700B10 /* GPUImageHazeFilter.m */,
				F350125D26E5F7FC00700B10 /* GPUImageLuminanceThresholdFilter.h */,
				F350125E26E5F7FC00700B10 /* GPUImageChromaKeyFilter.h */,
				F350125F26E5F7FC00700B10 /* GPUImageGammaFilter.h */,
				F350126026E5F7FC00700B10 /* GPUImageWhiteBalanceFilter.m */,
				F350126126E5F7FC00700B10 /* GPUImageHistogramGenerator.h */,
				F350126226E5F7FC00700B10 /* GPUImageMonochromeFilter.m */,
				F350126326E5F7FC00700B10 /* GPUImageAmatorkaFilter.h */,
				F350126426E5F7FC00700B10 /* GPUImageHueFilter.h */,
				F350126526E5F7FC00700B10 /* GPUImageHistogramEqualizationFilter.h */,
				F350126626E5F7FC00700B10 /* GPUImageMissEtikateFilter.h */,
				F350126726E5F7FC00700B10 /* GPUImageHistogramFilter.h */,
				F350126826E5F7FC00700B10 /* GPUImageSolarizeFilter.h */,
				F350126926E5F7FC00700B10 /* GPUImageLevelsFilter.h */,
				F350126A26E5F7FC00700B10 /* GPUImageSepiaFilter.h */,
				F350126B26E5F7FC00700B10 /* GPUImageOpacityFilter.h */,
				F350126C26E5F7FC00700B10 /* GPUImageHSBFilter.m */,
				F350126D26E5F7FC00700B10 /* GPUImageSaturationFilter.h */,
				F350126E26E5F7FC00700B10 /* GPUImageAdaptiveThresholdFilter.h */,
				F350126F26E5F7FC00700B10 /* GPUImageHighlightShadowFilter.m */,
				F350127026E5F7FC00700B10 /* GPUImageColorMatrixFilter.h */,
				F350127126E5F7FC00700B10 /* GPUImageSolidColorGenerator.h */,
				F350127226E5F7FC00700B10 /* GPUImageToneCurveFilter.h */,
				F350127326E5F7FC00700B10 /* GPUImageExposureFilter.m */,
				F350127426E5F7FC00700B10 /* GPUImageLuminanceRangeFilter.m */,
				F350127526E5F7FC00700B10 /* GPUImageFalseColorFilter.m */,
				F350127626E5F7FC00700B10 /* GPUImageRGBFilter.h */,
				F350127726E5F7FC00700B10 /* GPUImageLookupFilter.m */,
				F350127826E5F7FC00700B10 /* GPUImageAverageLuminanceThresholdFilter.h */,
				F350127926E5F7FC00700B10 /* GPUImageBrightnessFilter.h */,
			);
			path = ColorProcessing;
			sourceTree = "<group>";
		};
		F350127E26E5F7FC00700B10 /* ImageProcessing */ = {
			isa = PBXGroup;
			children = (
				F350127F26E5F7FC00700B10 /* GPUImageUnsharpMaskFilter.m */,
				F350128026E5F7FC00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.h */,
				F350128126E5F7FC00700B10 /* GPUImageThresholdEdgeDetectionFilter.m */,
				F350128226E5F7FC00700B10 /* GPUImageColorLocalBinaryPatternFilter.h */,
				F350128326E5F7FC00700B10 /* GPUImageParallelCoordinateLineTransformFilter.m */,
				F350128426E5F7FC00700B10 /* GPUImageSobelEdgeDetectionFilter.m */,
				F350128526E5F7FC00700B10 /* GPUImageRGBDilationFilter.h */,
				F350128626E5F7FC00700B10 /* GPUImageHighPassFilter.m */,
				F350128726E5F7FC00700B10 /* GPUImageCrosshairGenerator.h */,
				F350128826E5F7FC00700B10 /* GPUImageGaussianBlurPositionFilter.m */,
				F350128926E5F7FC00700B10 /* GPUImageColorPackingFilter.m */,
				F350128A26E5F7FC00700B10 /* GPUImageWeakPixelInclusionFilter.m */,
				F350128B26E5F7FC00700B10 /* GPUImageNonMaximumSuppressionFilter.m */,
				F350128C26E5F7FC00700B10 /* GPUImageGaussianSelectiveBlurFilter.h */,
				F350128D26E5F7FC00700B10 /* GPUImageSharpenFilter.h */,
				F350128E26E5F7FC00700B10 /* GPUImageMotionBlurFilter.h */,
				F350128F26E5F7FC00700B10 /* GPUImageCannyEdgeDetectionFilter.m */,
				F350129026E5F7FC00700B10 /* GPUImageFASTCornerDetectionFilter.m */,
				F350129126E5F7FC00700B10 /* GPUImageMedianFilter.h */,
				F350129226E5F7FC00700B10 /* GPUImageLocalBinaryPatternFilter.h */,
				F350129326E5F7FC00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.h */,
				F350129426E5F7FC00700B10 /* GPUImageiOSBlurFilter.m */,
				F350129526E5F7FC00700B10 /* GPUImageClosingFilter.h */,
				F350129626E5F7FC00700B10 /* GPUImageTransformFilter.m */,
				F350129726E5F7FC00700B10 /* GPUImageMotionDetector.h */,
				F350129826E5F7FC00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.m */,
				F350129926E5F7FC00700B10 /* GPUImageHoughTransformLineDetector.h */,
				F350129A26E5F7FC00700B10 /* GPUImageBilateralFilter.m */,
				F350129B26E5F7FC00700B10 /* GPUImageRGBErosionFilter.h */,
				F350129C26E5F7FC00700B10 /* GPUImageLowPassFilter.h */,
				F350129D26E5F7FC00700B10 /* GPUImageRGBOpeningFilter.h */,
				F350129E26E5F7FC00700B10 /* GPUImageSingleComponentGaussianBlurFilter.h */,
				F350129F26E5F7FC00700B10 /* GPUImageNobleCornerDetectionFilter.h */,
				F35012A026E5F7FC00700B10 /* GPUImageHarrisCornerDetectionFilter.m */,
				F35012A126E5F7FC00700B10 /* GPUImage3x3ConvolutionFilter.h */,
				F35012A226E5F7FC00700B10 /* GPUImageZoomBlurFilter.m */,
				F35012A326E5F7FC00700B10 /* GPUImageDilationFilter.h */,
				F35012A426E5F7FC00700B10 /* GPUImageOpeningFilter.m */,
				F35012A526E5F7FC00700B10 /* GPUImageErosionFilter.m */,
				F35012A626E5F7FC00700B10 /* GPUImageCropFilter.m */,
				F35012A726E5F7FC00700B10 /* GPUImageRGBClosingFilter.m */,
				F35012A826E5F7FC00700B10 /* GPUImageLineGenerator.m */,
				F35012A926E5F7FC00700B10 /* GPUImagePrewittEdgeDetectionFilter.h */,
				F35012AA26E5F7FC00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.m */,
				F35012AB26E5F7FC00700B10 /* GPUImageLanczosResamplingFilter.m */,
				F35012AC26E5F7FC00700B10 /* GPUImageXYDerivativeFilter.m */,
				F35012AD26E5F7FC00700B10 /* GPUImageColourFASTFeatureDetector.h */,
				F35012AE26E5F7FC00700B10 /* GPUImageColourFASTSamplingOperation.h */,
				F35012AF26E5F7FC00700B10 /* GPUImageLaplacianFilter.m */,
				F35012B026E5F7FC00700B10 /* GPUImageBoxBlurFilter.m */,
				F35012B126E5F7FC00700B10 /* GPUImageTiltShiftFilter.m */,
				F35012B226E5F7FC00700B10 /* GPUImageGaussianBlurFilter.m */,
				F35012B326E5F7FC00700B10 /* GPUImageNonMaximumSuppressionFilter.h */,
				F35012B426E5F7FC00700B10 /* GPUImageSharpenFilter.m */,
				F35012B526E5F7FC00700B10 /* GPUImageGaussianSelectiveBlurFilter.m */,
				F35012B626E5F7FC00700B10 /* GPUImageWeakPixelInclusionFilter.h */,
				F35012B726E5F7FC00700B10 /* GPUImageColorPackingFilter.h */,
				F35012B826E5F7FC00700B10 /* GPUImageGaussianBlurPositionFilter.h */,
				F35012B926E5F7FC00700B10 /* GPUImageCrosshairGenerator.m */,
				F35012BA26E5F7FC00700B10 /* GPUImageHighPassFilter.h */,
				F35012BB26E5F7FC00700B10 /* GPUImageSobelEdgeDetectionFilter.h */,
				F35012BC26E5F7FC00700B10 /* GPUImageRGBDilationFilter.m */,
				F35012BD26E5F7FC00700B10 /* GPUImageParallelCoordinateLineTransformFilter.h */,
				F35012BE26E5F7FC00700B10 /* GPUImageColorLocalBinaryPatternFilter.m */,
				F35012BF26E5F7FC00700B10 /* GPUImageThresholdEdgeDetectionFilter.h */,
				F35012C026E5F7FC00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.m */,
				F35012C126E5F7FC00700B10 /* GPUImageUnsharpMaskFilter.h */,
				F35012C226E5F7FC00700B10 /* GPUImageRGBOpeningFilter.m */,
				F35012C326E5F7FC00700B10 /* GPUImageRGBErosionFilter.m */,
				F35012C426E5F7FC00700B10 /* GPUImageBilateralFilter.h */,
				F35012C526E5F7FC00700B10 /* GPUImageLowPassFilter.m */,
				F35012C626E5F7FC00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.h */,
				F35012C726E5F7FC00700B10 /* GPUImageHoughTransformLineDetector.m */,
				F35012C826E5F7FC00700B10 /* GPUImageTransformFilter.h */,
				F35012C926E5F7FC00700B10 /* GPUImageMotionDetector.m */,
				F35012CA26E5F7FC00700B10 /* GPUImageClosingFilter.m */,
				F35012CB26E5F7FC00700B10 /* GPUImageiOSBlurFilter.h */,
				F35012CC26E5F7FC00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.m */,
				F35012CD26E5F7FC00700B10 /* GPUImageMedianFilter.m */,
				F35012CE26E5F7FC00700B10 /* GPUImageLocalBinaryPatternFilter.m */,
				F35012CF26E5F7FC00700B10 /* GPUImageFASTCornerDetectionFilter.h */,
				F35012D026E5F7FC00700B10 /* GPUImageMotionBlurFilter.m */,
				F35012D126E5F7FC00700B10 /* GPUImageCannyEdgeDetectionFilter.h */,
				F35012D226E5F7FC00700B10 /* GPUImagePrewittEdgeDetectionFilter.m */,
				F35012D326E5F7FC00700B10 /* GPUImageLineGenerator.h */,
				F35012D426E5F7FC00700B10 /* GPUImageRGBClosingFilter.h */,
				F35012D526E5F7FC00700B10 /* GPUImageCropFilter.h */,
				F35012D626E5F7FC00700B10 /* GPUImageErosionFilter.h */,
				F35012D726E5F7FC00700B10 /* GPUImageOpeningFilter.h */,
				F35012D826E5F7FC00700B10 /* GPUImageDilationFilter.m */,
				F35012D926E5F7FC00700B10 /* GPUImageZoomBlurFilter.h */,
				F35012DA26E5F7FC00700B10 /* GPUImageHarrisCornerDetectionFilter.h */,
				F35012DB26E5F7FC00700B10 /* GPUImage3x3ConvolutionFilter.m */,
				F35012DC26E5F7FC00700B10 /* GPUImageNobleCornerDetectionFilter.m */,
				F35012DD26E5F7FC00700B10 /* GPUImageSingleComponentGaussianBlurFilter.m */,
				F35012DE26E5F7FC00700B10 /* GPUImageGaussianBlurFilter.h */,
				F35012DF26E5F7FC00700B10 /* GPUImageTiltShiftFilter.h */,
				F35012E026E5F7FC00700B10 /* GPUImageBoxBlurFilter.h */,
				F35012E126E5F7FC00700B10 /* GPUImageColourFASTSamplingOperation.m */,
				F35012E226E5F7FC00700B10 /* GPUImageLaplacianFilter.h */,
				F35012E326E5F7FC00700B10 /* GPUImageColourFASTFeatureDetector.m */,
				F35012E426E5F7FC00700B10 /* GPUImageLanczosResamplingFilter.h */,
				F35012E526E5F7FC00700B10 /* GPUImageXYDerivativeFilter.h */,
				F35012E626E5F7FC00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.h */,
			);
			path = ImageProcessing;
			sourceTree = "<group>";
		};
		F35012EC26E5F7FC00700B10 /* Blends */ = {
			isa = PBXGroup;
			children = (
				F35012ED26E5F7FC00700B10 /* GPUImageColorBurnBlendFilter.m */,
				F35012EE26E5F7FC00700B10 /* GPUImageColorBlendFilter.h */,
				F35012EF26E5F7FC00700B10 /* GPUImageSubtractBlendFilter.h */,
				F35012F026E5F7FC00700B10 /* GPUImageMaskFilter.h */,
				F35012F126E5F7FC00700B10 /* GPUImageSoftLightBlendFilter.h */,
				F35012F226E5F7FC00700B10 /* GPUImagePoissonBlendFilter.h */,
				F35012F326E5F7FC00700B10 /* GPUImageScreenBlendFilter.h */,
				F35012F426E5F7FC00700B10 /* GPUImageLinearBurnBlendFilter.h */,
				F35012F526E5F7FC00700B10 /* GPUImageAlphaBlendFilter.m */,
				F35012F626E5F7FC00700B10 /* GPUImageExclusionBlendFilter.m */,
				F35012F726E5F7FC00700B10 /* GPUImageOverlayBlendFilter.m */,
				F35012F826E5F7FC00700B10 /* GPUImageMultiplyBlendFilter.m */,
				F35012F926E5F7FC00700B10 /* GPUImageHardLightBlendFilter.m */,
				F35012FA26E5F7FC00700B10 /* GPUImageNormalBlendFilter.h */,
				F35012FB26E5F7FC00700B10 /* GPUImageAddBlendFilter.h */,
				F35012FC26E5F7FC00700B10 /* GPUImageDissolveBlendFilter.m */,
				F35012FD26E5F7FC00700B10 /* GPUImageHueBlendFilter.h */,
				F35012FE26E5F7FC00700B10 /* GPUImageDarkenBlendFilter.h */,
				F35012FF26E5F7FC00700B10 /* GPUImageDifferenceBlendFilter.m */,
				F350130026E5F7FC00700B10 /* GPUImageDivideBlendFilter.m */,
				F350130126E5F7FC00700B10 /* GPUImageLightenBlendFilter.h */,
				F350130226E5F7FC00700B10 /* GPUImageColorDodgeBlendFilter.m */,
				F350130326E5F7FC00700B10 /* GPUImageSourceOverBlendFilter.m */,
				F350130426E5F7FC00700B10 /* GPUImageChromaKeyBlendFilter.h */,
				F350130526E5F7FC00700B10 /* GPUImageLuminosityBlendFilter.h */,
				F350130626E5F7FC00700B10 /* GPUImageSaturationBlendFilter.m */,
				F350130726E5F7FC00700B10 /* GPUImagePoissonBlendFilter.m */,
				F350130826E5F7FC00700B10 /* GPUImageMaskFilter.m */,
				F350130926E5F7FC00700B10 /* GPUImageSoftLightBlendFilter.m */,
				F350130A26E5F7FC00700B10 /* GPUImageSubtractBlendFilter.m */,
				F350130B26E5F7FC00700B10 /* GPUImageColorBlendFilter.m */,
				F350130C26E5F7FC00700B10 /* GPUImageColorBurnBlendFilter.h */,
				F350130D26E5F7FD00700B10 /* GPUImageAlphaBlendFilter.h */,
				F350130E26E5F7FD00700B10 /* GPUImageLinearBurnBlendFilter.m */,
				F350130F26E5F7FD00700B10 /* GPUImageScreenBlendFilter.m */,
				F350131026E5F7FD00700B10 /* GPUImageHueBlendFilter.m */,
				F350131126E5F7FD00700B10 /* GPUImageDissolveBlendFilter.h */,
				F350131226E5F7FD00700B10 /* GPUImageAddBlendFilter.m */,
				F350131326E5F7FD00700B10 /* GPUImageNormalBlendFilter.m */,
				F350131426E5F7FD00700B10 /* GPUImageHardLightBlendFilter.h */,
				F350131526E5F7FD00700B10 /* GPUImageMultiplyBlendFilter.h */,
				F350131626E5F7FD00700B10 /* GPUImageOverlayBlendFilter.h */,
				F350131726E5F7FD00700B10 /* GPUImageExclusionBlendFilter.h */,
				F350131826E5F7FD00700B10 /* GPUImageLuminosityBlendFilter.m */,
				F350131926E5F7FD00700B10 /* GPUImageSaturationBlendFilter.h */,
				F350131A26E5F7FD00700B10 /* GPUImageChromaKeyBlendFilter.m */,
				F350131B26E5F7FD00700B10 /* GPUImageSourceOverBlendFilter.h */,
				F350131C26E5F7FD00700B10 /* GPUImageColorDodgeBlendFilter.h */,
				F350131D26E5F7FD00700B10 /* GPUImageLightenBlendFilter.m */,
				F350131E26E5F7FD00700B10 /* GPUImageDifferenceBlendFilter.h */,
				F350131F26E5F7FD00700B10 /* GPUImageDivideBlendFilter.h */,
				F350132026E5F7FD00700B10 /* GPUImageDarkenBlendFilter.m */,
			);
			path = Blends;
			sourceTree = "<group>";
		};
		F350132426E5F7FD00700B10 /* Effects */ = {
			isa = PBXGroup;
			children = (
				F350132526E5F7FD00700B10 /* GPUImageSmoothToonFilter.h */,
				F350132626E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.h */,
				F350132726E5F7FD00700B10 /* GPUImageHalftoneFilter.h */,
				F350132826E5F7FD00700B10 /* GPUImageSwirlFilter.h */,
				F350132926E5F7FD00700B10 /* GPUImageEmbossFilter.m */,
				F350132A26E5F7FD00700B10 /* GPUImageGlassSphereFilter.h */,
				F350132B26E5F7FD00700B10 /* GPUImagePinchDistortionFilter.h */,
				F350132C26E5F7FD00700B10 /* GPUImagePixellatePositionFilter.m */,
				F350132D26E5F7FD00700B10 /* GPUImageVignetteFilter.m */,
				F350132E26E5F7FD00700B10 /* GPUImageSketchFilter.m */,
				F350132F26E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.m */,
				F350133026E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.m */,
				F350133126E5F7FD00700B10 /* GPUImageThresholdSketchFilter.h */,
				F350133226E5F7FD00700B10 /* GPUImageMosaicFilter.h */,
				F350133326E5F7FD00700B10 /* GPUImageKuwaharaFilter.h */,
				F350133426E5F7FD00700B10 /* GPUImageCrosshatchFilter.h */,
				F350133526E5F7FD00700B10 /* GPUImagePolarPixellateFilter.m */,
				F350133626E5F7FD00700B10 /* GPUImageToonFilter.m */,
				F350133726E5F7FD00700B10 /* GPUImagePixellateFilter.m */,
				F350133826E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.h */,
				F350133926E5F7FD00700B10 /* GPUImageSphereRefractionFilter.m */,
				F350133A26E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.h */,
				F350133B26E5F7FD00700B10 /* GPUImageStretchDistortionFilter.m */,
				F350133C26E5F7FD00700B10 /* GPUImagePosterizeFilter.m */,
				F350133D26E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.h */,
				F350133E26E5F7FD00700B10 /* GPUImagePolkaDotFilter.h */,
				F350133F26E5F7FD00700B10 /* GPUImageEmbossFilter.h */,
				F350134026E5F7FD00700B10 /* GPUImageSwirlFilter.m */,
				F350134126E5F7FD00700B10 /* GPUImageHalftoneFilter.m */,
				F350134226E5F7FD00700B10 /* GPUImageSmoothToonFilter.m */,
				F350134326E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.m */,
				F350134426E5F7FD00700B10 /* GPUImageThresholdSketchFilter.m */,
				F350134526E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.h */,
				F350134626E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.h */,
				F350134726E5F7FD00700B10 /* GPUImageSketchFilter.h */,
				F350134826E5F7FD00700B10 /* GPUImageVignetteFilter.h */,
				F350134926E5F7FD00700B10 /* GPUImagePixellatePositionFilter.h */,
				F350134A26E5F7FD00700B10 /* GPUImagePinchDistortionFilter.m */,
				F350134B26E5F7FD00700B10 /* GPUImageGlassSphereFilter.m */,
				F350134C26E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.m */,
				F350134D26E5F7FD00700B10 /* GPUImageSphereRefractionFilter.h */,
				F350134E26E5F7FD00700B10 /* GPUImagePixellateFilter.h */,
				F350134F26E5F7FD00700B10 /* GPUImageToonFilter.h */,
				F350135026E5F7FD00700B10 /* GPUImagePolarPixellateFilter.h */,
				F350135126E5F7FD00700B10 /* GPUImageCrosshatchFilter.m */,
				F350135226E5F7FD00700B10 /* GPUImageKuwaharaFilter.m */,
				F350135326E5F7FD00700B10 /* GPUImageMosaicFilter.m */,
				F350135426E5F7FD00700B10 /* GPUImagePolkaDotFilter.m */,
				F350135526E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.m */,
				F350135626E5F7FD00700B10 /* GPUImagePosterizeFilter.h */,
				F350135726E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.m */,
				F350135826E5F7FD00700B10 /* GPUImageStretchDistortionFilter.h */,
			);
			path = Effects;
			sourceTree = "<group>";
		};
		F350135A26E5F7FD00700B10 /* Pipeline */ = {
			isa = PBXGroup;
			children = (
				F350135B26E5F7FD00700B10 /* GPUImageFilterPipeline.m */,
				F350135C26E5F7FD00700B10 /* GPUImageFilterPipeline.h */,
			);
			path = Pipeline;
			sourceTree = "<group>";
		};
		F350136226E5F7FD00700B10 /* Sources */ = {
			isa = PBXGroup;
			children = (
				F350136326E5F7FD00700B10 /* GPUImageMovieComposition.h */,
				F350136426E5F7FD00700B10 /* GPUImageColorConversion.h */,
				F350136526E5F7FD00700B10 /* GPUImageUIElement.m */,
				F350136626E5F7FD00700B10 /* GPUImageMovie.m */,
				F350136726E5F7FD00700B10 /* GPUImageRawDataInput.h */,
				F350136826E5F7FD00700B10 /* GPUImagePicture.m */,
				F350136926E5F7FD00700B10 /* GPUImageTextureInput.m */,
				F350136A26E5F7FD00700B10 /* GPUImageVideoCamera.h */,
				F350136B26E5F7FD00700B10 /* GPUImageOutput.m */,
				F350136C26E5F7FD00700B10 /* GPUImageStillCamera.h */,
				F350136D26E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.m */,
				F350136E26E5F7FD00700B10 /* GPUImageUIElement.h */,
				F350136F26E5F7FD00700B10 /* GPUImageColorConversion.m */,
				F350137026E5F7FD00700B10 /* GPUImageMovieComposition.m */,
				F350137126E5F7FD00700B10 /* GPUImageRawDataInput.m */,
				F350137226E5F7FD00700B10 /* GPUImageMovie.h */,
				F350137326E5F7FD00700B10 /* GPUImageVideoCamera.m */,
				F350137426E5F7FD00700B10 /* GPUImageTextureInput.h */,
				F350137526E5F7FD00700B10 /* GPUImagePicture.h */,
				F350137626E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.h */,
				F350137726E5F7FD00700B10 /* GPUImageStillCamera.m */,
				F350137826E5F7FD00700B10 /* GPUImageOutput.h */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		F350137A26E5F7FD00700B10 /* Outputs */ = {
			isa = PBXGroup;
			children = (
				F350137B26E5F7FD00700B10 /* GPUImageView.h */,
				F350137C26E5F7FD00700B10 /* GPUImageRawDataOutput.h */,
				F350137D26E5F7FD00700B10 /* GPUImageMovieWriter.m */,
				F350137E26E5F7FD00700B10 /* GPUImageTextureOutput.h */,
				F350137F26E5F7FD00700B10 /* GPUImageRawDataOutput.m */,
				F350138026E5F7FD00700B10 /* GPUImageView.m */,
				F350138126E5F7FD00700B10 /* GPUImageMovieWriter.h */,
				F350138226E5F7FD00700B10 /* GPUImageTextureOutput.m */,
			);
			path = Outputs;
			sourceTree = "<group>";
		};
		F3581E7F26A9043E001B2DC7 /* FMDB */ = {
			isa = PBXGroup;
			children = (
				F3581E8026A9043E001B2DC7 /* FMDatabase.h */,
				F3581E8126A9043E001B2DC7 /* FMDatabaseQueue.m */,
				F3581E8226A9043E001B2DC7 /* FMResultSet.h */,
				F3581E8326A9043E001B2DC7 /* FMDatabasePool.h */,
				F3581E8426A9043E001B2DC7 /* FMDatabaseAdditions.m */,
				F3581E8526A9043E001B2DC7 /* FMDatabase.m */,
				F3581E8626A9043E001B2DC7 /* FMDatabaseQueue.h */,
				F3581E8726A9043E001B2DC7 /* FMDB.h */,
				F3581E8826A9043E001B2DC7 /* FMDatabaseAdditions.h */,
				F3581E8926A9043E001B2DC7 /* FMDatabasePool.m */,
				F3581E8A26A9043E001B2DC7 /* FMResultSet.m */,
			);
			path = FMDB;
			sourceTree = "<group>";
		};
		F36207C426BBC82800E38EC6 /* Dlib */ = {
			isa = PBXGroup;
			children = (
				F36207C626BBC82800E38EC6 /* dlib */,
				F36207C526BBC82800E38EC6 /* shape_predictor_68_face_landmarks.dat */,
				F36B7FD026C383ED00A30090 /* libdlib.a */,
			);
			path = Dlib;
			sourceTree = "<group>";
		};
		F36207C626BBC82800E38EC6 /* dlib */ = {
			isa = PBXGroup;
			children = (
			);
			path = dlib;
			sourceTree = "<group>";
		};
		F362B2FD26BB875100E38EC6 /* images */ = {
			isa = PBXGroup;
			children = (
				F362B2FE26BB877300E38EC6 /* lock.png */,
				F3301A9226BD3AD200CA9E91 /* template1.jpg */,
				A49042D126C0E1C60083A3D7 /* template1.txt */,
				F3301A9426BD3AD600CA9E91 /* template2.jpg */,
				A49042D626C0E2460083A3D7 /* template2.txt */,
				F3301A9326BD3AD400CA9E91 /* template3.jpg */,
				A49042D526C0E2430083A3D7 /* template3.txt */,
			);
			path = images;
			sourceTree = "<group>";
		};
		F37D4AA92697D96300A6740F = {
			isa = PBXGroup;
			children = (
				A5D334592A58FE7500F67392 /* File.swift */,
				F37D4AED2697D9A300A6740F /* Frameworks */,
				A50B9D7C2C1991F8002C4109 /* guide */,
				F362B2FD26BB875100E38EC6 /* images */,
				F37D4B182697D9C000A6740F /* MyTools */,
				A4686DE026A18144005F7A87 /* OtherView */,
				897AAEC4159148D8BBD33D36 /* Pods */,
				F37D4AB32697D96300A6740F /* Products */,
				F37D4AF02697D9B200A6740F /* ThirdLibrarys */,
				F37D4AB42697D96300A6740F /* TimeMachine */,
				A5D334582A58FE7400F67392 /* TimeMachine-Bridging-Header.h */,
				F37D4AD02697D96300A6740F /* TimeMachineTests */,
				F37D4ADB2697D96400A6740F /* TimeMachineUITests */,
				A50B9D682C194F34002C4109 /* UpgradeVIP */,
			);
			sourceTree = "<group>";
		};
		F37D4AB32697D96300A6740F /* Products */ = {
			isa = PBXGroup;
			children = (
				F37D4AB22697D96300A6740F /* TimeMachine.app */,
				F37D4ACD2697D96300A6740F /* TimeMachineTests.xctest */,
				F37D4AD82697D96400A6740F /* TimeMachineUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F37D4AB42697D96300A6740F /* TimeMachine */ = {
			isa = PBXGroup;
			children = (
				AB8DB0942DBE002B0052A79B /* v2.1 */,
				A51C5C3B2D79A29500266A15 /* LunarSwift */,
				A50C5E962D75A0E300A601FC /* year */,
				A5DC04592CB76915006E281C /* faceVc */,
				A5D3345D2A59110700F67392 /* update */,
				F350122826E5F7FC00700B10 /* GPUImage */,
				F36207C426BBC82800E38EC6 /* Dlib */,
				F307652126A7B26E00FF6708 /* Extend */,
				F307651726A7B25E00FF6708 /* ToolMethod */,
				F37D4AB52697D96300A6740F /* AppDelegate.h */,
				F37D4AB62697D96300A6740F /* AppDelegate.m */,
				F37D4AB82697D96300A6740F /* SceneDelegate.h */,
				F37D4AB92697D96300A6740F /* SceneDelegate.m */,
				F37D4ABE2697D96300A6740F /* Main.storyboard */,
				F37D4B232697D9EB00A6740F /* ViewController.h */,
				F37D4B222697D9EB00A6740F /* ViewController.m */,
				F37D4B2C2697DA0400A6740F /* HomeCell.h */,
				F37D4B282697DA0400A6740F /* HomeCell.m */,
				F37D4B2E2697DA0400A6740F /* BaseViewController.h */,
				F37D4B2A2697DA0400A6740F /* BaseViewController.m */,
				F37D4B2F2697DA0400A6740F /* MyImagePickerController.h */,
				F37D4B292697DA0400A6740F /* MyImagePickerController.m */,
				F37D4B2B2697DA0400A6740F /* MyImagePickerMaskView.h */,
				F37D4B2D2697DA0400A6740F /* MyImagePickerMaskView.m */,
				F34A6C2226A6BBDE006DE0E0 /* MyImagePickerResultVC.h */,
				F34A6C2326A6BBDE006DE0E0 /* MyImagePickerResultVC.m */,
				A418CF8F26A58EEA00AA67F8 /* PalmsImagePickerVC.h */,
				A418CF9026A58EEA00AA67F8 /* PalmsImagePickerVC.m */,
				A4179DE226AD354300FBB442 /* PalmsDatabaseManager.h */,
				A4179DE326AD354300FBB442 /* PalmsDatabaseManager.m */,
				A4179DE526AD355D00FBB442 /* PsychologicalDatabaseManager.h */,
				A4179DE626AD355D00FBB442 /* PsychologicalDatabaseManager.m */,
				F37D4B382697DA3100A6740F /* Young */,
				F37D4B372697DA3100A6740F /* Agedness */,
				F37D4B3B2697DA3100A6740F /* Baby */,
				A4686DD626A11F87005F7A87 /* Palms */,
				F34A6BE126A69889006DE0E0 /* Cartoon */,
				F3286E5226A6D58800C46DF1 /* Magic */,
				F3286E5326A6D5B000C46DF1 /* Psychological */,
				F37D4AC12697D96300A6740F /* Assets.xcassets */,
				F37D4AC32697D96300A6740F /* LaunchScreen.storyboard */,
				F37D4AC62697D96300A6740F /* Info.plist */,
				F37D4AC72697D96300A6740F /* main.m */,
				F37D4B402697DAA600A6740F /* PrefixHeader.pch */,
				A49CD67E26C9F62400F19C02 /* Localizable.strings */,
				A49CD68626C9F6DE00F19C02 /* InfoPlist.strings */,
			);
			path = TimeMachine;
			sourceTree = "<group>";
		};
		F37D4AD02697D96300A6740F /* TimeMachineTests */ = {
			isa = PBXGroup;
			children = (
				F37D4AD12697D96400A6740F /* TimeMachineTests.m */,
				F37D4AD32697D96400A6740F /* Info.plist */,
			);
			path = TimeMachineTests;
			sourceTree = "<group>";
		};
		F37D4ADB2697D96400A6740F /* TimeMachineUITests */ = {
			isa = PBXGroup;
			children = (
				F37D4ADC2697D96400A6740F /* TimeMachineUITests.m */,
				F37D4ADE2697D96400A6740F /* Info.plist */,
			);
			path = TimeMachineUITests;
			sourceTree = "<group>";
		};
		F37D4AED2697D9A300A6740F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				F362D57D26BB8C5500E38EC6 /* libc++.tbd */,
				F362B30B26BB881500E38EC6 /* Accelerate.framework */,
				F362B30926BB880F00E38EC6 /* AssetsLibrary.framework */,
				F362B30726BB880500E38EC6 /* CoreMedia.framework */,
				F3581E9826A90463001B2DC7 /* libsqlite3.0.tbd */,
				F3581E9326A9045C001B2DC7 /* libsqlite3.tbd */,
				F37D4AEE2697D9A300A6740F /* AVFoundation.framework */,
				64803D0B95379B4F80A43CA5 /* Pods_TimeMachine.framework */,
				B82C7FF7DD7EE20455599C0D /* Pods_TimeMachine_TimeMachineUITests.framework */,
				FB734F328401C6FCF513BFB2 /* Pods_TimeMachineTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F37D4AF02697D9B200A6740F /* ThirdLibrarys */ = {
			isa = PBXGroup;
			children = (
				F3BDC3F926C5027B0019A0F4 /* MyProgressView */,
				F3BDC3E926C502240019A0F4 /* TingCycleScrollView */,
				F3BDC3E326C5021F0019A0F4 /* TingFadeOutView */,
				F3BDC3E626C502220019A0F4 /* TingSimpleScrollView */,
				F3581E7F26A9043E001B2DC7 /* FMDB */,
				F307662A26A7B84100FF6708 /* SDWebImage */,
				F307652B26A7B53E00FF6708 /* AFNetworking */,
				F34A6C0926A6A121006DE0E0 /* SVProgressHUD */,
				A418CF8426A5622900AA67F8 /* StarView */,
				F37D4AF12697D9B200A6740F /* Masonry */,
			);
			path = ThirdLibrarys;
			sourceTree = "<group>";
		};
		F37D4AF12697D9B200A6740F /* Masonry */ = {
			isa = PBXGroup;
			children = (
				F37D4AF22697D9B200A6740F /* MASCompositeConstraint.h */,
				F37D4AF32697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.m */,
				F37D4AF42697D9B200A6740F /* MASConstraint+Private.h */,
				F37D4AF52697D9B200A6740F /* MASLayoutConstraint.h */,
				F37D4AF62697D9B200A6740F /* NSArray+MASShorthandAdditions.h */,
				F37D4AF72697D9B200A6740F /* MASConstraintMaker.h */,
				F37D4AF82697D9B200A6740F /* View+MASAdditions.h */,
				F37D4AF92697D9B200A6740F /* NSArray+MASAdditions.h */,
				F37D4AFA2697D9B200A6740F /* MASUtilities.h */,
				F37D4AFB2697D9B200A6740F /* MASViewAttribute.h */,
				F37D4AFC2697D9B200A6740F /* ViewController+MASAdditions.m */,
				F37D4AFD2697D9B200A6740F /* MASViewConstraint.h */,
				F37D4AFE2697D9B200A6740F /* MASConstraint.h */,
				F37D4AFF2697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.h */,
				F37D4B002697D9B200A6740F /* MASCompositeConstraint.m */,
				F37D4B012697D9B200A6740F /* MASConstraintMaker.m */,
				F37D4B022697D9B200A6740F /* MASLayoutConstraint.m */,
				F37D4B032697D9B200A6740F /* NSArray+MASAdditions.m */,
				F37D4B042697D9B200A6740F /* View+MASAdditions.m */,
				F37D4B052697D9B200A6740F /* View+MASShorthandAdditions.h */,
				F37D4B062697D9B200A6740F /* Masonry.h */,
				F37D4B072697D9B200A6740F /* MASConstraint.m */,
				F37D4B082697D9B200A6740F /* ViewController+MASAdditions.h */,
				F37D4B092697D9B200A6740F /* MASViewConstraint.m */,
				F37D4B0A2697D9B200A6740F /* MASViewAttribute.m */,
			);
			path = Masonry;
			sourceTree = "<group>";
		};
		F37D4B182697D9C000A6740F /* MyTools */ = {
			isa = PBXGroup;
			children = (
				A5D334872A59123E00F67392 /* MyAdManager.h */,
				A5D334862A59123E00F67392 /* MyAdManager.m */,
				A5D334822A59117800F67392 /* Reachability */,
				F34A6C1C26A6B534006DE0E0 /* UIImage+FixOrientation.h */,
				F34A6C1D26A6B534006DE0E0 /* UIImage+FixOrientation.m */,
				A51C5B322D79965600266A15 /* UIButton+CenterImageAndTitle.h */,
				A51C5B332D79965600266A15 /* UIButton+CenterImageAndTitle.m */,
				F37D4B192697D9C000A6740F /* MyColor.m */,
				F37D4B1A2697D9C000A6740F /* MyColor.h */,
				F37D4B6B269826D600A6740F /* iPhoneXTool.h */,
				F37D4B6C269826D600A6740F /* iPhoneXTool.m */,
				F307675226A7CA2200FF6708 /* CheckImageHaveFace.h */,
				F307675326A7CA2200FF6708 /* CheckImageHaveFace.m */,
				F307676D26A80C0E00FF6708 /* ImageManager.h */,
				F307676E26A80C0E00FF6708 /* ImageManager.m */,
				F307677926A81CC700FF6708 /* FileManager.h */,
				F307677A26A81CC700FF6708 /* FileManager.m */,
				F307677F26A8201600FF6708 /* TimeTool.h */,
				F307678026A8201600FF6708 /* TimeTool.m */,
				F3581E9D26A90683001B2DC7 /* DatabaseManager.h */,
				F3581E9E26A90683001B2DC7 /* DatabaseManager.m */,
				F307DA1F26A987F800E6C02D /* DrawManager.h */,
				F307DA2026A987F800E6C02D /* DrawManager.m */,
				F320396826CF507F008295B2 /* LanguageManager.h */,
				F320396726CF507D008295B2 /* LanguageManager.m */,
				A5DD99512D6C241900412F3C /* DeepSeekStreamClient.h */,
				A5DD99522D6C241900412F3C /* DeepSeekStreamClient.m */,
				A5C295462D7C183400B3F394 /* OSSUploadImageTool.h */,
				A5C295472D7C183400B3F394 /* OSSUploadImageTool.m */,
			);
			path = MyTools;
			sourceTree = "<group>";
		};
		F37D4B372697DA3100A6740F /* Agedness */ = {
			isa = PBXGroup;
			children = (
				F37D4B80269845DE00A6740F /* AgednessViewController.h */,
				F37D4B81269845DE00A6740F /* AgednessViewController.m */,
			);
			path = Agedness;
			sourceTree = "<group>";
		};
		F37D4B382697DA3100A6740F /* Young */ = {
			isa = PBXGroup;
			children = (
				F37D4B392697DA3100A6740F /* YoungViewController.h */,
				F37D4B3A2697DA3100A6740F /* YoungViewController.m */,
			);
			path = Young;
			sourceTree = "<group>";
		};
		F37D4B3B2697DA3100A6740F /* Baby */ = {
			isa = PBXGroup;
			children = (
				F37D4B5F2697F60300A6740F /* BabyViewController.h */,
				F37D4B602697F60300A6740F /* BabyViewController.m */,
				F37D4B6526981CE400A6740F /* BabyResultVC.h */,
				F37D4B6626981CE400A6740F /* BabyResultVC.m */,
			);
			path = Baby;
			sourceTree = "<group>";
		};
		F3BDC3E326C5021F0019A0F4 /* TingFadeOutView */ = {
			isa = PBXGroup;
			children = (
				F3BDC3E426C5021F0019A0F4 /* TingFadeOutView.h */,
				F3BDC3E526C5021F0019A0F4 /* TingFadeOutView.m */,
			);
			path = TingFadeOutView;
			sourceTree = "<group>";
		};
		F3BDC3E626C502220019A0F4 /* TingSimpleScrollView */ = {
			isa = PBXGroup;
			children = (
				F3BDC3E826C502220019A0F4 /* TingSimpleScrollView.h */,
				F3BDC3E726C502220019A0F4 /* TingSimpleScrollView.m */,
			);
			path = TingSimpleScrollView;
			sourceTree = "<group>";
		};
		F3BDC3E926C502240019A0F4 /* TingCycleScrollView */ = {
			isa = PBXGroup;
			children = (
				F3BDC3EA26C502240019A0F4 /* TingCycleScrollView.h */,
				F3BDC3EB26C502240019A0F4 /* TingCycleScrollView.m */,
			);
			path = TingCycleScrollView;
			sourceTree = "<group>";
		};
		F3BDC3F926C5027B0019A0F4 /* MyProgressView */ = {
			isa = PBXGroup;
			children = (
				F3BDC3FA26C5027B0019A0F4 /* MyProgressView.h */,
				F3BDC3FB26C5027B0019A0F4 /* MyProgressView.m */,
			);
			path = MyProgressView;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F37D4AB12697D96300A6740F /* TimeMachine */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F37D4AE12697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachine" */;
			buildPhases = (
				5EE98A9B0727BF42BF0F89F0 /* [CP] Check Pods Manifest.lock */,
				F37D4AAE2697D96300A6740F /* Sources */,
				F37D4AAF2697D96300A6740F /* Frameworks */,
				F37D4AB02697D96300A6740F /* Resources */,
				701221AA590D72AC7BC95600 /* [CP] Embed Pods Frameworks */,
				1435051F31991ACEA32C7F61 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TimeMachine;
			productName = TimeMachine;
			productReference = F37D4AB22697D96300A6740F /* TimeMachine.app */;
			productType = "com.apple.product-type.application";
		};
		F37D4ACC2697D96300A6740F /* TimeMachineTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F37D4AE42697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachineTests" */;
			buildPhases = (
				BD19E9DAE61EBDA6FA1F0E95 /* [CP] Check Pods Manifest.lock */,
				F37D4AC92697D96300A6740F /* Sources */,
				F37D4ACA2697D96300A6740F /* Frameworks */,
				F37D4ACB2697D96300A6740F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F37D4ACF2697D96300A6740F /* PBXTargetDependency */,
			);
			name = TimeMachineTests;
			productName = TimeMachineTests;
			productReference = F37D4ACD2697D96300A6740F /* TimeMachineTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F37D4AD72697D96400A6740F /* TimeMachineUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F37D4AE72697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachineUITests" */;
			buildPhases = (
				AFC9A02909FFA69C186A4830 /* [CP] Check Pods Manifest.lock */,
				F37D4AD42697D96400A6740F /* Sources */,
				F37D4AD52697D96400A6740F /* Frameworks */,
				F37D4AD62697D96400A6740F /* Resources */,
				6A66D55AC22F4BBA7417397A /* [CP] Embed Pods Frameworks */,
				6A75A97FFA9C5B881B941D7C /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F37D4ADA2697D96400A6740F /* PBXTargetDependency */,
			);
			name = TimeMachineUITests;
			productName = TimeMachineUITests;
			productReference = F37D4AD82697D96400A6740F /* TimeMachineUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F37D4AAA2697D96300A6740F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				TargetAttributes = {
					F37D4AB12697D96300A6740F = {
						CreatedOnToolsVersion = 12.4;
						LastSwiftMigration = 1420;
					};
					F37D4ACC2697D96300A6740F = {
						CreatedOnToolsVersion = 12.4;
						TestTargetID = F37D4AB12697D96300A6740F;
					};
					F37D4AD72697D96400A6740F = {
						CreatedOnToolsVersion = 12.4;
						TestTargetID = F37D4AB12697D96300A6740F;
					};
				};
			};
			buildConfigurationList = F37D4AAD2697D96300A6740F /* Build configuration list for PBXProject "TimeMachine" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				de,
				es,
				ja,
				ko,
				"zh-Hant",
				fr,
				ar,
			);
			mainGroup = F37D4AA92697D96300A6740F;
			productRefGroup = F37D4AB32697D96300A6740F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F37D4AB12697D96300A6740F /* TimeMachine */,
				F37D4ACC2697D96300A6740F /* TimeMachineTests */,
				F37D4AD72697D96400A6740F /* TimeMachineUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F37D4AB02697D96300A6740F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A49042D726C0E2460083A3D7 /* template3.txt in Resources */,
				A49042D226C0E1C60083A3D7 /* template1.txt in Resources */,
				F37D4AC52697D96300A6740F /* LaunchScreen.storyboard in Resources */,
				A50B9DA32C1991F8002C4109 /* IntroduceBackground04.png in Resources */,
				A50B9DA12C1991F8002C4109 /* BSEnterBaseController.xib in Resources */,
				F34A6C1826A6A121006DE0E0 /* SVProgressHUD.bundle in Resources */,
				A5E87C582CBD12B40039716D /* face.json in Resources */,
				F37D4AC22697D96300A6740F /* Assets.xcassets in Resources */,
				A50B9D9D2C1991F8002C4109 /* BSLaunchScreenVC.xib in Resources */,
				A570F62A2C103F9D00614266 /* 手相.json in Resources */,
				A49CD68426C9F6DE00F19C02 /* InfoPlist.strings in Resources */,
				F3620E7B26BBC83100E38EC6 /* shape_predictor_68_face_landmarks.dat in Resources */,
				A50B9DA52C1991F8002C4109 /* IntroduceBackground02.png in Resources */,
				A5E87C562CBD127B0039716D /* face_en.json in Resources */,
				A50B9DA72C1991F8002C4109 /* IntroduceBackground01.png in Resources */,
				A49042D826C0E2460083A3D7 /* template2.txt in Resources */,
				A50B9DAF2C1991F8002C4109 /* EnterViewBackground.png in Resources */,
				A418CF8E26A5622900AA67F8 /* Star.png in Resources */,
				F3301A9826BD3AD600CA9E91 /* template2.jpg in Resources */,
				A50B9DA62C1991F8002C4109 /* IntroduceBackground03.png in Resources */,
				A418CF8D26A5622900AA67F8 /* Star_highlight.png in Resources */,
				A50B9D9E2C1991F8002C4109 /* EnterViewBackground_en.png in Resources */,
				F3301A9726BD3AD600CA9E91 /* template3.jpg in Resources */,
				A50B9D672C194E21002C4109 /* 手相_en.json in Resources */,
				F37D4AC02697D96300A6740F /* Main.storyboard in Resources */,
				A49CD67C26C9F62400F19C02 /* Localizable.strings in Resources */,
				F362B30026BB877300E38EC6 /* lock.png in Resources */,
				F3301A9626BD3AD600CA9E91 /* template1.jpg in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4ACB2697D96300A6740F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4AD62697D96400A6740F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1435051F31991ACEA32C7F61 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5EE98A9B0727BF42BF0F89F0 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TimeMachine-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		6A66D55AC22F4BBA7417397A /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6A75A97FFA9C5B881B941D7C /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TimeMachine-TimeMachineUITests/Pods-TimeMachine-TimeMachineUITests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		701221AA590D72AC7BC95600 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TimeMachine/Pods-TimeMachine-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AFC9A02909FFA69C186A4830 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TimeMachine-TimeMachineUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BD19E9DAE61EBDA6FA1F0E95 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TimeMachineTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F37D4AAE2697D96300A6740F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F307670F26A7B84100FF6708 /* SDMemoryCache.m in Sources */,
				F320396926CF507F008295B2 /* LanguageManager.m in Sources */,
				F350139926E5F7FD00700B10 /* GPUImageSolidColorGenerator.m in Sources */,
				F350138A26E5F7FD00700B10 /* GPUImageTwoInputFilter.m in Sources */,
				F350140726E5F7FD00700B10 /* GPUImagePolarPixellateFilter.m in Sources */,
				A5D3347A2A59110700F67392 /* UIButton+Create.m in Sources */,
				F350138B26E5F7FD00700B10 /* GPUImageChromaKeyFilter.m in Sources */,
				A50C5E992D75A10900A601FC /* TMYearViewController.m in Sources */,
				F350142C26E5F7FD00700B10 /* GPUImageMovieWriter.m in Sources */,
				F35013DC26E5F7FD00700B10 /* GPUImageMotionBlurFilter.m in Sources */,
				F35013C526E5F7FD00700B10 /* GPUImageLineGenerator.m in Sources */,
				F33007F726BCC39A00CA9E91 /* MagicChangeFaceView.mm in Sources */,
				F35013A326E5F7FD00700B10 /* GPUImageLuminosity.m in Sources */,
				F350141826E5F7FD00700B10 /* GPUImagePolkaDotFilter.m in Sources */,
				F35013DA26E5F7FD00700B10 /* GPUImageMedianFilter.m in Sources */,
				F34A6C1726A6A121006DE0E0 /* SVIndefiniteAnimatedView.m in Sources */,
				F307673726A7B84100FF6708 /* SDInternalMacros.m in Sources */,
				F307673526A7B84100FF6708 /* SDDeviceHelper.m in Sources */,
				F37D4B612697F60300A6740F /* BabyViewController.m in Sources */,
				F35013D826E5F7FD00700B10 /* GPUImageClosingFilter.m in Sources */,
				F350142026E5F7FD00700B10 /* GPUImageFramebufferCache.m in Sources */,
				F350138826E5F7FD00700B10 /* GPUImageFourInputFilter.m in Sources */,
				F350139A26E5F7FD00700B10 /* GPUImageColorMatrixFilter.m in Sources */,
				A4686DDF26A163BB005F7A87 /* PalmsResultVC.m in Sources */,
				F307670A26A7B84100FF6708 /* SDWebImageIndicator.m in Sources */,
				F307671726A7B84100FF6708 /* SDImageIOCoder.m in Sources */,
				F307670126A7B84100FF6708 /* SDAnimatedImage.m in Sources */,
				F3581E8E26A9043E001B2DC7 /* FMDatabasePool.m in Sources */,
				F30766FD26A7B84100FF6708 /* SDWebImageDownloaderDecryptor.m in Sources */,
				A5D334802A59110700F67392 /* ZYEButton.m in Sources */,
				F37D4B6726981CE400A6740F /* BabyResultVC.m in Sources */,
				A50B9D7B2C194F34002C4109 /* NSDictionary+Value.m in Sources */,
				F350142A26E5F7FD00700B10 /* GPUImageVideoCamera.m in Sources */,
				F307653B26A7B53E00FF6708 /* AFHTTPSessionManager.m in Sources */,
				F3581E8F26A9043E001B2DC7 /* FMResultSet.m in Sources */,
				A5D3347E2A59110700F67392 /* UpdateManager.m in Sources */,
				F35013BF26E5F7FD00700B10 /* GPUImageHarrisCornerDetectionFilter.m in Sources */,
				F34A6C2426A6BBDE006DE0E0 /* MyImagePickerResultVC.m in Sources */,
				F35013D226E5F7FD00700B10 /* GPUImageShiTomasiFeatureDetectionFilter.m in Sources */,
				F350142626E5F7FD00700B10 /* GPUImagePicture+TextureSubimage.m in Sources */,
				F37D4B132697D9B200A6740F /* MASViewConstraint.m in Sources */,
				F37D4B332697DA0400A6740F /* MyImagePickerMaskView.m in Sources */,
				A5D3345A2A58FE7500F67392 /* File.swift in Sources */,
				F35013BE26E5F7FD00700B10 /* GPUImageBilateralFilter.m in Sources */,
				F37D4B322697DA0400A6740F /* BaseViewController.m in Sources */,
				F35013E626E5F7FD00700B10 /* GPUImageColorBurnBlendFilter.m in Sources */,
				F35013A026E5F7FD00700B10 /* GPUImageAverageLuminanceThresholdFilter.m in Sources */,
				F3581E8D26A9043E001B2DC7 /* FMDatabase.m in Sources */,
				F3286E6226A6D76700C46DF1 /* MagicViewController.m in Sources */,
				F350140326E5F7FD00700B10 /* GPUImageVignetteFilter.m in Sources */,
				F35013EE26E5F7FD00700B10 /* GPUImageDivideBlendFilter.m in Sources */,
				F35013BC26E5F7FD00700B10 /* GPUImageTransformFilter.m in Sources */,
				F307672026A7B84100FF6708 /* SDImageLoadersManager.m in Sources */,
				F307670B26A7B84100FF6708 /* SDGraphicsImageRenderer.m in Sources */,
				F35013B426E5F7FD00700B10 /* GPUImageHighPassFilter.m in Sources */,
				A5D334782A59110700F67392 /* PopAnimationTool.m in Sources */,
				F35013E526E5F7FD00700B10 /* GPUImageBuffer.m in Sources */,
				F307653A26A7B53E00FF6708 /* AFURLResponseSerialization.m in Sources */,
				F307672626A7B84100FF6708 /* SDImageGraphics.m in Sources */,
				F350138F26E5F7FD00700B10 /* GPUImageGrayscaleFilter.m in Sources */,
				F35013C226E5F7FD00700B10 /* GPUImageErosionFilter.m in Sources */,
				F350142F26E5F7FD00700B10 /* GPUImageTextureOutput.m in Sources */,
				F32048F826AFF19F00E0D893 /* VisionImageView.m in Sources */,
				F35013CF26E5F7FD00700B10 /* GPUImageCrosshairGenerator.m in Sources */,
				A51C5C3C2D79A29500266A15 /* SolarWeek.swift in Sources */,
				AB8DB0992DBE05410052A79B /* MainTabbarVC.m in Sources */,
				A51C5C3D2D79A29500266A15 /* TaoUtil.swift in Sources */,
				A51C5C3E2D79A29500266A15 /* Solar.swift in Sources */,
				A51C5C3F2D79A29500266A15 /* LunarTime.swift in Sources */,
				A51C5C402D79A29500266A15 /* ShouXingUtil.swift in Sources */,
				A51C5C412D79A29500266A15 /* LunarYear.swift in Sources */,
				A51C5C422D79A29500266A15 /* Holiday.swift in Sources */,
				A51C5C432D79A29500266A15 /* Tao.swift in Sources */,
				A51C5C442D79A29500266A15 /* FotoUtil.swift in Sources */,
				A51C5C452D79A29500266A15 /* SolarMonth.swift in Sources */,
				A51C5C462D79A29500266A15 /* Foto.swift in Sources */,
				A51C5C472D79A29500266A15 /* TaoFestival.swift in Sources */,
				A51C5C482D79A29500266A15 /* LunarUtil.swift in Sources */,
				A51C5C492D79A29500266A15 /* LunarMonth.swift in Sources */,
				A51C5C4A2D79A29500266A15 /* SolarUtil.swift in Sources */,
				A51C5C4B2D79A29500266A15 /* LiuYue.swift in Sources */,
				A51C5C4C2D79A29500266A15 /* LiuNian.swift in Sources */,
				A51C5C4D2D79A29500266A15 /* ShuJiu.swift in Sources */,
				A51C5C4E2D79A29500266A15 /* EightChar.swift in Sources */,
				A51C5C4F2D79A29500266A15 /* JieQi.swift in Sources */,
				A51C5C502D79A29500266A15 /* SolarHalfYear.swift in Sources */,
				A51C5C512D79A29500266A15 /* Lunar.swift in Sources */,
				A51C5C522D79A29500266A15 /* SolarYear.swift in Sources */,
				A51C5C532D79A29500266A15 /* Yun.swift in Sources */,
				A51C5C542D79A29500266A15 /* HolidayUtil.swift in Sources */,
				A51C5C552D79A29500266A15 /* NineStar.swift in Sources */,
				A51C5C562D79A29500266A15 /* SolarSeason.swift in Sources */,
				A51C5C572D79A29500266A15 /* FotoFestival.swift in Sources */,
				A51C5C582D79A29500266A15 /* DaYun.swift in Sources */,
				A51C5C592D79A29500266A15 /* Fu.swift in Sources */,
				A51C5C5A2D79A29500266A15 /* XiaoYun.swift in Sources */,
				A4179DE726AD355D00FBB442 /* PsychologicalDatabaseManager.m in Sources */,
				F350141626E5F7FD00700B10 /* GPUImageKuwaharaFilter.m in Sources */,
				F350140F26E5F7FD00700B10 /* GPUImageSmoothToonFilter.m in Sources */,
				A51C5B372D799D8300266A15 /* TMZhanbuController.m in Sources */,
				F350140A26E5F7FD00700B10 /* GPUImageSphereRefractionFilter.m in Sources */,
				F350140226E5F7FD00700B10 /* GPUImagePixellatePositionFilter.m in Sources */,
				F35013A426E5F7FD00700B10 /* GPUImageAverageColor.m in Sources */,
				A5C295482D7C183400B3F394 /* OSSUploadImageTool.m in Sources */,
				F37D4B6D269826D600A6740F /* iPhoneXTool.m in Sources */,
				AB8901BA2DBF279C00C17211 /* CardSliderView.m in Sources */,
				F350142126E5F7FD00700B10 /* GPUImageUIElement.m in Sources */,
				F307672126A7B84100FF6708 /* SDImageCachesManager.m in Sources */,
				F350140E26E5F7FD00700B10 /* GPUImageHalftoneFilter.m in Sources */,
				F350142426E5F7FD00700B10 /* GPUImageTextureInput.m in Sources */,
				F307673326A7B84100FF6708 /* SDImageCachesManagerOperation.m in Sources */,
				A50B9D9F2C1991F8002C4109 /* UIView+BSAnimation.m in Sources */,
				F307670926A7B84100FF6708 /* SDWebImageCacheSerializer.m in Sources */,
				F35013E326E5F7FD00700B10 /* GPUImageColourFASTFeatureDetector.m in Sources */,
				F30766FC26A7B84100FF6708 /* SDWebImageOperation.m in Sources */,
				F350139426E5F7FD00700B10 /* GPUImageHistogramEqualizationFilter.m in Sources */,
				A50B9DAB2C1991F8002C4109 /* BSSubscribeRadio.m in Sources */,
				F307673026A7B84100FF6708 /* UIButton+WebCache.m in Sources */,
				AB8901B72DBF234700C17211 /* CardView.m in Sources */,
				F35013C826E5F7FD00700B10 /* GPUImageXYDerivativeFilter.m in Sources */,
				F34A6C1626A6A121006DE0E0 /* SVProgressHUD.m in Sources */,
				F3BDC3E226C4F58A0019A0F4 /* MyPageControl.m in Sources */,
				F35013F826E5F7FD00700B10 /* GPUImageScreenBlendFilter.m in Sources */,
				A51C5B342D79965600266A15 /* UIButton+CenterImageAndTitle.m in Sources */,
				F35013A726E5F7FD00700B10 /* GPUImageWhiteBalanceFilter.m in Sources */,
				F37D4B142697D9B200A6740F /* MASViewAttribute.m in Sources */,
				F307673426A7B84100FF6708 /* SDImageAssetManager.m in Sources */,
				F307672C26A7B84100FF6708 /* SDWebImageOptionsProcessor.m in Sources */,
				A5D3347B2A59110700F67392 /* UIColor+Hex.m in Sources */,
				F35013E826E5F7FD00700B10 /* GPUImageExclusionBlendFilter.m in Sources */,
				A50B9D772C194F34002C4109 /* UpgradeVIPPayButton.m in Sources */,
				A50B9DA02C1991F8002C4109 /* BSEnterBaseController.m in Sources */,
				F307671126A7B84100FF6708 /* SDImageAPNGCoder.m in Sources */,
				F35013AC26E5F7FD00700B10 /* GPUImageLuminanceRangeFilter.m in Sources */,
				F307DA2126A987F800E6C02D /* DrawManager.m in Sources */,
				A50B9DA82C1991F8002C4109 /* ProgressViewUserInfo.m in Sources */,
				F35013A826E5F7FD00700B10 /* GPUImageMonochromeFilter.m in Sources */,
				F35013FC26E5F7FD00700B10 /* GPUImageLuminosityBlendFilter.m in Sources */,
				F35013D726E5F7FD00700B10 /* GPUImageMotionDetector.m in Sources */,
				F307673226A7B84100FF6708 /* UIColor+SDHexString.m in Sources */,
				F35013C126E5F7FD00700B10 /* GPUImageOpeningFilter.m in Sources */,
				F35013BA26E5F7FD00700B10 /* GPUImageFASTCornerDetectionFilter.m in Sources */,
				F350139226E5F7FD00700B10 /* GPUImageHistogramFilter.m in Sources */,
				F307651D26A7B25E00FF6708 /* RSA.m in Sources */,
				A50B9DA22C1991F8002C4109 /* BSLaunchScreenVC.m in Sources */,
				A4686DDC26A12C77005F7A87 /* PalmsUnscrambleVC.m in Sources */,
				F350141126E5F7FD00700B10 /* GPUImageThresholdSketchFilter.m in Sources */,
				A5D3347D2A59110700F67392 /* UIImageView+Create.m in Sources */,
				F350139326E5F7FD00700B10 /* GPUImageMissEtikateFilter.m in Sources */,
				F35013D326E5F7FD00700B10 /* GPUImageRGBOpeningFilter.m in Sources */,
				F307671F26A7B84100FF6708 /* SDWebImageDownloaderOperation.m in Sources */,
				F350140D26E5F7FD00700B10 /* GPUImageSwirlFilter.m in Sources */,
				F350139E26E5F7FD00700B10 /* GPUImageSepiaFilter.m in Sources */,
				F35013EA26E5F7FD00700B10 /* GPUImageMultiplyBlendFilter.m in Sources */,
				F3581E8B26A9043E001B2DC7 /* FMDatabaseQueue.m in Sources */,
				F37D4AB72697D96300A6740F /* AppDelegate.m in Sources */,
				F350138726E5F7FD00700B10 /* GPUImageFilterGroup.m in Sources */,
				F35013CB26E5F7FD00700B10 /* GPUImageTiltShiftFilter.m in Sources */,
				A5DD99532D6C241900412F3C /* DeepSeekStreamClient.m in Sources */,
				F34A6BED26A69D00006DE0E0 /* CartoonResultVC.m in Sources */,
				F350138C26E5F7FD00700B10 /* GPUImageLuminanceThresholdFilter.m in Sources */,
				F350141226E5F7FD00700B10 /* GPUImagePinchDistortionFilter.m in Sources */,
				F350140026E5F7FD00700B10 /* GPUImageTwoInputCrossTextureSamplingFilter.m in Sources */,
				F307653D26A7B53E00FF6708 /* AFURLRequestSerialization.m in Sources */,
				F35013F126E5F7FD00700B10 /* GPUImageSaturationBlendFilter.m in Sources */,
				F37D4B102697D9B200A6740F /* NSArray+MASAdditions.m in Sources */,
				F37D4B0C2697D9B200A6740F /* ViewController+MASAdditions.m in Sources */,
				F307671D26A7B84100FF6708 /* SDImageGIFCoder.m in Sources */,
				F35013B626E5F7FD00700B10 /* GPUImageColorPackingFilter.m in Sources */,
				F35013FD26E5F7FD00700B10 /* GPUImageChromaKeyBlendFilter.m in Sources */,
				F35013AD26E5F7FD00700B10 /* GPUImageFalseColorFilter.m in Sources */,
				F350139026E5F7FD00700B10 /* GPUImageLevelsFilter.m in Sources */,
				F350140C26E5F7FD00700B10 /* GPUImagePosterizeFilter.m in Sources */,
				F37D4B0D2697D9B200A6740F /* MASCompositeConstraint.m in Sources */,
				F307672426A7B84100FF6708 /* NSData+ImageContentType.m in Sources */,
				F34A6C1E26A6B534006DE0E0 /* UIImage+FixOrientation.m in Sources */,
				F35013B026E5F7FD00700B10 /* GPUImageUnsharpMaskFilter.m in Sources */,
				A4179DE426AD354300FBB442 /* PalmsDatabaseManager.m in Sources */,
				F307672E26A7B84100FF6708 /* UIView+WebCache.m in Sources */,
				F30766F926A7B84100FF6708 /* NSImage+Compatibility.m in Sources */,
				F307671326A7B84100FF6708 /* SDWebImageError.m in Sources */,
				F35013CC26E5F7FD00700B10 /* GPUImageGaussianBlurFilter.m in Sources */,
				A4686DD926A11FA9005F7A87 /* PalmsViewController.m in Sources */,
				F307670526A7B84100FF6708 /* SDAnimatedImagePlayer.m in Sources */,
				F307653E26A7B53E00FF6708 /* AFNetworkReachabilityManager.m in Sources */,
				F34A6C1426A6A121006DE0E0 /* SVRadialGradientLayer.m in Sources */,
				F307670D26A7B84100FF6708 /* SDImageCodersManager.m in Sources */,
				F36210CB26BBC9B600E38EC6 /* FacePointsTool.mm in Sources */,
				F350140526E5F7FD00700B10 /* GPUImageJFAVoronoiFilter.m in Sources */,
				F35013DB26E5F7FD00700B10 /* GPUImageLocalBinaryPatternFilter.m in Sources */,
				F307671026A7B84100FF6708 /* SDWebImageCompat.m in Sources */,
				AB9BE91F2DC0ABDD00D4CE8B /* FaceAnalysisVC.m in Sources */,
				F35013DE26E5F7FD00700B10 /* GPUImageDilationFilter.m in Sources */,
				A5D3347F2A59110700F67392 /* UIView+Extension.m in Sources */,
				F35013C326E5F7FD00700B10 /* GPUImageCropFilter.m in Sources */,
				F35013E726E5F7FD00700B10 /* GPUImageAlphaBlendFilter.m in Sources */,
				F350141A26E5F7FD00700B10 /* GPUImageCGAColorspaceFilter.m in Sources */,
				F350141426E5F7FD00700B10 /* GPUImagePerlinNoiseFilter.m in Sources */,
				F35013E926E5F7FD00700B10 /* GPUImageOverlayBlendFilter.m in Sources */,
				F350139126E5F7FD00700B10 /* GPUImageSolarizeFilter.m in Sources */,
				F3BDC3EE26C502240019A0F4 /* TingCycleScrollView.m in Sources */,
				F350141F26E5F7FD00700B10 /* GPUImageFramebuffer.m in Sources */,
				F35013C626E5F7FD00700B10 /* GPUImageDirectionalSobelEdgeDetectionFilter.m in Sources */,
				F37D4AC82697D96300A6740F /* main.m in Sources */,
				A50B9D792C194F34002C4109 /* NSString+Base64.m in Sources */,
				A50B9D762C194F34002C4109 /* UpgradeVIPViewController.m in Sources */,
				F350139826E5F7FD00700B10 /* GPUImageGammaFilter.m in Sources */,
				F350141726E5F7FD00700B10 /* GPUImageMosaicFilter.m in Sources */,
				F35013A926E5F7FD00700B10 /* GPUImageHSBFilter.m in Sources */,
				A418CF9126A58EEA00AA67F8 /* PalmsImagePickerVC.m in Sources */,
				F35013B826E5F7FD00700B10 /* GPUImageNonMaximumSuppressionFilter.m in Sources */,
				A5D334852A59117800F67392 /* Reachability.m in Sources */,
				A418CF7B26A51C2F00AA67F8 /* AWPolygonView.m in Sources */,
				F3BDC3FC26C5027B0019A0F4 /* MyProgressView.m in Sources */,
				F350142B26E5F7FD00700B10 /* GPUImageStillCamera.m in Sources */,
				F350141C26E5F7FD00700B10 /* GPUImageFilterPipeline.m in Sources */,
				A50B9DAA2C1991F8002C4109 /* BSEtDetailsView.m in Sources */,
				F35013B126E5F7FD00700B10 /* GPUImageThresholdEdgeDetectionFilter.m in Sources */,
				F307670726A7B84100FF6708 /* SDWebImageDownloader.m in Sources */,
				F3301A3026BCDCC800CA9E91 /* MagicResultVC.m in Sources */,
				F35013D026E5F7FD00700B10 /* GPUImageRGBDilationFilter.m in Sources */,
				F37D4B112697D9B200A6740F /* View+MASAdditions.m in Sources */,
				F307653C26A7B53E00FF6708 /* AFURLSessionManager.m in Sources */,
				F35013BB26E5F7FD00700B10 /* GPUImageiOSBlurFilter.m in Sources */,
				F35013FB26E5F7FD00700B10 /* GPUImageNormalBlendFilter.m in Sources */,
				F350141026E5F7FD00700B10 /* GPUImageVoronoiConsumerFilter.m in Sources */,
				AB8901BD2DBF758300C17211 /* AllFuncShowView.m in Sources */,
				A5D334882A59123E00F67392 /* MyAdManager.m in Sources */,
				F350141D26E5F7FD00700B10 /* GPUImageContext.m in Sources */,
				F35013D426E5F7FD00700B10 /* GPUImageRGBErosionFilter.m in Sources */,
				A50B9D7A2C194F34002C4109 /* APPMakeStoreIAPManager.m in Sources */,
				F307672826A7B84100FF6708 /* UIImage+MemoryCacheCost.m in Sources */,
				F35013F426E5F7FD00700B10 /* GPUImageSoftLightBlendFilter.m in Sources */,
				F307672226A7B84100FF6708 /* SDWebImageTransition.m in Sources */,
				F3286E5C26A6D66400C46DF1 /* PsychologicalResultVC.m in Sources */,
				F350142226E5F7FD00700B10 /* GPUImageMovie.m in Sources */,
				F37D4B82269845DE00A6740F /* AgednessViewController.m in Sources */,
				F350138D26E5F7FD00700B10 /* GPUImageSoftEleganceFilter.m in Sources */,
				F307672726A7B84100FF6708 /* SDImageCacheDefine.m in Sources */,
				AB8DB09C2DBE0A070052A79B /* MentalTestVC.m in Sources */,
				F350138926E5F7FD00700B10 /* GPUImageTwoPassFilter.m in Sources */,
				F35013C726E5F7FD00700B10 /* GPUImageLanczosResamplingFilter.m in Sources */,
				F35013B926E5F7FD00700B10 /* GPUImageCannyEdgeDetectionFilter.m in Sources */,
				A5FDEB502CBCF52E00F3D63A /* FaceResultController.m in Sources */,
				F37D4B3C2697DA3100A6740F /* YoungViewController.m in Sources */,
				F35013FA26E5F7FD00700B10 /* GPUImageAddBlendFilter.m in Sources */,
				F307673926A7B84100FF6708 /* NSBezierPath+SDRoundedCorners.m in Sources */,
				F307670E26A7B84100FF6708 /* UIImage+MultiFormat.m in Sources */,
				F37D4B0E2697D9B200A6740F /* MASConstraintMaker.m in Sources */,
				F30766F826A7B84100FF6708 /* SDImageHEICCoder.m in Sources */,
				F35013EF26E5F7FD00700B10 /* GPUImageColorDodgeBlendFilter.m in Sources */,
				F35013D526E5F7FD00700B10 /* GPUImageLowPassFilter.m in Sources */,
				F307671826A7B84100FF6708 /* SDDiskCache.m in Sources */,
				F3BDC3EC26C502240019A0F4 /* TingFadeOutView.m in Sources */,
				F307651C26A7B25E00FF6708 /* ESBFWaitView.m in Sources */,
				F350141B26E5F7FD00700B10 /* GPUImageFilter.m in Sources */,
				F350138E26E5F7FD00700B10 /* GPUImageColorInvertFilter.m in Sources */,
				F35013D626E5F7FD00700B10 /* GPUImageHoughTransformLineDetector.m in Sources */,
				F37D4B242697D9EB00A6740F /* ViewController.m in Sources */,
				A51C5C1B2D79A13D00266A15 /* TiDi.m in Sources */,
				F35013B726E5F7FD00700B10 /* GPUImageWeakPixelInclusionFilter.m in Sources */,
				F307673626A7B84100FF6708 /* SDWeakProxy.m in Sources */,
				F37D4B122697D9B200A6740F /* MASConstraint.m in Sources */,
				F35013BD26E5F7FD00700B10 /* GPUImageThresholdedNonMaximumSuppressionFilter.m in Sources */,
				A5D334812A59110700F67392 /* ZYEUpdateView.m in Sources */,
				F307653F26A7B53E00FF6708 /* AFSecurityPolicy.m in Sources */,
				F37D4B0B2697D9B200A6740F /* NSLayoutConstraint+MASDebugAdditions.m in Sources */,
				ABE8B6182DC1BCC0009C13D3 /* FaceAnalysisResultVC.m in Sources */,
				A5D334772A59110700F67392 /* UILabel+createLabels.m in Sources */,
				F307672A26A7B84100FF6708 /* SDWebImageCacheKeyFilter.m in Sources */,
				F307670426A7B84100FF6708 /* SDImageAWebPCoder.m in Sources */,
				F307671C26A7B84100FF6708 /* SDImageCache.m in Sources */,
				F307671A26A7B84100FF6708 /* UIImage+ExtendedCacheData.m in Sources */,
				F307673A26A7B84100FF6708 /* SDDisplayLink.m in Sources */,
				F350140626E5F7FD00700B10 /* GPUImageKuwaharaRadius3Filter.m in Sources */,
				A50B9DAD2C1991F8002C4109 /* BSEtWebUrlView.m in Sources */,
				F350141926E5F7FD00700B10 /* GPUImageBulgeDistortionFilter.m in Sources */,
				F35013AB26E5F7FD00700B10 /* GPUImageExposureFilter.m in Sources */,
				F307670C26A7B84100FF6708 /* UIImage+GIF.m in Sources */,
				F35013DF26E5F7FD00700B10 /* GPUImage3x3ConvolutionFilter.m in Sources */,
				F350139726E5F7FD00700B10 /* GPUImageHistogramGenerator.m in Sources */,
				F3581E8C26A9043E001B2DC7 /* FMDatabaseAdditions.m in Sources */,
				A50B9DAC2C1991F8002C4109 /* BSAgreementView.m in Sources */,
				F35013EB26E5F7FD00700B10 /* GPUImageHardLightBlendFilter.m in Sources */,
				F35013F726E5F7FD00700B10 /* GPUImageLinearBurnBlendFilter.m in Sources */,
				F35013A126E5F7FD00700B10 /* GPUImageRGBFilter.m in Sources */,
				F350140B26E5F7FD00700B10 /* GPUImageStretchDistortionFilter.m in Sources */,
				F307676F26A80C0E00FF6708 /* ImageManager.m in Sources */,
				F307673B26A7B84100FF6708 /* SDAsyncBlockOperation.m in Sources */,
				F35013CD26E5F7FD00700B10 /* GPUImageSharpenFilter.m in Sources */,
				F350140926E5F7FD00700B10 /* GPUImagePixellateFilter.m in Sources */,
				F35013ED26E5F7FD00700B10 /* GPUImageDifferenceBlendFilter.m in Sources */,
				F37D4B1B2697D9C000A6740F /* MyColor.m in Sources */,
				F35013F326E5F7FD00700B10 /* GPUImageMaskFilter.m in Sources */,
				F350140426E5F7FD00700B10 /* GPUImageSketchFilter.m in Sources */,
				F307672326A7B84100FF6708 /* SDImageTransformer.m in Sources */,
				F35013FF26E5F7FD00700B10 /* GPUImageDarkenBlendFilter.m in Sources */,
				F307670326A7B84100FF6708 /* UIImage+ForceDecode.m in Sources */,
				A418CF8C26A5622900AA67F8 /* StarView.m in Sources */,
				F307671E26A7B84100FF6708 /* SDImageFrame.m in Sources */,
				F35013DD26E5F7FD00700B10 /* GPUImagePrewittEdgeDetectionFilter.m in Sources */,
				ABA367A22DCB091D00E23A5C /* CopyLabel.m in Sources */,
				F35013D126E5F7FD00700B10 /* GPUImageColorLocalBinaryPatternFilter.m in Sources */,
				F307672D26A7B84100FF6708 /* UIView+WebCacheOperation.m in Sources */,
				F35013D926E5F7FD00700B10 /* GPUImageDirectionalNonMaximumSuppressionFilter.m in Sources */,
				F350142926E5F7FD00700B10 /* GPUImageRawDataInput.m in Sources */,
				A5DC045C2CB7693F006E281C /* FacePalmController.m in Sources */,
				F307672926A7B84100FF6708 /* SDImageCacheConfig.m in Sources */,
				F34A6C1526A6A121006DE0E0 /* SVProgressAnimatedView.m in Sources */,
				F350139B26E5F7FD00700B10 /* GPUImageAdaptiveThresholdFilter.m in Sources */,
				F350141326E5F7FD00700B10 /* GPUImageGlassSphereFilter.m in Sources */,
				F35013FE26E5F7FD00700B10 /* GPUImageLightenBlendFilter.m in Sources */,
				F350141526E5F7FD00700B10 /* GPUImageCrosshatchFilter.m in Sources */,
				F30766FB26A7B84100FF6708 /* SDImageCoder.m in Sources */,
				F35013A526E5F7FD00700B10 /* GPUImageContrastFilter.m in Sources */,
				F350142526E5F7FD00700B10 /* GPUImageOutput.m in Sources */,
				F350138626E5F7FD00700B10 /* GPUImageTwoPassTextureSamplingFilter.m in Sources */,
				F307670026A7B84100FF6708 /* UIImageView+HighlightedWebCache.m in Sources */,
				F35013B326E5F7FD00700B10 /* GPUImageSobelEdgeDetectionFilter.m in Sources */,
				F350142726E5F7FD00700B10 /* GPUImageColorConversion.m in Sources */,
				F35013CA26E5F7FD00700B10 /* GPUImageBoxBlurFilter.m in Sources */,
				F3BDC3ED26C502240019A0F4 /* TingSimpleScrollView.m in Sources */,
				ABE8B61B2DC1BFB5009C13D3 /* FaceAnalysisTabVC.m in Sources */,
				F350140126E5F7FD00700B10 /* GPUImageEmbossFilter.m in Sources */,
				F35013A226E5F7FD00700B10 /* GPUImageToneCurveFilter.m in Sources */,
				F307671226A7B84100FF6708 /* SDWebImageDownloaderResponseModifier.m in Sources */,
				F307670626A7B84100FF6708 /* UIImage+Transform.m in Sources */,
				F307670226A7B84100FF6708 /* SDAnimatedImageView.m in Sources */,
				F35013B526E5F7FD00700B10 /* GPUImageGaussianBlurPositionFilter.m in Sources */,
				F35013C926E5F7FD00700B10 /* GPUImageLaplacianFilter.m in Sources */,
				F307673C26A7B84100FF6708 /* SDAssociatedObject.m in Sources */,
				F3BDC3DF26C4F5610019A0F4 /* ImageTextButton.m in Sources */,
				F37D4B0F2697D9B200A6740F /* MASLayoutConstraint.m in Sources */,
				F307675426A7CA2200FF6708 /* CheckImageHaveFace.m in Sources */,
				F350139D26E5F7FD00700B10 /* GPUImageOpacityFilter.m in Sources */,
				A418CF8B26A5622900AA67F8 /* StarsView.m in Sources */,
				F34A6BE426A69899006DE0E0 /* CartoonViewController.m in Sources */,
				F35013AE26E5F7FD00700B10 /* GPUImageLookupFilter.m in Sources */,
				F35013AA26E5F7FD00700B10 /* GPUImageHighlightShadowFilter.m in Sources */,
				F350141E26E5F7FD00700B10 /* GLProgram.m in Sources */,
				A50B9DB02C1991F8002C4109 /* BSUserDefaultManager.m in Sources */,
				F307672F26A7B84100FF6708 /* UIImage+Metadata.m in Sources */,
				A5D3348B2A59132000F67392 /* IPChangeIconView.m in Sources */,
				F307673826A7B84100FF6708 /* SDFileAttributeHelper.m in Sources */,
				F307671526A7B84100FF6708 /* SDImageCoderHelper.m in Sources */,
				F307671426A7B84100FF6708 /* SDWebImagePrefetcher.m in Sources */,
				F37D4B302697DA0400A6740F /* HomeCell.m in Sources */,
				F350142E26E5F7FD00700B10 /* GPUImageView.m in Sources */,
				F30766FE26A7B84100FF6708 /* SDWebImageManager.m in Sources */,
				F350139F26E5F7FD00700B10 /* GPUImageBrightnessFilter.m in Sources */,
				F307678126A8201600FF6708 /* TimeTool.m in Sources */,
				F35013F526E5F7FD00700B10 /* GPUImageSubtractBlendFilter.m in Sources */,
				F307671626A7B84100FF6708 /* NSButton+WebCache.m in Sources */,
				F3581E9F26A90683001B2DC7 /* DatabaseManager.m in Sources */,
				F350140826E5F7FD00700B10 /* GPUImageToonFilter.m in Sources */,
				F37D4B312697DA0400A6740F /* MyImagePickerController.m in Sources */,
				F350142826E5F7FD00700B10 /* GPUImageMovieComposition.m in Sources */,
				F35013CE26E5F7FD00700B10 /* GPUImageGaussianSelectiveBlurFilter.m in Sources */,
				F35013E426E5F7FD00700B10 /* GPUImageThreeInputFilter.m in Sources */,
				F307671B26A7B84100FF6708 /* SDWebImageDownloaderConfig.m in Sources */,
				F37D4ABA2697D96300A6740F /* SceneDelegate.m in Sources */,
				A50B9D782C194F34002C4109 /* APPStoreIAPObserver.m in Sources */,
				A50B9DA92C1991F8002C4109 /* BSWaitingAnimationView.m in Sources */,
				F35013E226E5F7FD00700B10 /* GPUImageColourFASTSamplingOperation.m in Sources */,
				F350139526E5F7FD00700B10 /* GPUImageHueFilter.m in Sources */,
				F35013F026E5F7FD00700B10 /* GPUImageSourceOverBlendFilter.m in Sources */,
				F35013E126E5F7FD00700B10 /* GPUImageSingleComponentGaussianBlurFilter.m in Sources */,
				F307677B26A81CC700FF6708 /* FileManager.m in Sources */,
				F35013B226E5F7FD00700B10 /* GPUImageParallelCoordinateLineTransformFilter.m in Sources */,
				F35013F926E5F7FD00700B10 /* GPUImageHueBlendFilter.m in Sources */,
				F3286E5626A6D5E000C46DF1 /* PsychologicalViewController.m in Sources */,
				F307671926A7B84100FF6708 /* SDAnimatedImageRep.m in Sources */,
				F350142D26E5F7FD00700B10 /* GPUImageRawDataOutput.m in Sources */,
				F35013A626E5F7FD00700B10 /* GPUImageHazeFilter.m in Sources */,
				F35013F626E5F7FD00700B10 /* GPUImageColorBlendFilter.m in Sources */,
				F30766FF26A7B84100FF6708 /* SDImageIOAnimatedCoder.m in Sources */,
				F35013EC26E5F7FD00700B10 /* GPUImageDissolveBlendFilter.m in Sources */,
				F350142326E5F7FD00700B10 /* GPUImagePicture.m in Sources */,
				F35013E026E5F7FD00700B10 /* GPUImageNobleCornerDetectionFilter.m in Sources */,
				A5D3347C2A59110700F67392 /* ZYELabel.m in Sources */,
				F35013AF26E5F7FD00700B10 /* GPUImage3x3TextureSamplingFilter.m in Sources */,
				F35013C426E5F7FD00700B10 /* GPUImageRGBClosingFilter.m in Sources */,
				F307672B26A7B84100FF6708 /* SDWebImageDefine.m in Sources */,
				F350139626E5F7FD00700B10 /* GPUImageAmatorkaFilter.m in Sources */,
				F350139C26E5F7FD00700B10 /* GPUImageSaturationFilter.m in Sources */,
				F35013F226E5F7FD00700B10 /* GPUImagePoissonBlendFilter.m in Sources */,
				F307672526A7B84100FF6708 /* UIImageView+WebCache.m in Sources */,
				F30766FA26A7B84100FF6708 /* SDAnimatedImageView+WebCache.m in Sources */,
				F307670826A7B84100FF6708 /* SDImageLoader.m in Sources */,
				A429D1C826AA5AD700E145FD /* PalmsResultCircleMaskView.m in Sources */,
				F307673126A7B84100FF6708 /* SDWebImageDownloaderRequestModifier.m in Sources */,
				F35013C026E5F7FD00700B10 /* GPUImageZoomBlurFilter.m in Sources */,
				F307652426A7B26E00FF6708 /* UIView+Layout.m in Sources */,
				A5D334792A59110700F67392 /* PopView.m in Sources */,
				A50B9DAE2C1991F8002C4109 /* BSIntroduceView.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4AC92697D96300A6740F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F37D4AD22697D96400A6740F /* TimeMachineTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F37D4AD42697D96400A6740F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F37D4ADD2697D96400A6740F /* TimeMachineUITests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F37D4ACF2697D96300A6740F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F37D4AB12697D96300A6740F /* TimeMachine */;
			targetProxy = F37D4ACE2697D96300A6740F /* PBXContainerItemProxy */;
		};
		F37D4ADA2697D96400A6740F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F37D4AB12697D96300A6740F /* TimeMachine */;
			targetProxy = F37D4AD92697D96400A6740F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		A49CD67E26C9F62400F19C02 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				A49CD67D26C9F62400F19C02 /* en */,
				A49CD68926C9F6F600F19C02 /* zh-Hans */,
				F332A16D26CE283F00D4C1D9 /* de */,
				F332A17126CE2DCE00D4C1D9 /* es */,
				F332A17526CE33C900D4C1D9 /* ja */,
				F332A17926CE3B6A00D4C1D9 /* ko */,
				F332A17D26CE46BF00D4C1D9 /* zh-Hant */,
				F332A18126CE4F9800D4C1D9 /* fr */,
				F332A18526CE557200D4C1D9 /* ar */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		A49CD68626C9F6DE00F19C02 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				A49CD68526C9F6DE00F19C02 /* en */,
				A49CD68A26C9F6F800F19C02 /* zh-Hans */,
				F332A16E26CE284100D4C1D9 /* de */,
				F332A17226CE2DD000D4C1D9 /* es */,
				F332A17626CE33CC00D4C1D9 /* ja */,
				F332A17A26CE3B6D00D4C1D9 /* ko */,
				F332A17E26CE46C200D4C1D9 /* zh-Hant */,
				F332A18226CE4F9B00D4C1D9 /* fr */,
				F332A18626CE557400D4C1D9 /* ar */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		F37D4ABE2697D96300A6740F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F37D4ABF2697D96300A6740F /* Base */,
				A49CD68726C9F6F000F19C02 /* zh-Hans */,
				F332A16B26CE283A00D4C1D9 /* de */,
				F332A16F26CE2DC900D4C1D9 /* es */,
				F332A17326CE33C400D4C1D9 /* ja */,
				F332A17726CE3B6500D4C1D9 /* ko */,
				F332A17B26CE46BA00D4C1D9 /* zh-Hant */,
				F332A17F26CE4F9300D4C1D9 /* fr */,
				F332A18326CE556D00D4C1D9 /* ar */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		F37D4AC32697D96300A6740F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F37D4AC42697D96300A6740F /* Base */,
				A49CD68826C9F6F300F19C02 /* zh-Hans */,
				F332A16C26CE283C00D4C1D9 /* de */,
				F332A17026CE2DCB00D4C1D9 /* es */,
				F332A17426CE33C700D4C1D9 /* ja */,
				F332A17826CE3B6700D4C1D9 /* ko */,
				F332A17C26CE46BD00D4C1D9 /* zh-Hant */,
				F332A18026CE4F9600D4C1D9 /* fr */,
				F332A18426CE556F00D4C1D9 /* ar */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		F37D4ADF2697D96400A6740F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		F37D4AE02697D96400A6740F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F37D4AE22697D96400A6740F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 028C48365ED9935D7D141E02 /* Pods-TimeMachine.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				EXCLUDED_ARCHS = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREFIX_HEADER = TimeMachine/PrefixHeader.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					DLIB_JPEG_SUPPORT,
					DLIB_NO_GUI_SUPPORT,
					NDEBUG,
					DDLIB_USE_BLAS,
					DLIB_USE_LAPACK,
				);
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/TimeMachine/Dlib\"";
				INFOPLIST_FILE = TimeMachine/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/Release",
					"$(PROJECT_DIR)/DLIB/dlib/neon_test_build/Debug",
					"$(PROJECT_DIR)/DLIB/dlib/cpp11_test_build/Debug",
					"$(PROJECT_DIR)/DLIB",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/Release",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/neon_test_build/Debug",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/cpp11_test_build/Debug",
					"$(PROJECT_DIR)/TimeMachine/DLIB",
					"$(PROJECT_DIR)/TimeMachine/Dlib",
					"$(PROJECT_DIR)/dlib/Release-iphoneos",
				);
				MARKETING_VERSION = 1.7.0;
				OTHER_CFLAGS = (
					"-DDLIB_JPEG_SUPPORT",
					"-DDLIB_NO_GUI_SUPPORT",
					"-DNDEBUG",
					"-DDLIB_USE_BLAS",
					"-DDLIB_USE_LAPACK",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.cecexiangji.appname;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "TimeMachine-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F37D4AE32697D96400A6740F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 83182782C7C73993F345BC3A /* Pods-TimeMachine.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = W83W7DVU38;
				EXCLUDED_ARCHS = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				GCC_PREFIX_HEADER = TimeMachine/PrefixHeader.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					DLIB_JPEG_SUPPORT,
					DLIB_NO_GUI_SUPPORT,
					NDEBUG,
					DDLIB_USE_BLAS,
					DLIB_USE_LAPACK,
				);
				HEADER_SEARCH_PATHS = "\"$(SRCROOT)/TimeMachine/Dlib\"";
				INFOPLIST_FILE = TimeMachine/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/DLIB/dlib/Release",
					"$(PROJECT_DIR)/DLIB/dlib/neon_test_build/Debug",
					"$(PROJECT_DIR)/DLIB/dlib/cpp11_test_build/Debug",
					"$(PROJECT_DIR)/DLIB",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/i386/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphonesimulator/dlib.build/Objects-normal/x86_64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Release-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/armv7/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/dlib.build/Debug-iphoneos/dlib.build/Objects-normal/arm64/Binary",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/Release",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/neon_test_build/Debug",
					"$(PROJECT_DIR)/TimeMachine/DLIB/dlib/cpp11_test_build/Debug",
					"$(PROJECT_DIR)/TimeMachine/DLIB",
					"$(PROJECT_DIR)/TimeMachine/Dlib",
					"$(PROJECT_DIR)/dlib/Release-iphoneos",
				);
				MARKETING_VERSION = 1.7.0;
				OTHER_CFLAGS = (
					"-DDLIB_JPEG_SUPPORT",
					"-DDLIB_NO_GUI_SUPPORT",
					"-DNDEBUG",
					"-DDLIB_USE_BLAS",
					"-DDLIB_USE_LAPACK",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.cecexiangji.appname;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "TimeMachine-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F37D4AE52697D96400A6740F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BC0CF8C846A6428437E749E2 /* Pods-TimeMachineTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = TimeMachineTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.TimeMachineTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeMachine.app/TimeMachine";
			};
			name = Debug;
		};
		F37D4AE62697D96400A6740F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D4EE2D4F5B446E3291182DA2 /* Pods-TimeMachineTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = TimeMachineTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.4;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.TimeMachineTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TimeMachine.app/TimeMachine";
			};
			name = Release;
		};
		F37D4AE82697D96400A6740F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6B503A42498A43226238314F /* Pods-TimeMachine-TimeMachineUITests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = TimeMachineUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.TimeMachineUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TimeMachine;
			};
			name = Debug;
		};
		F37D4AE92697D96400A6740F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EED86555F594696A0F39EE57 /* Pods-TimeMachine-TimeMachineUITests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				INFOPLIST_FILE = TimeMachineUITests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.fullstack.TimeMachineUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TimeMachine;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F37D4AAD2697D96300A6740F /* Build configuration list for PBXProject "TimeMachine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F37D4ADF2697D96400A6740F /* Debug */,
				F37D4AE02697D96400A6740F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F37D4AE12697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachine" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F37D4AE22697D96400A6740F /* Debug */,
				F37D4AE32697D96400A6740F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F37D4AE42697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachineTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F37D4AE52697D96400A6740F /* Debug */,
				F37D4AE62697D96400A6740F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F37D4AE72697D96400A6740F /* Build configuration list for PBXNativeTarget "TimeMachineUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F37D4AE82697D96400A6740F /* Debug */,
				F37D4AE92697D96400A6740F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F37D4AAA2697D96300A6740F /* Project object */;
}
