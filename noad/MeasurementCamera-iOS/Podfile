# Uncomment the next line to define a global platform for your project
# platform :ios, '9.0'

target 'TimeMachine' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!
  pod 'Ads-CN'#广告
  pod 'BlocksKit', :git =>'https://github.com/Tioks/BlocksKit'#block回调简化代码
  # Pods for TimeMachine
  pod 'AFNetworking'
  pod 'UMCommon'
  pod 'UMDevice'
  pod 'UMAPM'
  pod 'BRPickerView'
  pod 'AliyunOSSiOS'
  pod 'Masonry'
  pod 'WMPageController'
  pod 'Toast'
  pod 'OpenCV2'
  
  target 'TimeMachineTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'TimeMachineUITests' do
    # Pods for testing
  end

end
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
    end
  end
end
