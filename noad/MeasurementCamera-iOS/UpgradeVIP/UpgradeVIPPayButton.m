//
//  UpgradeVIPPayButton.m
//  AudioEdit
//
//  Created by FS003 on 2021/7/14.
//

#import "UpgradeVIPPayButton.h"

@implementation UpgradeVIPPayButton

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        
        if (self.maskPicView == nil) {
            self.maskPicView = [[UIView alloc] initWithFrame:self.bounds];
            self.maskPicView.backgroundColor = [MyColor colorWithHexString:@"#C4967F" alpha:0.3];
            self.maskPicView.layer.cornerRadius = 6.0;
            [self addSubview:self.maskPicView];
        }
        
        if (self.imgView == nil) {
            self.imgView = [[UIImageView alloc] initWithFrame:CGRectMake(frame.size.width-32, (frame.size.height-21)/2, 21, 21)];
            self.imgView.image = [UIImage imageNamed:@"订阅未选择"];
            self.imgView.contentMode = UIViewContentModeScaleAspectFit;
            [self addSubview:self.imgView];
        }
        
        if (self.priceLabel == nil) {
            self.priceLabel = [[UILabel alloc] initWithFrame:CGRectMake(12, (frame.size.height-30)/2, 100, 30)];
            self.priceLabel.font = [UIFont systemFontOfSize:29.0];
            self.priceLabel.textColor = [UIColor whiteColor];
            [self addSubview:self.priceLabel];
        }
        
        if (self.titleLabel == nil) {
            self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(110, 13, frame.size.width-160, 16)];
            self.titleLabel.font = [UIFont systemFontOfSize:15.0];
            self.titleLabel.textColor = [UIColor whiteColor];
            [self addSubview:self.titleLabel];
        }
        
        if (self.descLabel == nil) {
            self.descLabel = [[UILabel alloc] initWithFrame:CGRectMake(110, frame.size.height-30, frame.size.width-160, 24)];
            self.descLabel.font = [UIFont systemFontOfSize:10.0];
            self.descLabel.textColor = [UIColor whiteColor];
            self.descLabel.backgroundColor = [UIColor clearColor];
            self.descLabel.numberOfLines = 0;
            [self addSubview:self.descLabel];
        }
        
        if (self.button == nil) {
            self.button = [UIButton buttonWithType:UIButtonTypeCustom];
            [self.button setFrame:self.bounds];
            [self.button setSelected:NO];
            self.button.backgroundColor = [UIColor clearColor];
            [self addSubview:self.button];
            [self.button addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
        }
    }
    return self;
}

- (void)setPriceText:(NSString *)priceText
{
    if ([priceText containsString:@"¥"]) {
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:priceText];
        [priceAttributedString addAttribute:NSFontAttributeName
                                   value:[UIFont boldSystemFontOfSize:16.0]
                                   range:NSMakeRange(0, 1)];
        self.priceLabel.attributedText = priceAttributedString;
    } else {
        NSString *priceString = [NSString stringWithFormat:@"¥%@", priceText];
        NSMutableAttributedString *priceAttributedString = [[NSMutableAttributedString alloc] initWithString:priceString];
        [priceAttributedString addAttribute:NSFontAttributeName
                                   value:[UIFont boldSystemFontOfSize:16.0]
                                   range:NSMakeRange(0, 1)];
        self.priceLabel.attributedText = priceAttributedString;
    }
}

- (void)buttonClick:(UIButton *)sender
{
    //NSLog(@"tag == %ld", self.tag);
    if (sender.isSelected == YES) {
        return;
    }
    NSInteger tag = self.tag;
    if ([self.delegate respondsToSelector:@selector(upgradeVIPPayButtonClick:)]) {
        [self.delegate upgradeVIPPayButtonClick:tag];
    }
}

- (void)setSelected:(BOOL)isSelected
{
    if (isSelected == YES) {
        [self.button setSelected:YES];
        _imgView.image = [UIImage imageNamed:@"订阅已选择"];
        self.maskPicView.alpha = 1.0;
    } else {
        [self.button setSelected:NO];
        _imgView.image = [UIImage imageNamed:@"订阅未选择"];
        self.maskPicView.alpha = 0.0;
    }
}


@end
