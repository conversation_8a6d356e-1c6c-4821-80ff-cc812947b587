//
//  UpgradeVIPViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/8/18.
//

#import "UpgradeVIPViewController.h"
#import "TingCycleScrollView.h"
#import "UpgradeVIPPayButton.h"
#import <StoreKit/StoreKit.h>
#import "APPMakeStoreIAPManager.h"

@interface UpgradeVIPViewController () <TingCycleScrollViewDatasource, UpgradeVIPPayButtonDelegate, MKStoreKitDelegate>
{
    NSTimer *timer;
    int timeCount;
}

@property (nonatomic, strong) UIScrollView *myScrollView;
@property (nonatomic, strong) TingCycleScrollView *cycleScrollView;
@property (nonatomic, strong) NSArray *imageList;

@property (nonatomic) NSInteger currentTypeSelected;  // 当前选择的付款类型
@property (nonatomic, strong) NSArray *payTypeList;   // 付款类型数组

@property (nonatomic, strong) NSArray *explainArray;

@property NSString* choose;

@end

@implementation UpgradeVIPViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.currentTypeSelected = 0;
    
    [self initBgImgView];
    
    if (self.imageList == nil) {
        self.imageList = [NSArray arrayWithObjects:@{@"title":NSLocalizedString(@"CartoonEffects", nil), @"image":@"订阅顶图1"}, @{@"title":NSLocalizedString(@"MagicFaceChange", nil), @"image":@"订阅顶图2"}, @{@"title":NSLocalizedString(@"PalmReading", nil), @"image":@"订阅顶图3"}, @{@"title":NSLocalizedString(@"PsychologicalTest", nil), @"image":@"订阅顶图4"}, nil];
    }
    
    self.payTypeList = [NSArray arrayWithObjects:@{@"title":NSLocalizedString(@"ContinuouslyYear", nil)}, @{@"title":NSLocalizedString(@"ContinuouslyMonth", nil)}, @{@"title":NSLocalizedString(@"LifetimeValidity", nil)}, nil];
    
    if (self.explainArray == nil) {
        self.explainArray = [NSArray arrayWithObjects:NSLocalizedString(@"UpgradeVIPResumeSubscribe", nil), NSLocalizedString(@"UpgradeVIPTermsOfUse", nil), NSLocalizedString(@"UpgradeVIPPrivacyPolicy", nil), nil];
    }
    
    if (self.myScrollView == nil) {
        self.myScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, kStatusBarHeight, kScreenWidth, kScreenHeight-kStatusBarHeight-kBottomSafeHeight)];
        self.myScrollView.bounces = NO;
        [self.view addSubview:self.myScrollView];
    }
    
    [self initBackButton];
    
    [self initViews];
    
    //请求价格
    [self refreshPrice];
    NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    if (!weekPrice) {
        [[APPMakeStoreIAPManager sharedManager] requestProductData];
        //[[ProgressViewUserInfo shareUserInfoArray] showProgressView:self.view labelText:nil];
        timeCount = 0;
        timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(refreshPrice) userInfo:nil repeats:YES];
    }
}

- (void)initViews
{
    if (self.cycleScrollView == nil) {
        self.cycleScrollView = [[TingCycleScrollView alloc] initWithFrame:CGRectMake(0, 60, kScreenWidth, kScreenWidth * 0.77)];
        self.cycleScrollView.datasource = self;
        self.cycleScrollView.pageControl.pageIndicatorTintColor = [MyColor colorWithHexString:@"#FFFFFF" alpha:0.36];
        self.cycleScrollView.pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
        [self.myScrollView addSubview:self.cycleScrollView];
    }
    
    // 选择订阅时间  价格
    CGFloat upgradeVIPPayButtonOriginY = _cycleScrollView.frame.origin.y + _cycleScrollView.bounds.size.height + 20;
    for (int i = 0; i < _payTypeList.count; i++) {
        UIView *upgradeVIPButtonBgView = [[UIView alloc] init];
        upgradeVIPButtonBgView.tag = 100 + i;
        upgradeVIPButtonBgView.layer.cornerRadius = 6.0;
        upgradeVIPButtonBgView.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
        [_myScrollView addSubview:upgradeVIPButtonBgView];
        if (i == 0) {
            [upgradeVIPButtonBgView setFrame:CGRectMake(15, upgradeVIPPayButtonOriginY, kScreenWidth-30, 88)];
            // 最受欢迎
            UILabel *zshyLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 64, upgradeVIPButtonBgView.bounds.size.width, 24)];
            zshyLabel.text = NSLocalizedString(@"MostPopular", nil);
            zshyLabel.textColor = [UIColor whiteColor];
            zshyLabel.textAlignment = NSTextAlignmentCenter;
            zshyLabel.font = [UIFont systemFontOfSize:12.0];
            zshyLabel.backgroundColor = [MyColor colorWithHexString:@"#C4967F" alpha:1.0];
            zshyLabel.layer.cornerRadius = 6.0;
            zshyLabel.layer.masksToBounds = YES;
            zshyLabel.tag = 500;
            [upgradeVIPButtonBgView addSubview:zshyLabel];
        } else {
            [upgradeVIPButtonBgView setFrame:CGRectMake(15, upgradeVIPPayButtonOriginY + 106 + 80*(i-1), kScreenWidth-30, 64)];
        }
        
        UpgradeVIPPayButton *upgradeVIPPayButton = [[UpgradeVIPPayButton alloc] initWithFrame:CGRectMake(0, 0, upgradeVIPButtonBgView.bounds.size.width, 64)];
        upgradeVIPPayButton.delegate = self;
        upgradeVIPPayButton.tag = 1000+i;
        [upgradeVIPButtonBgView addSubview:upgradeVIPPayButton];
        [upgradeVIPPayButton setPriceText:@"¥0"];
        upgradeVIPPayButton.titleLabel.text = _payTypeList[i][@"title"];
        if (i == 0) {
            [upgradeVIPPayButton setSelected:YES];
            upgradeVIPPayButton.descLabel.text = [NSString stringWithFormat:@"%@¥0/%@,%@", NSLocalizedString(@"AutoRenewalSubscription", nil), NSLocalizedString(@"UpgradeVipYear", nil),local(@"免费试用3天")];
            
            upgradeVIPButtonBgView.layer.borderColor = [MyColor colorWithHexString:@"#E8CBA6" alpha:1.0].CGColor;
            upgradeVIPButtonBgView.layer.borderWidth = 1.2;
        } else if (i == 1) {
            [upgradeVIPPayButton setSelected:NO];
            upgradeVIPPayButton.descLabel.text = [NSString stringWithFormat:@"%@¥0/%@,%@", NSLocalizedString(@"AutoRenewalSubscription", nil), NSLocalizedString(@"UpgradeVipMonth", nil), NSLocalizedString(@"CanBeCancelled", nil)];
            
            upgradeVIPButtonBgView.layer.borderColor = [UIColor clearColor].CGColor;
        } else {
            [upgradeVIPPayButton setSelected:NO];
//            upgradeVIPPayButton.descLabel.text = NSLocalizedString(@"FreeForLimitedTime", nil);

            upgradeVIPButtonBgView.layer.borderColor = [UIColor clearColor].CGColor;
        }
    }
    UIView *upgradeVIPButtonBgView = (UIView *)[_myScrollView viewWithTag:102];
    UIView *subscribeView = [[UIView alloc] initWithFrame:CGRectMake(50, upgradeVIPButtonBgView.frame.origin.y + upgradeVIPButtonBgView.bounds.size.height + 20, kScreenWidth - 100, 50)];
    subscribeView.layer.cornerRadius = 25.0;
    subscribeView.layer.masksToBounds = YES;
    [_myScrollView addSubview:subscribeView];
    CAGradientLayer *gradientLayer = [CAGradientLayer layer];
    gradientLayer.colors = @[(__bridge id)[MyColor colorWithHexString:@"#FEECBF" alpha:1.0].CGColor, (__bridge id)[MyColor colorWithHexString:@"#B57F6F" alpha:1.0].CGColor];
    gradientLayer.locations = @[@0.0, @1.0];
    gradientLayer.startPoint = CGPointMake(0, 0);
    gradientLayer.endPoint = CGPointMake(1.0, 0.0);
    gradientLayer.frame = subscribeView.bounds;
    [subscribeView.layer addSublayer:gradientLayer];
    
    UIButton *subscribeButton = [[UIButton alloc] initWithFrame:subscribeView.bounds];
    [subscribeButton setTitle:NSLocalizedString(@"Continue", nil) forState:UIControlStateNormal];
    [subscribeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [subscribeButton.titleLabel setFont:[UIFont boldSystemFontOfSize:16.0]];
    [subscribeView addSubview:subscribeButton];
    [subscribeButton addTarget:self action:@selector(subscribeButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    
    // 恢复订阅  使用条款  隐私政策 按钮
    CGFloat explainButtonOriginY = subscribeView.frame.origin.y + subscribeView.bounds.size.height + 10;
    for (int i = 0; i < _explainArray.count; i++) {
        UIButton *explainButton = [[UIButton alloc] initWithFrame:CGRectMake(30+((kScreenWidth-60)/3)*i, explainButtonOriginY, (kScreenWidth-60)/3, 30)];
        [explainButton setTitle:_explainArray[i] forState:UIControlStateNormal];
        [explainButton setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
        [explainButton.titleLabel setFont:[UIFont systemFontOfSize:11.0]];
        explainButton.backgroundColor = [UIColor clearColor];
        explainButton.tag = 2000+i;
        [explainButton addTarget:self action:@selector(explainButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [_myScrollView addSubview:explainButton];
        if (i == 0  ||  i == 1) {
            UIView *line = [[UIView alloc] initWithFrame:CGRectMake(explainButton.bounds.size.width-1, (explainButton.bounds.size.height-8)/2, 1, 8)];
            line.backgroundColor = [UIColor grayColor];
            [explainButton addSubview:line];
        }
    }
    
    UILabel *tipsLabel = [[UILabel alloc] init];
    tipsLabel.text = NSLocalizedString(@"UpgradeVIPExplain1", nil);
    tipsLabel.textColor = [UIColor grayColor];
    tipsLabel.numberOfLines = 0;
    tipsLabel.textAlignment =NSTextAlignmentLeft;
    tipsLabel.font = [UIFont systemFontOfSize:10.0];
    [_myScrollView addSubview:tipsLabel];
    [tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(subscribeView.mas_bottom).with.offset(60);
        make.leading.equalTo(_myScrollView.mas_leading).with.offset(20);
        make.width.equalTo(@(_myScrollView.bounds.size.width-40));
    }];
    
    [_myScrollView layoutIfNeeded];
    
    [_myScrollView setContentSize:CGSizeMake(self.view.bounds.size.width, tipsLabel.frame.origin.y+tipsLabel.bounds.size.height+20)];
}

#pragma mark - TingCycleScrollViewDatasource
- (NSInteger)numberOfPagesInTingCycleScrollView:(TingCycleScrollView *)cycleScrollView
{
    if (cycleScrollView == _cycleScrollView) {
        return _imageList.count;
    }
    return 0;
}
- (UIView *)tingCycleScrollView:(TingCycleScrollView *)cycleScrollView pageAtIndex:(NSInteger)index
{
    if (cycleScrollView == _cycleScrollView) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:cycleScrollView.bounds];
        imageView.image = [UIImage imageNamed:_imageList[index][@"image"]];
        imageView.contentMode = UIViewContentModeScaleAspectFit;
        
        UILabel *label = [[UILabel alloc] init];
        label.text = _imageList[index][@"title"];
        label.textColor = [UIColor whiteColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.font = [UIFont systemFontOfSize:14.0];
        [imageView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(imageView.mas_bottom).with.offset(-30);
            make.centerX.equalTo(imageView.mas_centerX).with.offset(0);
            make.width.equalTo(@300);
            make.height.equalTo(@20);
        }];
        
        return imageView;
    }
    return [UIView new];
}


- (void)upgradeVIPPayButtonClick:(NSInteger)viewTag
{
    for (int i = 0; i < 3; i++) {
        UpgradeVIPPayButton *gradeVIPPayButton = (UpgradeVIPPayButton *)[_myScrollView viewWithTag:1000+i];
        [gradeVIPPayButton setSelected:NO];
        
        UIView *upgradeVIPButtonBgView = (UIView *)[_myScrollView viewWithTag:100+i];
        upgradeVIPButtonBgView.layer.borderColor = [UIColor clearColor].CGColor;
        upgradeVIPButtonBgView.layer.borderWidth = 1.2;
    }
    UpgradeVIPPayButton *gradeVIPPayButton = (UpgradeVIPPayButton *)[_myScrollView viewWithTag:viewTag];
    [gradeVIPPayButton setSelected:YES];
    
    _currentTypeSelected = viewTag-1000;
    
    UIView *upgradeVIPButtonBgView = (UIView *)[_myScrollView viewWithTag:100+_currentTypeSelected];
    upgradeVIPButtonBgView.layer.borderColor = [MyColor colorWithHexString:@"#E8CBA6" alpha:1.0].CGColor;
    
    UILabel *zshyLabel = (UILabel *)[_myScrollView viewWithTag:500];
    if (_currentTypeSelected > 0) {
        zshyLabel.backgroundColor = [MyColor colorWithHexString:@"#2D2D35" alpha:1.0];
    } else {
        zshyLabel.backgroundColor = [MyColor colorWithHexString:@"#C4967F" alpha:1.0];
    }
}


// 点击订阅按钮
- (void)subscribeButtonClick:(UIButton *)sender
{
    [SVProgressHUD show];
    NSLog(@"subscribeButtonClick");
    switch (_currentTypeSelected) {
        case 0:
        {
            [[APPMakeStoreIAPManager sharedManager] buyFeatureB];
            self.choose = @"com.cecexiangji.appname.year";
        }
            break;
        case 1:
        {
            [[APPMakeStoreIAPManager sharedManager] buyFeatureA];
            self.choose = @"com.cecexiangji.appname.month";
        }
            break;
        case 2:
        {
            [[APPMakeStoreIAPManager sharedManager] buyFeatureC];
            self.choose = @"com.cecexiangji.appname.permanent";
        }
            break;
            
        default:
            break;
    }
    [APPMakeStoreIAPManager sharedManager].delegate = self;
    //一个月
    /*
    if (YES) {
        [[APPMakeStoreIAPManager sharedManager] buyFeatureA];
    }else if (YES)//一年
    {
        [[APPMakeStoreIAPManager sharedManager] buyFeatureB];
    }else
    {
        [[APPMakeStoreIAPManager sharedManager] buyFeatureC];
    }
     */
}

- (void)refreshPrice
{
    timeCount++;
    NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
    NSString *yearPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureBId"];
    NSString *SVIPPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureCId"];
    //NSLog(@"weekPrice == %@   yearPrice == %@   SVIPPrice == %@", weekPrice, yearPrice, SVIPPrice);
    
    //NSString *weekPrice = @"¥39";
    //NSString *yearPrice = @"¥199";
    //NSString *SVIPPrice = @"¥399";
    
    if (weekPrice || timeCount > 10) {
        [timer invalidate];
        timer = nil;
        
        if (weekPrice) {
            //[self.monthView initialize:@"连续包月" and:weekPrice with:@""];
            UpgradeVIPPayButton *monthlyButton = (UpgradeVIPPayButton *)[_myScrollView viewWithTag:1001];
            if ([monthlyButton isKindOfClass:[UpgradeVIPPayButton class]]) {
                [monthlyButton setPriceText:weekPrice];
                
                monthlyButton.descLabel.text = [NSString stringWithFormat:@"%@%@/%@,%@", NSLocalizedString(@"AutoRenewalSubscription", nil), weekPrice, NSLocalizedString(@"UpgradeVipMonth", nil),local(@"免费试用3天")];
            }
            
            if (yearPrice) {
                //[self.yearView initialize:@"连续包年" and:yearPrice with:@""];
                UpgradeVIPPayButton *yearButton = (UpgradeVIPPayButton *)[_myScrollView viewWithTag:1000];
                if ([yearButton isKindOfClass:[UpgradeVIPPayButton class]]) {
                    [yearButton setPriceText:yearPrice];
                    
                    yearButton.descLabel.text = [NSString stringWithFormat:@"%@%@/%@,%@", NSLocalizedString(@"AutoRenewalSubscription", nil), yearPrice, NSLocalizedString(@"UpgradeVipYear", nil), NSLocalizedString(@"CanBeCancelled", nil)];
                }
            }
            
            if (SVIPPrice) {
                //[self.SVIPView initialize:@"终身有效" and:SVIPPrice with:@""];
                UpgradeVIPPayButton *slButton = (UpgradeVIPPayButton *)[_myScrollView viewWithTag:1002];
                if ([slButton isKindOfClass:[UpgradeVIPPayButton class]]) {
                    [slButton setPriceText:SVIPPrice];
                }
            }
        } else {
            UIAlertController *vc = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"Tips", nil) message:NSLocalizedString(@"UpgradeVIPPriceFailed", nil) preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *action = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                [self dismissViewControllerAnimated:YES completion:nil];
            }];
            [vc addAction:action];
            [self presentViewController:vc animated:YES completion:nil];
        }
    }
}

- (void)failed
{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@购买失败",self.from,self.choose]}];
    [SVProgressHUD dismiss];
   // [[ProgressViewUserInfo shareUserInfoArray] hiddenProgressView:self.view];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- ( void)buySuccessBack
{
    [SVProgressHUD dismiss];
   // [[ProgressViewUserInfo shareUserInfoArray] hiddenProgressView:self.view];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)productAPurchased
{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@",self.from,self.choose]}];
    [SVProgressHUD dismiss];
   // [[ProgressViewUserInfo shareUserInfoArray] hiddenProgressView:self.view];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)productBPurchased
{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@",self.from,self.choose]}];
    [SVProgressHUD dismiss];
   // [[ProgressViewUserInfo shareUserInfoArray] hiddenProgressView:self.view];
    [self dismissViewControllerAnimated:YES completion:nil];
}
- (void)productCPurchased
{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@",self.from,self.choose]}];
    [SVProgressHUD dismiss];
   // [[ProgressViewUserInfo shareUserInfoArray] hiddenProgressView:self.view];
    [self dismissViewControllerAnimated:YES completion:nil];
}

- (void)explainButtonClick:(UIButton *)sender
{
    NSInteger tag = sender.tag-2000;
    switch (tag) {
        case 0:
        {
            [APPMakeStoreIAPManager sharedManager].delegate = self;
            [[APPMakeStoreIAPManager sharedManager] restoreButClick:^{
                //[SVProgressHUD show];
            }];
        }
            break;
            
        case 1:
        {
            
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/live/************************************"] options:@{} completionHandler:nil];
            
        }
            break;
            
        case 2:
        {
                [[UIApplication sharedApplication] openURL:[NSURL URLWithString:@"https://www.freeprivacypolicy.com/live/************************************"] options:@{} completionHandler:nil];
            
        }
            break;
            
        default:
            break;
    }
}



@end
