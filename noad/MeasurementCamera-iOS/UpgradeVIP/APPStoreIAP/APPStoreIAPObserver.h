//
//  SecretSafeStoreIAPObserver.m
//
//  Created by <PERSON><PERSON><PERSON> on 17-Oct-09.
//  Copyright 2009 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <StoreKit/StoreKit.h>
typedef void (^checkReceiptCompleteResponseBlock)(NSString* response, NSError* error);
typedef void (^checkReceiptCompleteJsonBlock)(NSDictionary *json, NSError* error);
@interface APPStoreIAPObserver : NSObject<SKPaymentTransactionObserver> {

    
}
@property (nonatomic) BOOL production;
@property (nonatomic,copy) checkReceiptCompleteResponseBlock checkReceiptCompleteBlock;
@property (nonatomic,strong) NSMutableData* receiptRequestData;

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions;
- (void)failedTransaction:(SKPaymentTransaction *)transaction;
- (void)completeTransaction:(SKPaymentTransaction *)transaction;
- (void)restoreTransaction:(SKPaymentTransaction *)transaction;
- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue;
-(void)checkVipDate;
@property(nonatomic,strong)NSMutableArray * subscribeArray;
@end
