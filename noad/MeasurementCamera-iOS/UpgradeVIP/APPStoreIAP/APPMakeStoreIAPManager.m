
#import "APPMakeStoreIAPManager.h"
#import "NSDictionary+Value.h"

@implementation APPMakeStoreIAPManager

@synthesize purchasableObjects;
//@synthesize storeObserver;
@synthesize delegate;

// all your features should be managed one and only by StoreManager

static NSString *featureAId = @"com.cecexiangji.appname.month";//周订阅
static NSString *featureBId = @"com.cecexiangji.appname.year";//年订阅
static NSString *featureCId = @"com.cecexiangji.appname.permanent";//永久订阅

/*static NSString *featureAId = @"com.franze.testdev10";
static NSString *featureBId = @"com.franze.testdev11";
static NSString *featureCId = @"com.franze.testdev12";*/
//static NSString *featureJId = @"com.franze.testdev13";

BOOL featureCPurchased;

static APPMakeStoreIAPManager* _sharedStoreManager; // self


+ (BOOL) featureCPurchased {
    
    return featureCPurchased;
}

+ (APPMakeStoreIAPManager*)sharedManager
{
    @synchronized(self) {
        
        if (_sharedStoreManager == nil) {
            
            _sharedStoreManager = [[self alloc] init]; // assignment not done here
            _sharedStoreManager.purchasableObjects = [[NSMutableArray alloc] init];
            [_sharedStoreManager requestProductData];
            [APPMakeStoreIAPManager loadPurchases];
            _sharedStoreManager.storeObserver = [[APPStoreIAPObserver alloc] init];
            _sharedStoreManager.storeObserver.subscribeArray = [NSMutableArray arrayWithObjects:featureAId,featureBId,nil];
            [[SKPaymentQueue defaultQueue] addTransactionObserver:_sharedStoreManager.storeObserver];
            [_sharedStoreManager.storeObserver checkVipDate];
        }
    }
    return _sharedStoreManager;
}

+(void)onlyCheckVipDate
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [_sharedStoreManager.storeObserver checkVipDate];
    });
}
-(void)checkVipDate
{
    [self.storeObserver checkVipDate];
}
+ (BOOL)featureVip
{
//    return YES;
//#if DEBUG
//    return YES;
//    return YES;
//#endif
    if ([self featureCPurchased]) {
        return YES;
    }
    NSUserDefaults * userDefaults = [NSUserDefaults standardUserDefaults];
    NSDate * expiresDate = [userDefaults objectForKey:@"VipExpiresDate"];
    if (!expiresDate) {
        [userDefaults setInteger:0 forKey:@"Payment"];
        return NO;
    }
    NSDate * nowDate = [self getInternetDate];//[NSDate date];
    //更早的时间
    NSDate *earlierDate = [expiresDate earlierDate:nowDate];
    
    if ([earlierDate isEqualToDate:expiresDate] ) {
        //过期;
         [userDefaults setInteger:0 forKey:@"Payment"];
        return NO;
    }
    else
    {
        //还在订阅;
         [userDefaults setInteger:1 forKey:@"Payment"];
        return YES;
    }
    
}
#pragma mark --请求网络时间
+ (NSDate *)getInternetDate{
    NSString *urlString = @"http://m.baidu.com";
    urlString = [urlString stringByAddingPercentEscapesUsingEncoding: NSUTF8StringEncoding];
    // 实例化NSMutableURLRequest，并进行参数配置
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    [request setURL:[NSURL URLWithString: urlString]];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setTimeoutInterval: 2];
    [request setHTTPShouldHandleCookies:FALSE];
    [request setHTTPMethod:@"GET"];
    NSError *error = nil;
    NSHTTPURLResponse *response;
    [NSURLConnection sendSynchronousRequest:request
                          returningResponse:&response error:&error];
    if (error ||!response) {
        return [NSDate date];
    }
    NSString *date = [[response allHeaderFields] objectForKey:@"Date"];
    date = [date substringFromIndex:5];//index到这个字符串的结尾
    date = [date substringToIndex:[date length]-4];//从索引0到给定的索引index
    NSDateFormatter *dMatter = [[NSDateFormatter alloc] init];
    dMatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US"];
    [dMatter setDateFormat:@"dd MMM yyyy HH:mm:ss"];
    NSDate *netDate = [dMatter dateFromString:date];//[[dMatter dateFromString:date] dateByAddingTimeInterval:60*60*8];//时间差8小时
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    NSInteger interval = [zone secondsFromGMTForDate: netDate];
    netDate = [netDate  dateByAddingTimeInterval: interval];
    return netDate;
}
#pragma mark Singleton Methods

+ (id)allocWithZone:(NSZone *)zone

{
    @synchronized(self) {
        
        if (_sharedStoreManager == nil) {
            
            _sharedStoreManager = [super allocWithZone:zone];
            return _sharedStoreManager;  // assignment and return on first allocation
        }
    }
    
    return nil; //on subsequent allocation attempts return nil
}


- (id)copyWithZone:(NSZone *)zone
{
    return self;
}




- (void) requestProductData
{
    SKProductsRequest *request= [[SKProductsRequest alloc] initWithProductIdentifiers:
                                 [NSSet setWithObjects:featureAId,featureBId, featureCId,  nil]]; // add any other product here
    request.delegate = self;
    [request start];

}
- (void)restoreButClick:(void(^)())completionBlock
{
    [[SKPaymentQueue defaultQueue] restoreCompletedTransactions];
    completionBlock();
}
-(void)alertnoitems
{
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Message" message:@"You haven't purchased any items!"
                                                   delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
    [alert show];
}

- (void)productsRequest:(SKProductsRequest *)request didReceiveResponse:(SKProductsResponse *)response
{
    NSLog(@"come here...");
    [purchasableObjects addObjectsFromArray:response.products];
    for(int i=0;i<[purchasableObjects count];i++)
    {
        SKProduct *product = [purchasableObjects objectAtIndex:i];
        NSLog(@"Feature: %@, -----Cost: %f, ID: %@",[product localizedTitle],
              [[product price] doubleValue], [product productIdentifier]);
        NSString *price = [self getProductPrice:i];
        if ([[product productIdentifier] isEqual:featureAId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureAId"];
            [[NSNotificationCenter defaultCenter]postNotificationName:@"refreshPrice" object:nil];

        }
        
        if ([[product productIdentifier] isEqual:featureBId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureBId"];
        }
        
        if ([[product productIdentifier] isEqual:featureCId]) {
            [[NSUserDefaults standardUserDefaults] setObject:price forKey:@"featureCId"];
        //    [self AudioEditMember:price];
        }
    }
}



-(NSString*)getProductPrice:(int)index
{
    if (_sharedStoreManager&&_sharedStoreManager.purchasableObjects) {
        if (_sharedStoreManager.purchasableObjects.count > 0&&index >=0 && index <_sharedStoreManager.purchasableObjects.count) {
            SKProduct *product = [_sharedStoreManager.purchasableObjects objectAtIndex:index];
            NSNumberFormatter *numberFormatter = [[NSNumberFormatter alloc] init];
            [numberFormatter setFormatterBehavior:NSNumberFormatterBehavior10_4];
            [numberFormatter setNumberStyle:NSNumberFormatterCurrencyStyle];
            [numberFormatter setLocale:product.priceLocale];
            NSString *formattedPrice = [numberFormatter stringFromNumber:product.price];
            return formattedPrice;
        }
    }
    return @"$1.99";
}

- (void) buyFeatureA
{
    [self buyFeature:featureAId];
}

- (void) buyFeatureB
{
    [self buyFeature:featureBId];
}

- (void) buyFeatureC
{
    [self buyFeature:featureCId];
}

- (void) buyFeature:(NSString*) featureId
{
    if ([SKPaymentQueue canMakePayments])
    {
        SKPayment *payment = [SKPayment paymentWithProductIdentifier:featureId];
        [[SKPaymentQueue defaultQueue] addPayment:payment];
    }
    else
    {
        UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"MyApp" message:@"You are not authorized to purchase from AppStore"
                                                       delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
        [alert show];
    }
}

-(void)paymentCanceled
{
    if([delegate respondsToSelector:@selector(failed)])
        [delegate failed];
}

- (void) failedTransaction: (SKPaymentTransaction *)transaction
{
    if([delegate respondsToSelector:@selector(failed)])
        [delegate failed];
    NSString *messageToBeShown = [NSString stringWithFormat:@"Reason: %@, You can try: %@", [transaction.error localizedFailureReason], [transaction.error localizedRecoverySuggestion]];
    UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Unable to complete your purchase" message:messageToBeShown
                                                   delegate:self cancelButtonTitle:@"OK" otherButtonTitles: nil];
    [alert show];
}

-(void) provideContent: (NSString*) productIdentifier
{
    if([productIdentifier isEqualToString:featureAId])
    {
        featureCPurchased = YES;
        if([delegate respondsToSelector:@selector(productCPurchased)])
            [delegate productCPurchased];
    }
    
    [APPMakeStoreIAPManager updatePurchases];
    // [self refreshbuy];
}


+(void) loadPurchases
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    featureCPurchased = [userDefaults boolForKey:featureCId];
    
}
+(void) updatePurchases
{
    NSLog(@"updatePurchases------------");
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:featureCPurchased forKey:featureCId];
}
+(void) ChangeOldToBuyA:(BOOL)setbool
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:setbool forKey:featureAId];
    featureCPurchased = setbool;
}
+(void) updateAuyA
{
    NSUserDefaults *userDefaults = [NSUserDefaults standardUserDefaults];
    [userDefaults setBool:NO forKey:featureCId];
    featureCPurchased = NO;
}

@end
