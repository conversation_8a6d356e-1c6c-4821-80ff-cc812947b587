
#import <Foundation/Foundation.h>
#import "APPStoreIAPObserver.h"
#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN
@protocol MKStoreKitDelegate <NSObject>
@optional

- (void)productCPurchased;
- (void)failed;
-(void)buySuccessBack;
-(void)buycancelBack;
@end

@interface APPMakeStoreIAPManager : NSObject<SKProductsRequestDelegate> {
    
    NSMutableArray *purchasableObjects;
    APPStoreIAPObserver *storeObserver;
    
    id<MKStoreKitDelegate> delegate;
}
@property (nonatomic, retain) id<MKStoreKitDelegate> delegate;
@property (nonatomic, retain) NSMutableArray *purchasableObjects;
@property (nonatomic, retain) APPStoreIAPObserver *storeObserver;
- (void) requestProductData;
- (void) buyFeatureA;
- (void) buyFeatureB;
- (void) buyFeatureC;
- (void) buyFeature:(NSString*) featureId;

-(void)paymentCanceled;
-(void)refreshbuy;
- (void) failedTransaction: (SKPaymentTransaction *)transaction;
-(void) provideContent: (NSString*) productIdentifier;

+ (APPMakeStoreIAPManager*)sharedManager;
-(NSString*)getProductPrice:(int)index;
+ (BOOL) featureCPurchased;
+(void) loadPurchases;
+(void) updatePurchases;
- (void)restoreButClick:(void(^)())completionBlock;
-(void)alertnoitems;
+(void) updateAuyA;
+(void) ChangeOldToBuyA:(BOOL)setbool;
+(void)onlyCheckVipDate;
+(BOOL)featureVip;
-(void)checkVipDate;
@end

NS_ASSUME_NONNULL_END
