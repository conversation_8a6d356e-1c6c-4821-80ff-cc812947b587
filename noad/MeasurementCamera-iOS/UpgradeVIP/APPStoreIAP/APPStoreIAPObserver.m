//
//  SecretSafeStoreIAPObserver.m
//
//  Created by <PERSON><PERSON><PERSON> on 17-Oct-09.
//  Copyright 2009 <PERSON><PERSON><PERSON>. All rights reserved.
//

#import "APPStoreIAPObserver.h"
#import "APPMakeStoreIAPManager.h"
#import "NSString+Base64.h"

// 密钥
static NSString *sharedSecret = @"********************************";

#ifdef DEBUG
NSString * const storeUrl = @"https://sandbox.itunes.apple.com/verifyReceipt";
#else
NSString * const storeUrl = @"https://buy.itunes.apple.com/verifyReceipt";
#endif

NSString * const sandboxStoreUrl = @"https://sandbox.itunes.apple.com/verifyReceipt";
NSString * const sandboxSharedSecret = @"********************************";

@implementation APPStoreIAPObserver

- (void)paymentQueue:(SKPaymentQueue *)queue updatedTransactions:(NSArray *)transactions
{
    for (SKPaymentTransaction *transaction in transactions)
    {
        switch (transaction.transactionState)
        {
            case SKPaymentTransactionStatePurchased:
                
                [self completeTransaction:transaction];
                
                break;
                
            case SKPaymentTransactionStateFailed:
                
                [self failedTransaction:transaction];
                
                break;
                
            case SKPaymentTransactionStateRestored:
                
                [self restoreTransaction:transaction];
                
            default:
                
                break;
        }
    }
}
- (BOOL)paymentQueue:(SKPaymentQueue *)queue shouldAddStorePayment:(SKPayment *)payment forProduct:(SKProduct *)product {
    return YES;
}
- (void)failedTransaction: (SKPaymentTransaction *)transaction
{
    if (transaction.error.code != SKErrorPaymentCancelled)
    {
        // Optionally, display an error here.
    }
    [[APPMakeStoreIAPManager sharedManager] paymentCanceled];
    [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
}

- (void)checkVipDate
{
    
    NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
    NSData *receipt = [NSData dataWithContentsOfURL:receiptURL];
    
    [self checkReceipt_2:receipt andSharedSecret:sharedSecret onCompletion:^(NSDictionary *json, NSError *error) {
        if (error) {
            //[self changeCurrentPaymentStatus:nil haveSubscription:NO];
            return;
        }
        NSLog(@"json == %@", json);
        NSArray *array = json[@"latest_receipt_info"];
        NSMutableArray *subscribe = [NSMutableArray array];
        for (NSDictionary *dict in array) {
            if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                [subscribe addObject:dict];
           }
        }
        NSDictionary *dic = [array lastObject];
        
        NSString *expiresStr = dic[@"expires_date"];    //2016-12-29 08:09:11 Etc/GMT
        NSDateFormatter *fmt = [[NSDateFormatter alloc] init];
        fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
        //        [fmt setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"UTC"]];
        NSDate * expiresDate = [fmt dateFromString:expiresStr];
        if (!expiresDate) {
            //过期;
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults removeObjectForKey:CloudVipExpiresDate];
            return ;
        }
        NSDate *nowDate = [self getInternetDate];     //[NSDate date];//2016-12-29 09:44:34 +0000
        //更早的时间
        NSDate *earlierDate = [expiresDate earlierDate:nowDate];
        
        
        if ([earlierDate isEqualToDate:expiresDate] ) {
            //过期;
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults removeObjectForKey:CloudVipExpiresDate];
        } else {
            //还在订阅;
            [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults setObject:expiresDate forKey:CloudVipExpiresDate];
        }
    }];

    /*
    [self checkReceipt:receipt AndSharedSecret:sharedSecret onCompletion:^(NSString *response, NSError *error) {
        
        NSDictionary *rec = [self toJSON:response];
        NSLog(@"rec == %@", rec);
        NSArray *array = rec[@"latest_receipt_info"];
        NSMutableArray * subscribe = [NSMutableArray array];
        for (NSDictionary * dict in array) {
            if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                [subscribe addObject:dict];
           }
        }
        NSDictionary * dic = [array lastObject];
        
        NSString *expiresStr = dic[@"expires_date"];//2016-12-29 08:09:11 Etc/GMT
        NSDateFormatter * fmt = [[NSDateFormatter alloc] init];
        fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
        //        [fmt setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"UTC"]];
        NSDate * expiresDate = [fmt dateFromString:expiresStr];
        if (!expiresDate) {
            //过期;
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults removeObjectForKey:CloudVipExpiresDate];
            return ;
        }
        NSDate * nowDate = [self getInternetDate];//[NSDate date];//2016-12-29 09:44:34 +0000
        //更早的时间
        NSDate *earlierDate = [expiresDate earlierDate:nowDate];
        
        
        if ([earlierDate isEqualToDate:expiresDate] ) {
            //过期;
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults removeObjectForKey:CloudVipExpiresDate];
        } else {
            //还在订阅;
            [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:VipExpiresDate];
          //  [self.shareGroupUserDefaults setObject:expiresDate forKey:CloudVipExpiresDate];
        }
    }];     */
}
     
#pragma mark --请求网络时间
- (NSDate *)getInternetDate
{
    NSString *urlString = @"http://m.baidu.com";
    urlString = [urlString stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    // 实例化NSMutableURLRequest，并进行参数配置
    NSMutableURLRequest *request = [[NSMutableURLRequest alloc] init];
    [request setURL:[NSURL URLWithString:urlString]];
    [request setCachePolicy:NSURLRequestReloadIgnoringCacheData];
    [request setTimeoutInterval:5];
    [request setHTTPShouldHandleCookies:FALSE];
    [request setHTTPMethod:@"GET"];
    
    NSError *error = nil;
    NSHTTPURLResponse *response;
    [NSURLConnection sendSynchronousRequest:request
                          returningResponse:&response error:&error];

    // 处理返回的数据 20180928
    if (error ||!response) {
        return [NSDate date];
    }
    NSString *date = [[response allHeaderFields] objectForKey:@"Date"];
    date = [date substringFromIndex:5];//index到这个字符串的结尾
    date = [date substringToIndex:[date length]-4];//从索引0到给定的索引index
    NSDateFormatter *dMatter = [[NSDateFormatter alloc] init];
    dMatter.locale = [[NSLocale alloc] initWithLocaleIdentifier:@"en_US"];
    [dMatter setDateFormat:@"dd MMM yyyy HH:mm:ss"];
    NSDate *netDate = [dMatter dateFromString:date];//[[dMatter dateFromString:date] dateByAddingTimeInterval:60*60*8];//时间差8小时
    NSTimeZone *zone = [NSTimeZone systemTimeZone];
    NSInteger interval = [zone secondsFromGMTForDate:netDate];
    netDate = [netDate dateByAddingTimeInterval:interval];
    return [NSDate date];
}

- (void)checkReceiptAndFinishTransaction:(SKPaymentTransaction *)transaction productId:(NSString*)productId{
    if ([self.subscribeArray containsObject:productId]) {
        NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
        NSData *receipt = [NSData dataWithContentsOfURL:receiptURL];
        
        [self checkReceipt_2:receipt andSharedSecret:sharedSecret onCompletion:^(NSDictionary *json, NSError *error) {
            if (error) {
                //[self changeCurrentPaymentStatus:nil haveSubscription:NO];
                return;
            }
            //NSLog(@"json == %@", json);
            NSArray * array = json[@"latest_receipt_info"];
            NSMutableArray * subscribe = [NSMutableArray array];
            for (NSDictionary * dict in array) {
                if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                    [subscribe addObject:dict];
                }
            }
            NSDictionary * dic = [subscribe lastObject];

            NSString * expiresStr = dic[@"expires_date"];  //2016-12-29 08:09:11 Etc/GMT
            NSDateFormatter * fmt = [[NSDateFormatter alloc] init];
            fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
            //        [fmt setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"UTC"]];
            NSDate * expiresDate = [fmt dateFromString:expiresStr];
            if (!expiresDate) {
                [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
            }else
            {
                NSDate *nowDate = [self getInternetDate];    //[NSDate date];//2016-12-29 09:44:34 +0000
                //更早的时间
                NSDate *earlierDate = [expiresDate earlierDate:nowDate];
                if ([earlierDate isEqualToDate:expiresDate] ) {
                    //过期;
                    [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
                    
                }
                else
                {
                    //还在订阅;
                    [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:VipExpiresDate];
                    
                }
                
            }

            if([json[@"status"] integerValue]==0)
            {
                [[APPMakeStoreIAPManager sharedManager] provideContent: transaction.payment.productIdentifier];
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];

                //MYLog(@"SUCCESS %@",response);
            } else {
                [[APPMakeStoreIAPManager sharedManager] paymentCanceled];
                //MYLog(@"Fail");
            }
        }];
        
        /*
       [self checkReceipt:receipt AndSharedSecret:sharedSecret onCompletion:^(NSString *response, NSError *error) {
            NSDictionary* rec = [self toJSON:response];
            NSArray * array = rec[@"latest_receipt_info"];
            NSMutableArray * subscribe = [NSMutableArray array];
            for (NSDictionary * dict in array) {
                if ([self.subscribeArray containsObject:dict[@"product_id"]]) {
                    [subscribe addObject:dict];
                }
            }
            NSDictionary * dic = [subscribe lastObject];

            NSString * expiresStr = dic[@"expires_date"];  //2016-12-29 08:09:11 Etc/GMT
            NSDateFormatter * fmt = [[NSDateFormatter alloc] init];
            fmt.dateFormat = @"yyyy-MM-dd HH:mm:ss VV";
            //        [fmt setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"UTC"]];
            NSDate * expiresDate = [fmt dateFromString:expiresStr];
            if (!expiresDate) {
                [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
            }else
            {
                NSDate *nowDate = [self getInternetDate];    //[NSDate date];//2016-12-29 09:44:34 +0000
                //更早的时间
                NSDate *earlierDate = [expiresDate earlierDate:nowDate];
                if ([earlierDate isEqualToDate:expiresDate] ) {
                    //过期;
                    [[NSUserDefaults standardUserDefaults] removeObjectForKey:VipExpiresDate];
                    
                }
                else
                {
                    //还在订阅;
                    [[NSUserDefaults standardUserDefaults] setObject:expiresDate forKey:VipExpiresDate];
                    
                }
                
            }

            if([rec[@"status"] integerValue]==0)
            {
                [[APPMakeStoreIAPManager sharedManager] provideContent: transaction.payment.productIdentifier];
                [[SKPaymentQueue defaultQueue] finishTransaction: transaction];

                //MYLog(@"SUCCESS %@",response);
            }
            else {
                [[APPMakeStoreIAPManager sharedManager] paymentCanceled];
                //MYLog(@"Fail");
            }
        }]; */
    } else {
        [[APPMakeStoreIAPManager sharedManager] provideContent:transaction.payment.productIdentifier];
        [[SKPaymentQueue defaultQueue] finishTransaction:transaction];

    }
}
- (void)completeTransaction:(SKPaymentTransaction *)transaction
{
    
    NSString * productId = transaction.payment.productIdentifier;
    [self checkReceiptAndFinishTransaction:transaction productId:productId];
    return;
}

- (void)restoreTransaction:(SKPaymentTransaction *)transaction
{
    [self checkReceiptAndFinishTransaction:transaction productId:transaction.originalTransaction.payment.productIdentifier];
    
//    [[SecretStoreIAPManager sharedManager] provideContent: transaction.originalTransaction.payment.productIdentifier];
//    [[SKPaymentQueue defaultQueue] finishTransaction: transaction];
}
- (void)paymentQueueRestoreCompletedTransactionsFinished:(SKPaymentQueue *)queue
{
    if (queue.transactions.count <=0) {
        [[APPMakeStoreIAPManager sharedManager] alertnoitems];
        return;
    }
}

- (void)checkReceipt:(NSData*)receiptData AndSharedSecret:(NSString*)secretKey onCompletion:(checkReceiptCompleteResponseBlock)completion
{
    self.checkReceiptCompleteBlock = completion;
    
    NSError *jsonError = nil;
    NSString *receiptBase64 = [NSString base64StringFromData:receiptData length:[receiptData length]];
    
    
    NSData *jsonData = nil;
    
    if(secretKey != nil && ![secretKey isEqualToString:@""]) {
        jsonData = [NSJSONSerialization dataWithJSONObject:[NSDictionary dictionaryWithObjectsAndKeys:receiptBase64,@"receipt-data",
                                                            secretKey,@"password",
                                                            nil]
                                                   options:NSJSONWritingPrettyPrinted
                                                     error:&jsonError];
        
    } else {
        jsonData = [NSJSONSerialization dataWithJSONObject:[NSDictionary dictionaryWithObjectsAndKeys:
                                                            receiptBase64,@"receipt-data",
                                                            nil]
                                                   options:NSJSONWritingPrettyPrinted
                                                     error:&jsonError];
    }
    
    //    NSString* jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    
    //NSURL *requestURL = nil;
    //requestURL = [NSURL URLWithString:@"https://sandbox.itunes.apple.com/verifyReceipt"];
    //requestURL = [NSURL URLWithString:@"https://buy.itunes.apple.com/verifyReceipt"];
    
    NSURL *requestURL = [NSURL URLWithString:storeUrl];
    
    NSMutableURLRequest *req = [[NSMutableURLRequest alloc] initWithURL:requestURL];
    [req setHTTPMethod:@"POST"];
    [req setHTTPBody:jsonData];
    
    NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:req completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSString *dataStr = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSDictionary *rec = [self toJSON:dataStr];
        dispatch_async(dispatch_get_main_queue(), ^{
            switch ([rec[@"status"] integerValue]) {
                case 0:
                    completion(dataStr, error);
                    break;
                case 21007:
                {
                    /*
                    [self checkReceiptInSandbox:^(NSString *response, NSError *error) {
                        completion(response, error);
                    }];    */
                }
                    break;
                default:
                    completion(nil, error);
                    break;
            }
        });
    }];
    [task resume];
    
    
    /*
    NSURLConnection *conn = [[NSURLConnection alloc] initWithRequest:req delegate:self];
    if(conn) {
        self.receiptRequestData = [[NSMutableData alloc] init];
    } else {
        NSError* error = nil;
        NSMutableDictionary* errorDetail = [[NSMutableDictionary alloc] init];
        [errorDetail setValue:@"Can't create connection" forKey:NSLocalizedDescriptionKey];
        error = [NSError errorWithDomain:@"IAPHelperError" code:100 userInfo:errorDetail];
        if(_checkReceiptCompleteBlock) {
            _checkReceiptCompleteBlock(nil,error);
        }
    }    */
}


/// MARK: 验证收据_2
- (void)checkReceipt_2:(NSData *)receiptData andSharedSecret:(NSString *)secretKey onCompletion:(checkReceiptCompleteJsonBlock)completion
{
    NSError *jsonError = nil;
    NSString *receiptBase64 = [receiptData base64EncodedStringWithOptions:0];
    
    NSData *jsonData = nil;
    if(secretKey != nil && ![secretKey isEqualToString:@""]) {
        jsonData = [NSJSONSerialization dataWithJSONObject:[NSDictionary dictionaryWithObjectsAndKeys:receiptBase64, @"receipt-data", secretKey, @"password", nil] options:0 error:&jsonError];
    }
    else {
        jsonData = [NSJSONSerialization dataWithJSONObject:[NSDictionary dictionaryWithObjectsAndKeys:receiptBase64, @"receipt-data",nil] options:0 error:&jsonError];
    }
    NSURL *requestURL = [NSURL URLWithString:storeUrl];
    
    NSMutableURLRequest *req = [[NSMutableURLRequest alloc] initWithURL:requestURL];
    [req setHTTPMethod:@"POST"];
    [req setHTTPBody:jsonData];
    
    NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:req completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        
        NSString *dataStr = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSDictionary *rec = [self toJSON:dataStr];
        //NSLog(@"dataStr == %@", dataStr);
        dispatch_async(dispatch_get_main_queue(), ^{
            switch ([rec[@"status"] integerValue]) {
                case 0:
                    completion(rec, error);
                    break;
                case 21007:
                {
                    [self checkReceiptInSandbox:^(NSDictionary *json, NSError *error) {
                        completion(json, error);
                    }];
                }
                    break;
                default:
                    completion(nil, error);
                    break;
            }
            
        });
    }];
    
    [task resume];
}

/// MARK: 验证沙盒收据
- (void)checkReceiptInSandbox:(checkReceiptCompleteJsonBlock)completion
{
    NSURL *receiptURL = [[NSBundle mainBundle] appStoreReceiptURL];
    NSData *receiptData = [NSData dataWithContentsOfURL:receiptURL];
    NSString *receiptBase64 = [receiptData base64EncodedStringWithOptions:0];
    
    NSError *jsonError = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:[NSDictionary dictionaryWithObjectsAndKeys:receiptBase64, @"receipt-data", sandboxSharedSecret, @"password", nil] options:0 error:&jsonError];
    
    NSURL *requestURL = [NSURL URLWithString:sandboxStoreUrl];
    NSMutableURLRequest *req = [[NSMutableURLRequest alloc] initWithURL:requestURL];
    [req setHTTPMethod:@"POST"];
    [req setHTTPBody:jsonData];
    
    NSURLSessionTask *task = [[NSURLSession sharedSession] dataTaskWithRequest:req completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
        NSString *dataStr = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSDictionary *rec = [self toJSON:dataStr];
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(rec, error);
        });
    }];
    
    [task resume];
}

- (id)toJSON:(NSString *)json
{
    if(json == nil){
      return nil;
    }
    NSError* e = nil;
    id jsonObject = [NSJSONSerialization JSONObjectWithData:[json dataUsingEncoding:NSUTF8StringEncoding]
                                                    options:NSJSONReadingMutableContainers
                                                      error:&e];
    if(e==nil) {
        return jsonObject;
    }
    else {
        NSLog(@"%@",[e localizedDescription]);
        return nil;
    }
}

- (void)connection:(NSURLConnection *)connection didFailWithError:(NSError *)error {
    NSLog(@"Cannot transmit receipt data. %@",[error localizedDescription]);
    
    if(_checkReceiptCompleteBlock) {
        _checkReceiptCompleteBlock(nil,error);
    }
}

- (void)connection:(NSURLConnection *)connection didReceiveResponse:(NSURLResponse *)response {
    [self.receiptRequestData setLength:0];
}

- (void)connection:(NSURLConnection *)connection didReceiveData:(NSData *)data {
    [self.receiptRequestData appendData:data];
}

- (void)connectionDidFinishLoading:(NSURLConnection *)connection {
    NSString *response = [[NSString alloc] initWithData:self.receiptRequestData encoding:NSUTF8StringEncoding];
    if(_checkReceiptCompleteBlock) {
        _checkReceiptCompleteBlock(response,nil);
    }
}

@end

