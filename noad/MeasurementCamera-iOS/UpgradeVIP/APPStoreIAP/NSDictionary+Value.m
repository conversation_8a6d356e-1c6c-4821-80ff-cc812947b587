

#import "NSDictionary+Value.h"

@implementation NSDictionary (Value)

-(id)valueFromDictionaryWithNSNullCheck:(NSString *)key {
    NSObject* value = [self valueForKey:key];
    if([value isKindOfClass:[NSNull class]]) {
        value = nil;
    }
    return value;
}

-(BOOL)isValidValueWithKey:(NSString *)key {
    NSObject* value = [self valueForKey:key];
    if(value && ![value isKindOfClass:[NSNull class]]) {
        return YES;
    }
    return NO;
}
@end
