//
//  UpgradeVIPPayButton.h
//  AudioEdit
//
//  Created by FS003 on 2021/7/14.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UpgradeVIPPayButtonDelegate <NSObject>

- (void)upgradeVIPPayButtonClick:(NSInteger)viewTag;

@end

@interface UpgradeVIPPayButton : UIView

@property (nonatomic, strong) UIView *maskPicView;
@property (nonatomic, strong) UIImageView *imgView;
@property (nonatomic, strong) UILabel *priceLabel;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *descLabel;
@property (nonatomic, strong) UIButton *button;
@property (nonatomic, weak) id <UpgradeVIPPayButtonDelegate> delegate;

- (void)setPriceText:(NSString *)priceText;

- (void)setSelected:(BOOL)isSelected;

@end

NS_ASSUME_NONNULL_END
