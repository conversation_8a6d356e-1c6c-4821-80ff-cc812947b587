//
//  TingCycleScrollView.m
//  MyScrollView-Demo
//
//  Created by developer on 15/6/12.
//  Copyright (c) 2015年 developer. All rights reserved.
//

#import "TingCycleScrollView.h"

@interface TingCycleScrollView () <UIScrollViewDelegate>

@property (strong, nonatomic) UIScrollView *scrollView;
@property (nonatomic) NSInteger totalPages;
@property (strong, nonatomic) NSMutableArray *currentSubViews;

@end

@implementation TingCycleScrollView 

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        _currentPage = 0;

        if (!_currentSubViews) {
            _currentSubViews = [NSMutableArray array];
        }
        
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, frame.size.width, frame.size.height)];
        [_scrollView setShowsHorizontalScrollIndicator:NO];
        [_scrollView setShowsVerticalScrollIndicator:NO];
        [_scrollView setPagingEnabled:YES];
        [_scrollView setDelegate:self];
        [_scrollView setContentSize:CGSizeMake(frame.size.width*2, frame.size.height)];
        [_scrollView setContentOffset:CGPointMake(0, 0)];
        [self addSubview:_scrollView];
        
        _pageControl = [[UIPageControl alloc] initWithFrame:CGRectMake(0, frame.size.height-20, frame.size.width, 20)];
        [_pageControl setPageIndicatorTintColor:[UIColor whiteColor]];
        [_pageControl setCurrentPageIndicatorTintColor:[UIColor orangeColor]];
        [_pageControl setUserInteractionEnabled:NO];
        [self addSubview:_pageControl];
    }
    
    return self;
}

- (void)setDatasource:(id<TingCycleScrollViewDatasource>)datasource
{
    _datasource = datasource;
    [self reloadData];
}

- (void)reloadData
{
    _totalPages = [_datasource numberOfPagesInTingCycleScrollView:self];
    if (_totalPages <= 0) {
        return;
    }
    _pageControl.numberOfPages = _totalPages;
    [self refreshView];        
}

- (void)refreshView
{
    if (_scrollView.subviews.count > 0) {
        [_scrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    _pageControl.currentPage = _currentPage;
    [self obtainWillDisplayViewWithCurrentPage:_currentPage];
    [_scrollView setContentSize:CGSizeMake(_scrollView.bounds.size.width * _currentSubViews.count, _scrollView.bounds.size.height)];
    for (int i = 0; i < _currentSubViews.count; i++) {
        
        UIView *view = [_currentSubViews objectAtIndex:i];
        view.frame = CGRectOffset(view.frame, view.frame.size.width*i, 0);
        [_scrollView addSubview:view];
        
        UITapGestureRecognizer *recognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(recognizerClick:)];
        [view addGestureRecognizer:recognizer];
    }
    if (_totalPages > 1) {
        [_scrollView setContentOffset:CGPointMake(_scrollView.bounds.size.width, 0)];
    } else if (_totalPages == 1) {
        [_scrollView setContentOffset:CGPointMake(0, 0)];
    }
}

- (void)recognizerClick:(UITapGestureRecognizer *)recognizer
{
    if ([self.delegate respondsToSelector:@selector(tingCycleScrollView:clickAtIndex:)]) {
        [self.delegate tingCycleScrollView:self clickAtIndex:_currentPage];
    }
}

- (void)obtainWillDisplayViewWithCurrentPage:(NSInteger)currentPage
{
    if (_currentSubViews.count > 0) {
        [_currentSubViews removeAllObjects];
    }
    if (_totalPages > 1) {
        
        NSInteger last = [self obtainValidPage:currentPage-1];
        NSInteger next = [self obtainValidPage:currentPage+1];
        
        [_currentSubViews addObject:[_datasource tingCycleScrollView:self pageAtIndex:last]];
        [_currentSubViews addObject:[_datasource tingCycleScrollView:self pageAtIndex:currentPage]];
        [_currentSubViews addObject:[_datasource tingCycleScrollView:self pageAtIndex:next]];
        
    } else if (_totalPages == 1) {
        
        [_currentSubViews addObject:[_datasource tingCycleScrollView:self pageAtIndex:currentPage]];
    }
}

- (NSInteger)obtainValidPage:(NSInteger)page
{
    if (page == -1) {
        page = _totalPages-1;
    } else if (page == _totalPages) {
        page = 0;
    }
    
    return page;
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    CGFloat x = scrollView.contentOffset.x;
    if (x >= _scrollView.bounds.size.width*2) {
        _currentPage = [self obtainValidPage:_currentPage+1];
        [self refreshView];
    } else if (x <= 0) {
        _currentPage = [self obtainValidPage:_currentPage-1];
        [self refreshView];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    if (_totalPages > 1) {
        [_scrollView setContentOffset:CGPointMake(_scrollView.bounds.size.width, 0) animated:YES];
    } else if (_totalPages == 1) {
        [_scrollView setContentOffset:CGPointMake(0, 0)];
    }
}

@end
