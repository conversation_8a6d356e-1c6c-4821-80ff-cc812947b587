//
//  TingFadeOutView.m
//  IDou
//
//  Created by developer on 15/8/14.
//  Copyright (c) 2015年 developer. All rights reserved.
//

#import "TingFadeOutView.h"

#define screen_bounds [[UIScreen mainScreen] bounds]

static TingFadeOutView *sharedView = nil;

@interface TingFadeOutView ()

@property (strong, nonatomic) UIView *contentView;
@property (strong, nonatomic) UIView *bgView;

@end

@implementation TingFadeOutView

+ (TingFadeOutView *)sharedView
{
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        if (!sharedView) {
            sharedView = [[TingFadeOutView alloc] initWithFrame:screen_bounds];
            [sharedView setAlpha:0];
        }
    });
    
    return sharedView;
}

+ (void)showWithContentView:(UIView *)contentView maskAlpha:(CGFloat)alpha
{
    [[TingFadeOutView sharedView] addToWindowWithContentView:contentView maskAlpha:alpha];
}

+ (void)dismiss
{
    [[TingFadeOutView sharedView] hide];
}

- (void)addToWindowWithContentView:(UIView *)contentView maskAlpha:(CGFloat)alpha
{
    
    if (_bgView != nil) {
        
        [UIView animateWithDuration:0.01 animations:^{
            self.alpha = 0;
        } completion:^(BOOL finished) {
            
            [self->_contentView removeFromSuperview];
            self->_contentView = nil;
            
            [self->_bgView removeFromSuperview];
            self->_bgView = nil;
            
            if (!self->_bgView.superview) {
                
                self->_bgView = [[UIView alloc] initWithFrame:screen_bounds];
                [self->_bgView setBackgroundColor:[UIColor clearColor]];
                
                NSEnumerator *frontToBackWindows = [[[UIApplication sharedApplication] windows] reverseObjectEnumerator];
                
                for (UIWindow *window in frontToBackWindows) {
                    if (window.windowLevel == UIWindowLevelNormal) {
                        [window addSubview:self->_bgView];
                        break;
                    }
                }
            }
            
            if (!self.superview) {
                [self->_bgView addSubview:self];
            }
            
            [self addSubviewWithContentView:contentView maskAlpha:alpha];
        }];
        
    } else {
        
        if (!_bgView.superview) {
            
            _bgView = [[UIView alloc] initWithFrame:screen_bounds];
            [_bgView setBackgroundColor:[UIColor clearColor]];
            
            NSEnumerator *frontToBackWindows = [[[UIApplication sharedApplication] windows] reverseObjectEnumerator];
            
            for (UIWindow *window in frontToBackWindows) {
                if (window.windowLevel == UIWindowLevelNormal) {
                    [window addSubview:_bgView];
                    break;
                }
            }
            
            [_bgView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(hide)]];
        }
        
        if (!self.superview) {
            [_bgView addSubview:self];
        }
        
        [self addSubviewWithContentView:contentView maskAlpha:alpha];
    }
}

- (void)addSubviewWithContentView:(UIView *)contentView maskAlpha:(CGFloat)alpha
{
    [self setBackgroundColor:[UIColor colorWithRed:22.0/255 green:22.0/255 blue:22.0/255 alpha:alpha]];
    
    _contentView = contentView;
    [self addSubview:_contentView];
    _contentView.alpha = 0;
    
    [self startAnimation];
}

- (void)startAnimation
{
    [UIView animateWithDuration:0.3 animations:^{
        
        [self setAlpha:1.0];
        [self->_contentView setAlpha:1.0];
        
    }];
}

- (void)hide
{
    [UIView animateWithDuration:0.3 animations:^(void){
        
        [self setAlpha:0];
        [self->_contentView setAlpha:0];
        
    } completion:^(BOOL finished) {
        
        [self->_contentView removeFromSuperview];
        self->_contentView = nil;
        
        [self->_bgView removeFromSuperview];
        self->_bgView = nil;
    }];
}


@end
