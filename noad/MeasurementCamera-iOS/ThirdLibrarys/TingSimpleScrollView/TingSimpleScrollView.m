//
//  TingSimpleScrollView.m
//  LSWork-JJR
//
//  Created by ting<PERSON> on 2017/6/24.
//  Copyright © 2017年 tingwen Ye. All rights reserved.
//

#import "TingSimpleScrollView.h"

@interface TingSimpleScrollView () <UIScrollViewDelegate>

@property (strong, nonatomic) UIScrollView *scrollView;
@property (nonatomic) NSInteger totalPages;

@end

@implementation TingSimpleScrollView

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        _currentPage = 0;
        
        _scrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, 0, frame.size.width, frame.size.height)];
        [_scrollView setShowsHorizontalScrollIndicator:NO];
        [_scrollView setShowsVerticalScrollIndicator:NO];
        [_scrollView setPagingEnabled:YES];
        [_scrollView setDelegate:self];
        [_scrollView setContentOffset:CGPointMake(0, 0)];
        [_scrollView setBounces:NO];
        [self addSubview:_scrollView];
        
    }
    
    return self;
}

- (void)setDatasource:(id<TingSimpleScrollViewDatasource>)datasource
{
    _datasource = datasource;
    [self initView];
}

- (void)setCurrentPage:(NSInteger)currentPage
{
    _currentPage = currentPage;
    [_scrollView setContentOffset:CGPointMake(_scrollView.bounds.size.width*_currentPage, 0)];
}

- (void)initView
{
    _totalPages = [_datasource numberOfPagesInTingSimpleScrollView:self];
    if (_totalPages <= 0) {
        return;
    }
    [_scrollView setContentSize:CGSizeMake(_scrollView.bounds.size.width*_totalPages, _scrollView.bounds.size.height)];
    for (int i = 0; i < _totalPages; i++) {
        UIView *view = [_datasource tingSimpleScrollView:self pageAtIndex:i];
        view.frame = CGRectOffset(view.frame, view.frame.size.width*i, 0);
        [_scrollView addSubview:view];
    }
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView
{
    
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView
{
    CGFloat xPoint = scrollView.contentOffset.x;
    NSInteger curPage = (NSInteger)xPoint/_scrollView.bounds.size.width;
    _currentPage = curPage;
    if (self.callback) {
        self.callback(curPage);
    }
    //NSLog(@"x == %f", xPoint);
}


@end
