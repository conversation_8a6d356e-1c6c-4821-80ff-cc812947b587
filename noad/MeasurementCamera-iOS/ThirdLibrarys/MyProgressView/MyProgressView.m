//
//  AutoPlayLabel.m
//  LSWork-JJR
//
//  Created by ting<PERSON> on 2017/11/3.
//  Copyright © 2017年 tingwen Ye. All rights reserved.
//

#import "MyProgressView.h"

@implementation MyProgressView

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _trackTintView = [[UIView alloc] initWithFrame:self.bounds];
        _trackTintView.layer.cornerRadius = 2.0;
        _trackTintView.layer.masksToBounds = YES;
        [self addSubview:_trackTintView];
        
        _progressTintView = [[UIView alloc] init];
        _progressTintView.layer.cornerRadius = 2.0;
        _progressTintView.layer.masksToBounds = YES;
        [self addSubview:_progressTintView];
    }
    return self;
}

- (void)setTrackTintColor:(UIColor *)trackTintColor
{
    [_trackTintView setBackgroundColor:trackTintColor];
}

- (void)setProgressTintColor:(UIColor *)progressTintColor
{
    [_progressTintView setBackgroundColor:progressTintColor];
}

- (void)setProgress:(CGFloat)progress
{
    [_trackTintView setFrame:self.bounds];
    CGFloat width = self.bounds.size.width*progress;
    [_progressTintView setFrame:CGRectMake(0, 0, width, self.bounds.size.height)];
}


@end
