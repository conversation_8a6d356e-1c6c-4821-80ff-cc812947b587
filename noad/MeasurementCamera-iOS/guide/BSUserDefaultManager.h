//
//  BSUserDefaultManager.h
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <Foundation/Foundation.h>
NS_ASSUME_NONNULL_BEGIN

@interface BSUserDefaultManager : NSObject
/// 用户打开app的状态值
typedef NS_OPTIONS(NSUInteger, OpenAppState){
    /// 用户第一次打开app
    First_Open_App = 0,
    /// 用户已接受协议,但是没有进入过主界面
    Accept_Agreement = 1,
    /// 用户已经进入过一次主界面
    Goto_Main_Interface = 2,
};
/// 设置app打开状态
/// @param state 状态值(0:第一次打开app  1:已接受协议,但未进入主页 2:已进入主页)
+ (void)setOpenAppState:(OpenAppState)state;

// 获取app打开状态
/// @return 状态值(0:第一次打开app  1:已接受协议,但未进入主页 2:已进入主页)
+ (OpenAppState)getOpenAppState;

/// 是否存储登录用户
/// @return YES:已存在登录用户  NO:不存在登录用户
+ (BOOL)isExistLoginUser;



/// 退出用户登录,清空UserDefault本地存储的用户登录信息
+ (void)exitLoginUser;

@end

NS_ASSUME_NONNULL_END
