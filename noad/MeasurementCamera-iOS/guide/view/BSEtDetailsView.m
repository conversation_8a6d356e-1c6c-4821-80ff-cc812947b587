//
//  BSEtDetailsView.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSEtDetailsView.h"
@interface BSEtDetailsView ()
@property (nonatomic, strong) NSMutableArray <UILabel*> *detailsLabMarr;
@end
@implementation BSEtDetailsView
- (void)loadCustomWithDetailsArray:(NSArray*)details{
    self.detailsLabMarr = [NSMutableArray array];
    for (NSString *detail in details) {
        UILabel *detailLab = [[UILabel alloc] init];
        detailLab.text = detail;
        detailLab.font = [UIFont boldSystemFontOfSize:20];
        detailLab.textColor = [UIColor whiteColor];
        detailLab.numberOfLines = 1;
        detailLab.adjustsFontSizeToFitWidth = YES;
        [self addSubview:detailLab];
        [self.detailsLabMarr addObject:detailLab];
    }
    for (int i=0; i<self.detailsLabMarr.count; i++) {
        UILabel *detailLab = self.detailsLabMarr[i];
        [detailLab mas_makeConstraints:^(MASConstraintMaker *make) {
            //multipliedBy(参数)中参数不能我0,所以给了个0.0001作为i=0时的比率
            make.top.equalTo(self.mas_bottom).multipliedBy(0.0001+i*1.0/self.detailsLabMarr.count);
            make.left.equalTo(@0);
            make.right.equalTo(@0);
            make.height.equalTo(self.mas_height).multipliedBy(1.0/self.detailsLabMarr.count);
        }];
    }
}

@end
