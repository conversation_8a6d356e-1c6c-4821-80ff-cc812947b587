//
//  BSWaitingAnimationView.h
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface BSWaitingAnimationView : UIView
/// 实例化自定义等待试图
/// @param radius 小点半径
/// @param y 所在父试图的Y轴坐标
/// @param superView 所在父试图
/// @param text 文本内容
+ (instancetype)loadCustomWithRadius:(CGFloat)radius superView:(UIView*)superView y:(CGFloat)y text:(NSString*)text;

/// 开始动画
- (void)startAnimation;

/// 停止动画
- (void)stopAnimation;

@end

NS_ASSUME_NONNULL_END
