//
//  BSSubscribeRadio.h
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@protocol BSSubscribeRadioProtocol <NSObject>
/// 订阅选项控件的点击事件
/// @param tag 控件的标识码
- (void)subscribeRadioClickActionWithTag:(NSInteger)tag;
@end

@interface BSSubscribeRadio : UIView
/// 加载订阅选项框的自定义控件
/// @param content 订阅内容
/// @param tag 控件标识码
- (void)loadCustomContent:(NSString*)content tag:(NSInteger)tag;

/// 选中状态(YES:已选中  NO:未选中)
@property (readonly ,nonatomic, assign) BOOL isSelectedState;
/// 设置选中状态
/// @param state 选中状态(YES:已选中  NO:未选中)
- (void)setSelectRadioWithState:(BOOL)state;

@property(nonatomic, assign)id<BSSubscribeRadioProtocol>mainDelegate;
@end

NS_ASSUME_NONNULL_END
