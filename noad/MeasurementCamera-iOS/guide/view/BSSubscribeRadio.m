//
//  BSSubscribeRadio.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSSubscribeRadio.h"
@interface BSSubscribeRadio()
/// 试图核心控制选中按钮(作为整个自定义控件的填充背景)
@property (nonatomic, strong) UIButton *backgroundBtn;

/// 是否选中的图标试图
@property (nonatomic, strong) UIImageView *isSelectedIcon;

/// 已选中的图片对象
@property (nonatomic, strong) UIImage *selectedYesImg;

/// 未选中的图片对象
@property (nonatomic, strong) UIImage *selectedNoImg;

/// 是否选中的状态(YES:已选中  NO:未选中)
@property (nonatomic, assign) BOOL isSelectedState;

/// 内容的文本控件
@property (nonatomic, strong) UILabel *contentLab;
@end

@implementation BSSubscribeRadio
/// 加载订阅选项框的自定义控件
/// @param content 订阅内容
/// @param tag 控件标识码
- (void)loadCustomContent:(NSString*)content tag:(NSInteger)tag{
    self.backgroundBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    [self.backgroundBtn setBackgroundColor:BKgetColorFrom(35, 50, 75, 1.0)];
    self.backgroundBtn.layer.cornerRadius = 30.0f;
    self.backgroundBtn.layer.masksToBounds = YES;
    self.backgroundBtn.layer.borderWidth = 3.0f;
    self.backgroundBtn.layer.borderColor = [[UIColor clearColor] CGColor];
    self.backgroundBtn.tag = tag;
    [self addSubview:self.backgroundBtn];
    [self.backgroundBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.bottom.equalTo(@0);
    }];
    [self.backgroundBtn addTarget:self action:@selector(backgroundBtnAction:) forControlEvents:UIControlEventTouchUpInside];
    
    self.selectedYesImg = [UIImage imageNamed:@"SelectedYes"];
    self.selectedNoImg = [UIImage imageNamed:@"SelectedNo"];
    self.isSelectedIcon = [[UIImageView alloc] initWithImage:self.selectedNoImg];
    [self.backgroundBtn addSubview:self.isSelectedIcon];
    [self.isSelectedIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backgroundBtn.mas_centerY);
        make.left.equalTo(@10);
        make.width.equalTo(self.backgroundBtn.mas_height).multipliedBy(1.0f / 2.0f);
        make.height.equalTo(self.backgroundBtn.mas_height).multipliedBy(1.0f / 2.0f);
    }];
    
    self.contentLab = [[UILabel alloc] init];
    self.contentLab.text = content;
    self.contentLab.numberOfLines = 1;
    self.contentLab.adjustsFontSizeToFitWidth = YES;
    self.contentLab.textColor = [UIColor whiteColor];
    [self.backgroundBtn addSubview:self.contentLab];
    [self.contentLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backgroundBtn.mas_centerY);
        make.left.equalTo(self.isSelectedIcon.mas_right).offset(10);
        make.height.equalTo(self.backgroundBtn.mas_height);
        make.width.equalTo(self.backgroundBtn.mas_width).multipliedBy(0.8);
    }];
    
    self.userInteractionEnabled = YES;
}

/// 设置选中状态
/// @param state 选中状态(YES:已选中  NO:未选中)
- (void)setSelectRadioWithState:(BOOL)state{
    if (state) {
        self.isSelectedState = YES;
        [self.isSelectedIcon setImage:self.selectedYesImg];
        self.backgroundBtn.layer.borderColor = [BKgetColorFrom(242, 197, 0, 1.0) CGColor];
    } else {
        self.isSelectedState = NO;
        [self.isSelectedIcon setImage:self.selectedNoImg];
        self.backgroundBtn.layer.borderColor = [[UIColor clearColor] CGColor];
    }
}

/// 核心背景按钮的点击事件
/// @param sender 按钮对象
- (void)backgroundBtnAction:(UIButton*)sender{
    [self.mainDelegate subscribeRadioClickActionWithTag:sender.tag];
}


@end
