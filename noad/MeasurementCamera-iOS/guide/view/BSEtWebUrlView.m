//
//  BSEtWebUrlView.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSEtWebUrlView.h"
@interface BSEtWebUrlView()

@end
@implementation BSEtWebUrlView
/// 加载网页链接按钮的集合试图
/// @param titleArray 按钮名称的集合数组
/// @param tagArray 按钮标识码的集合数组
- (void)loadCustomWithTitleArray:(NSArray<NSString*>*)titleArray tagArray:(NSArray<NSNumber*>*)tagArray{
    self.webUrlBtnMarr = [NSMutableArray array];
    for (NSString *title in titleArray) {
        UIButton *webUrlBtn = [UIButton buttonWithType:UIButtonTypeSystem];
        [webUrlBtn setTitle:title forState:UIControlStateNormal];
        webUrlBtn.titleLabel.numberOfLines = 1;
        webUrlBtn.titleLabel.adjustsFontSizeToFitWidth = YES;
        [webUrlBtn setTitleColor:[UIColor grayColor] forState:UIControlStateNormal];
        [self loadBottonLineToButton:webUrlBtn];
        [self addSubview:webUrlBtn];
        [self.webUrlBtnMarr addObject:webUrlBtn];
    }
    
    for (int i=0; i<self.webUrlBtnMarr.count; i++) {
        UIButton *webUrlBtn = self.webUrlBtnMarr[i];
        [webUrlBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@0);
            make.bottom.equalTo(@0);
            make.width.equalTo(self.mas_width).multipliedBy(1.0f/self.webUrlBtnMarr.count);
            make.left.equalTo(self.mas_right).multipliedBy(0.0001+1.0f*i/self.webUrlBtnMarr.count).offset((i-1)*20);
        }];
        webUrlBtn.tag = [tagArray[i] integerValue];
        [webUrlBtn addTarget:self action:@selector(btnClick:) forControlEvents:UIControlEventTouchUpInside];
    }
}
-(IBAction)btnClick:(id)sender
{
    [self.mainDelegate buttonClickActionWithTag:((UIButton*)sender).tag];
}
/// 加载链接按钮的底部下划线
/// @param button 按钮对象
- (void)loadBottonLineToButton:(UIButton*)button{
    UIView *bottonBorder = [[UIView alloc] init];
    bottonBorder.backgroundColor = [UIColor grayColor];
    [button addSubview:bottonBorder];
    [bottonBorder mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(button.mas_bottom).offset(-1);
        make.centerX.equalTo(button.mas_centerX);
        make.width.equalTo(button.mas_width).multipliedBy(0.75);
        make.height.equalTo(@1);
    }];
}

@end
