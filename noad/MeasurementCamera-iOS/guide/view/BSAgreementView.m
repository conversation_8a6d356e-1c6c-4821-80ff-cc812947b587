//
//  BSAgreementView.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSAgreementView.h"
@interface BSAgreementView()
/// 核心背景试图
@property (nonatomic, strong) UIImageView *mainBackground;

/// [接受后继续]按钮控件
@property (nonatomic, strong) UIButton *mainBtn;

/// 标题的文本控件
@property (nonatomic, strong) UILabel *titleLab;

/// 详情的文本控件
@property (nonatomic, strong) UITextView *detailsLab;

@end
@implementation BSAgreementView
- (instancetype)initWithFrame:(CGRect)frame{
    self = [super initWithFrame:frame];
    if (self) {
        [self initUI];
    }
    return self;
}

- (void)initUI{
    /// 核心背景试图
    self.mainBackground = [[UIImageView alloc] initWithFrame:self.frame];
    
    [self.mainBackground setImage:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"AgreementBackground" ofType:@"jpg"]]];
    self.mainBackground.userInteractionEnabled = YES;
    [self addSubview:self.mainBackground];
    
    /// [接受后继续]按钮控件
    CGFloat mainBtnWidth = 0.8 * self.width;
    CGFloat mainBtnHeiht = 60.0f;
    CGFloat mainBtnX = self.width/2 - mainBtnWidth/2;
    CGFloat mainBtnY  = self.height - 130 - mainBtnHeiht;
    self.mainBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    self.mainBtn.frame = CGRectMake(mainBtnX, mainBtnY, mainBtnWidth, mainBtnHeiht);
    [self.mainBtn setTitle:NSLocalizedString(@"接受后继续", nil) forState:UIControlStateNormal];
    [self.mainBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    self.mainBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [self.mainBtn setBackgroundColor:[UIColor whiteColor]];
    self.mainBtn.layer.cornerRadius = 30;
    self.mainBtn.layer.masksToBounds = YES;
    [self.mainBackground addSubview:self.mainBtn];
    [self.mainBtn addTarget:self action:@selector(mainBtnAction) forControlEvents:UIControlEventTouchUpInside];
    
    UIButton* declineBtn = [UIButton buttonWithType:UIButtonTypeSystem];
    declineBtn.frame = CGRectMake(mainBtnX, mainBtnY+70, mainBtnWidth, mainBtnHeiht);
    [declineBtn setTitle:NSLocalizedString(@"拒绝", nil) forState:UIControlStateNormal];
    [declineBtn setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    declineBtn.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [declineBtn setBackgroundColor:[UIColor whiteColor]];
    declineBtn.layer.cornerRadius = 30;
    declineBtn.layer.masksToBounds = YES;
    [self.mainBackground addSubview:declineBtn];
    [declineBtn addTarget:self action:@selector(declineBtn) forControlEvents:UIControlEventTouchUpInside];
    
    /// 标题的文本控件
    self.titleLab = [[UILabel alloc] init];
    self.titleLab.text = NSLocalizedString(@"欢迎使用Mobile Clone", nil);
    self.titleLab.textAlignment = NSTextAlignmentCenter;
    self.titleLab.font = [UIFont boldSystemFontOfSize:20.0f];
    self.titleLab.textColor = [UIColor whiteColor];
    [self.mainBackground addSubview:self.titleLab];
    [self.titleLab mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mainBackground.mas_centerX);
        make.bottom.equalTo(self.mainBackground.mas_bottom).offset(-2.1f/4 * SCREENH_HEIGHT);
        make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.7);
        make.height.equalTo(@30);
    }];
    
    /// 详情的文本控件
    UIFont *linkFont = [UIFont systemFontOfSize:18.0];
       
   self.detailsLab = [[UITextView alloc]init];

   self.detailsLab.userInteractionEnabled = YES;
   self.detailsLab.font = linkFont;
   
   
   self.detailsLab.editable = NO;//必须禁止输入，否则点击将弹出输入键盘
   self.detailsLab.scrollEnabled = NO;
   self.detailsLab.backgroundColor = [UIColor clearColor];
   self.detailsLab.textContainerInset = UIEdgeInsetsMake(0,0, 0, 0);//文本距离边界值
   NSString *linkStr =NSLocalizedString( @"我们希望提供服务并确保用户隐私,请了解我们如何处理有关您或您的设备数据,继续即表示您确认承认并接受我们的隐私政策。",nil);
   //    NSString *linkStr = NSLocalizedString(@"We want to provide transparency and ensure user privacy. Please understand that we How to process data about you or your device.To continue means that you are sure Acknowledge and accept our privacy policy.", nil);

   CGFloat linkW = SCREEN_WIDTH - 15*2;
   CGSize linkSize = [self getAttributionHeightWithString:linkStr lineSpace:1.5 kern:1 font:linkFont width:linkW];
   self.detailsLab.frame = CGRectMake(10, 300, linkW, linkSize.height);
   
   NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:linkStr];
   [attributedString addAttribute:NSLinkAttributeName value:@"https://www.freeprivacypolicy.com/privacy/view/2e6556241582cf0a67fbb9b06cd98b0d" range:[[attributedString string] rangeOfString:NSLocalizedString(@"Privacy Policy",nil)]];
  
   
   NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
   //调整行间距
   paragraphStyle.lineSpacing = 1.5;
   NSDictionary *attriDict = @{NSParagraphStyleAttributeName:paragraphStyle,NSKernAttributeName:@(1),
                               NSFontAttributeName:linkFont};
   [attributedString addAttributes:attriDict range:NSMakeRange(0, attributedString.length)];
   
   self.detailsLab.attributedText = attributedString;
    UIColor * pricolor = [UIColor colorWithRed:13.0/255.0 green:123.0/255.0 blue:227.0/255.0 alpha:1.0];
    self.detailsLab.linkTextAttributes = @{NSForegroundColorAttributeName: pricolor, NSUnderlineColorAttributeName:pricolor, NSUnderlineStyleAttributeName: @(NSUnderlineStyleSingle)};
   /// 详情的文本控件
   self.detailsLab.textColor = [UIColor whiteColor];
   [self.mainBackground addSubview:self.detailsLab];
   [self.mainBackground sendSubviewToBack:self.detailsLab];
   [self.detailsLab mas_makeConstraints:^(MASConstraintMaker *make) {
       make.centerX.equalTo(self.mainBackground.mas_centerX);
       make.top.equalTo(self.titleLab.mas_bottom).offset(20);
       make.width.equalTo(self.mainBackground.mas_width).multipliedBy(0.9);
       make.height.equalTo(@150);
   }];
}
-(void)declineBtn
{
    exit(0);
}
/// [接受后继续]按钮点击事件
- (void)mainBtnAction{
    [self.delegate gotoIntroduceView];
}
- (NSAttributedString *)getAttributedWithString:(NSString *)string WithLineSpace:(CGFloat)lineSpace kern:(CGFloat)kern font:(UIFont *)font{
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    //调整行间距
    paragraphStyle.lineSpacing = lineSpace;
    NSDictionary *attriDict = @{NSParagraphStyleAttributeName:paragraphStyle,NSKernAttributeName:@(kern),
                                NSFontAttributeName:font};
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc]initWithString:string attributes:attriDict];
    return attributedString;
}

/* 获取富文本的高度
 *
 * @param string    文字
 * @param lineSpace 行间距
 * @param kern      字间距
 * @param font      字体大小
 * @param width     文本宽度
 *
 * @return size
 */
- (CGSize)getAttributionHeightWithString:(NSString *)string lineSpace:(CGFloat)lineSpace kern:(CGFloat)kern font:(UIFont *)font width:(CGFloat)width {
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.lineSpacing = lineSpace;
    NSDictionary *attriDict = @{
        NSParagraphStyleAttributeName:paragraphStyle,
        NSKernAttributeName:@(kern),
        NSFontAttributeName:font};
    CGSize size = [string boundingRectWithSize:CGSizeMake(width, MAXFLOAT) options:NSStringDrawingUsesLineFragmentOrigin | NSStringDrawingUsesFontLeading attributes:attriDict context:nil].size;
    return size;
}
@end
