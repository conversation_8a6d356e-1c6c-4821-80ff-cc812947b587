//
//  BSEnterBaseController.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSEnterBaseController.h"
#import "ViewController.h"
#import "BSUserDefaultManager.h"
@interface BSEnterBaseController ()

@end

@implementation BSEnterBaseController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.mainBackground = [[UIImageView alloc] initWithImage:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"EnterViewBackground" ofType:@"png"]]];
    [self.view addSubview:self.mainBackground];
    [self.mainBackground mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@0);
        make.left.equalTo(@0);
        make.right.equalTo(@0);
        make.bottom.equalTo(@0);
    }];
    self.mainBackground.userInteractionEnabled = YES;
}

/// 加载[关闭]按钮控件的试图
- (void)loadCloseBtnView{
    if (@available(iOS 13.0, *)) {
         self.closeBtn = [UIButton buttonWithType:UIButtonTypeClose];
     } else {
         self.closeBtn = [UIButton buttonWithType:UIButtonTypeSystem];
         [self.closeBtn setTitle:@"X" forState:UIControlStateNormal];
         self.closeBtn.titleLabel.font = [UIFont boldSystemFontOfSize:17];
         [self.closeBtn setTintColor:[UIColor darkGrayColor]];
         [self.closeBtn setBackgroundColor:[UIColor lightGrayColor]];
         self.closeBtn.layer.cornerRadius = 15.0f;
         self.closeBtn.layer.masksToBounds = YES;
     }
    [self.mainBackground addSubview:self.closeBtn];
    [self.closeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(kNavgationBarHeight));
        make.left.equalTo(@20);
        make.width.equalTo(@30);
        make.height.equalTo(@30);
    }];
    [self.closeBtn addTarget:self action:@selector(closeBtnAction) forControlEvents:UIControlEventTouchUpInside];
}

/// 进入主页核心按钮的点击事件
- (void)mainEnterBtnAction{
    [self goToMainTabBarController];
}

/// [关闭]按钮的点击事件
- (void)closeBtnAction{
    [self goToMainTabBarController];
}

/// 进入主体TabBar控制器
- (void)goToMainTabBarController{
    CGFloat transitAlpha = 0.75f;
    ViewController *mainTabBarCtl = [[ViewController alloc] init];
    mainTabBarCtl.childViewControllers.firstObject.view.alpha = transitAlpha;
    mainTabBarCtl.childViewControllers.firstObject.view.transform = CGAffineTransformMakeScale(1.5, 1.0);
    [UIView animateWithDuration:0.5f animations:^{
        self.mainBackground.alpha = transitAlpha;
    } completion:^(BOOL finished) {
        [AppDelegate getAppDelegate].window.rootViewController = mainTabBarCtl;
        [UIView animateWithDuration:0.5f animations:^{
            mainTabBarCtl.childViewControllers.firstObject.view.alpha = 1.0f;
            mainTabBarCtl.childViewControllers.firstObject.view.transform = CGAffineTransformMakeScale(1.0, 1.0);
        } completion:^(BOOL finished) {
            //[打开app状态值]标识为[已经进入主界面]
            [BSUserDefaultManager setOpenAppState:Goto_Main_Interface];
        }];
    }];
}

@end
