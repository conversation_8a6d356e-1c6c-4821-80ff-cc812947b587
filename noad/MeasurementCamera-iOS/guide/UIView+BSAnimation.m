//
//  UIView+BSAnimation.m
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "UIView+BSAnimation.h"

@implementation UIView (BSAnimation)
/// 透明渐变动画
/// @param duration 动画时间
/// @param startAlpha 开始透明度
/// @param endAlpha 结束透明度
/// @param completion 结束后可执行代码块
- (void)startAlphaAnimationWithDuration:(NSTimeInterval)duration startAlpha:(CGFloat)startAlpha endAlpha:(CGFloat)endAlpha completion:(void (^ __nullable)(BOOL finished))completion{
    self.alpha = startAlpha;
    [UIView animateWithDuration:duration animations:^{
        self.alpha = endAlpha;
    } completion:completion];
}


/// 镜像动画
/// @param duration 动画时间
/// @param startTransform 开始镜像
/// @param endTransform 结束镜像
/// @param completion 结束后可执行代码块
- (void)startTransformAnimationWithDuration:(NSTimeInterval)duration startTransform:(CGAffineTransform)startTransform endTransform:(CGAffineTransform)endTransform completion:(void (^ __nullable)(BOOL finished))completion{
    self.transform = startTransform;
    [UIView animateWithDuration:duration animations:^{
        self.transform = endTransform;
    } completion:completion];
}

/// 多试图依次顺序动画(递归法)
/// @param viewsArray 试图集合的数组
/// @param duration 每个试图动画时间
/// @param startTransform 动画开始的镜像参数
/// @param endTransform 动画结束的镜像参数
/// @param completion 结束后执行的代码块
+ (void)startTransformCollectAnimationWithViewsArray:(NSArray<UIView*>*)viewsArray duration:(NSTimeInterval)duration startTransform:(CGAffineTransform)startTransform endTransform:(CGAffineTransform)endTransform completion:(void (^ __nullable)(BOOL finished))completion{
    for (UIView* currentView in viewsArray) {
        currentView.transform = startTransform;
    }
    
    [self transformCollectAnimationWithViewsArray:viewsArray currentIndex:0 duration:duration endTransform:endTransform completion:completion];
}

+ (void)transformCollectAnimationWithViewsArray:(NSArray<UIView*>*)viewsArray currentIndex:(NSInteger)currentIndex duration:(NSTimeInterval)duration endTransform:(CGAffineTransform)endTransform completion:(void (^ __nullable)(BOOL finished))completion{
    if (currentIndex >= viewsArray.count) {
        [UIView animateWithDuration:0 animations:^{} completion:completion];
        return;
    }
    
    UIView *currentView = viewsArray[currentIndex];
    [UIView animateWithDuration:duration animations:^{
        currentView.transform = endTransform;
    } completion:^(BOOL finished) {
        [self transformCollectAnimationWithViewsArray:viewsArray currentIndex:(currentIndex+1) duration:duration endTransform:endTransform completion:completion];
    }];
}


@end
