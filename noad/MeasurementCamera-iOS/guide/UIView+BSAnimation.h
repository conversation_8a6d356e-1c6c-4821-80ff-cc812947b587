//
//  UIView+BSAnimation.h
//  PhoneClone
//
//  Created by macbookair on 14/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIView (BSAnimation)
/// 透明渐变动画
/// @param duration 动画时间
/// @param startAlpha 开始透明度
/// @param endAlpha 结束透明度
/// @param completion 结束后可执行代码块
- (void)startAlphaAnimationWithDuration:(NSTimeInterval)duration startAlpha:(CGFloat)startAlpha endAlpha:(CGFloat)endAlpha completion:(void (^ __nullable)(BOOL finished))completion;

/// 镜像动画
/// @param duration 动画时间
/// @param startTransform 开始镜像
/// @param endTransform 结束镜像
/// @param completion 结束后可执行代码块
- (void)startTransformAnimationWithDuration:(NSTimeInterval)duration startTransform:(CGAffineTransform)startTransform endTransform:(CGAffineTransform)endTransform completion:(void (^ __nullable)(BOOL finished))completion;

/// 多试图依次顺序动画(递归法)
/// @param viewsArray 试图集合的数组
/// @param duration 每个试图动画时间
/// @param startTransform 动画开始的镜像参数
/// @param endTransform 动画结束的镜像参数
/// @param completion 结束后执行的代码块
+ (void)startTransformCollectAnimationWithViewsArray:(NSArray<UIView*>*)viewsArray duration:(NSTimeInterval)duration startTransform:(CGAffineTransform)startTransform endTransform:(CGAffineTransform)endTransform completion:(void (^ __nullable)(BOOL finished))completion;

@end

NS_ASSUME_NONNULL_END
