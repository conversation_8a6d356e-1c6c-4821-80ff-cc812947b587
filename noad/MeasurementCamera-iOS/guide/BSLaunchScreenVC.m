//
//  BSLaunchScreenVC.m
//  PhoneClone
//
//  Created by macbookair on 12/6/2020.
//  Copyright © 2020 PhoneClone. All rights reserved.
//

#import "BSLaunchScreenVC.h"
#import "BSIntroduceView.h"
#import "ViewController.h"
#import "BSWaitingAnimationView.h"
#import "BSAgreementView.h"
#import "MainTabbarVC.h"
@interface BSLaunchScreenVC ()<BSAgreementViewProtocol, BSIntroduceViewProtocol,MKStoreKitDelegate>

@property (nonatomic, strong) BSAgreementView *agreementView;

@property (nonatomic, strong) BSIntroduceView *introduceView;

/// 用于记录是否是第一次打开app(YES:第一次打开app  NO:不是第一次打开app)
@property (nonatomic, assign) BOOL isFirstOpenApp;
@end

@implementation BSLaunchScreenVC

- (void)viewDidLoad {
    [super viewDidLoad];
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.view.backgroundColor = [UIColor colorWithHexString:@"#040412" alpha:1];
    self.introduceView = [[BSIntroduceView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREENH_HEIGHT)];
    if(!AppDelegate.getAppDelegate.showGuide)
    {
        [self.introduceView loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02",@"IntroduceBackground03"] titleArray:@[NSLocalizedString(@"卡通漫画特效", nil),NSLocalizedString(@"变脸魔法", nil),NSLocalizedString(@"手相解读", nil)] detailsArray:@[@"",@"",@""]];
    }else
    {
        
        
        
        NSString *weekPrice = [[NSUserDefaults standardUserDefaults] objectForKey:@"featureAId"];
        
        if (!weekPrice) {
            weekPrice = @"28.00";
        }
        
        
        [self.introduceView loadIntroduceScrollViewWithBackgrounds:@[@"IntroduceBackground01", @"IntroduceBackground02",@"IntroduceBackground03",@"IntroduceBackground04"] titleArray:@[NSLocalizedString(@"卡通漫画特效", nil),NSLocalizedString(@"变脸魔法", nil),NSLocalizedString(@"手相解读", nil),NSLocalizedString(@"", nil)] detailsArray:@[@"",@"",@"",[NSString stringWithFormat:local(@"免费试用3天体验测测相机全部功能权限，您可以在试用期间无限制的使用所有功能，如果体验不满意可以随时取消，请在当前订阅周期到期前24小时以前前三天免费后按%@/月，自动更新，随时取消"),weekPrice]]];
        
        
    }
    
    [self.view addSubview:self.introduceView];
    self.introduceView.delegate = self;
    
    //    self.agreementView = [[BSAgreementView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREENH_HEIGHT)];
    //    [self.view addSubview:self.agreementView];
    //    self.agreementView.delegate = self;
    
    //加载启动页等待试图
    [self loadWaitingLaunchScreenView];
    
    //    if(AppDelegate.getAppDelegate.showGuide==YES)
    //    {
    //        UIButton* close = [UIButton createButtonWithImageName:@"关闭"];
    //        close.y = SCREENH_HEIGHT-80-bottomHeight-40-100;
    //        [self.view addSubview:close];
    //        close.frame = CGRectMake(SCREEN_WIDTH-20-40, bottomHeight+40+20, 28, 28);
    //        [close bk_whenTapped:^{
    //            [self enterTabbar];
    //        }];
    //    }
}

/// 试图出现前的调用方法
/// @param animated 是否动画
- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
}

/// 加载启动页等待试图
- (void)loadWaitingLaunchScreenView{
    UIImageView *welcome = [[UIImageView alloc]initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREENH_HEIGHT)];
    [welcome setImage:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"EnterViewBackground" ofType:@"png"]]];
    NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
    if (![currentLanguageRegion containsString:@"zh"])
    {
        [welcome setImage:[UIImage imageWithContentsOfFile:[[NSBundle mainBundle] pathForResource:@"EnterViewBackground_en" ofType:@"png"]]];
    }
    welcome.contentMode = UIViewContentModeScaleAspectFill;
    [self.view addSubview:welcome];
    [self.view bringSubviewToFront:welcome];
    
    BSWaitingAnimationView  *waitingView = [BSWaitingAnimationView loadCustomWithRadius:2.5 superView:welcome y:SCREENH_HEIGHT-100 text:NSLocalizedString(@"Loading", nil)];
    [waitingView  startAnimation];
    
    
    //    [BSUserDefaultManager initFirstOpenApp];
    //获取app打开状态,用于判断是否打开[接受协议页面]
    //    if ([BSUserDefaultManager getOpenAppState] == First_Open_App) {//第一次打开app
    //        self.isFirstOpenApp = YES;
    //    } else {//不是第一次打开app
    self.isFirstOpenApp = NO;
    //        [self.agreementView removeFromSuperview];
    //    }
    //===============================================================================
    //启动页动画 -> Duration结束动画时间  delay:启动页停滞时间
    welcome.alpha = 1.0;
    [UIView animateWithDuration:1.0f delay:8.0f options:UIViewAnimationOptionTransitionNone animations:^{
        welcome.alpha = 0.8;
    } completion:^(BOOL finished) {
        [waitingView stopAnimation];
        [welcome removeFromSuperview];
        if (!self.isFirstOpenApp) {
            //如果不是第一次打开app,就要马上进入引导介绍页面,并且开启引导介绍页面的动画
            [self.introduceView waveAnimationOfFirst];
        }
    }];
}

/// 接受协议页面结束后,切换到介绍引导试图页面
- (void)gotoIntroduceView{
    if (self.agreementView.superview != nil) {
        [self.agreementView startAlphaAnimationWithDuration:0.5 startAlpha:1.0 endAlpha:0.8 completion:^(BOOL finished) {
            [self.agreementView removeFromSuperview];
        }];
    }
    
    //介绍引导页的出现动画
    [self.introduceView startTransformAnimationWithDuration:0.5 startTransform:CGAffineTransformMakeScale(1.5, 1.0) endTransform:CGAffineTransformMakeScale(1.0, 1.0) completion:nil];
    //引导页出现时,展示第一个背景试图的波动动画
    [self.introduceView waveAnimationWithIndex:0];
    //[打开app状态值]标识为[已接受协议,但未进入主页]
    [BSUserDefaultManager setOpenAppState:Accept_Agreement];
}

/// 介绍引导页面结束后,切换到TabBar主控制器
- (void)gotoEnterViewController{
    if(AppDelegate.getAppDelegate.showGuide)
    {
        [[APPMakeStoreIAPManager sharedManager]buyFeatureA];
        [APPMakeStoreIAPManager sharedManager].delegate = self;
        [SVProgressHUD show];
    }
    else
    {
        [self enterTabbar];
    }
    
}

- (void)productCPurchased
{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@",@"三联屏",@"com.cecexiangji.appname.month"]}];
    [SVProgressHUD dismiss];
    [self enterTabbar];
}

- (void)cancelVipViewction{
    [SVProgressHUD dismiss];
    [self enterTabbar];
//    BKdislog(@"已经取消订阅!");
}


- (void)failed{
    [MobClick event:@"Subscribe" attributes:@{@"source":[NSString stringWithFormat:@"%@%@购买失败",@"三联屏",@"com.cecexiangji.appname.month"]}];
    [SVProgressHUD dismiss];
}

- (void)enterTabbar
{
//    if ([[NSUserDefaults standardUserDefaults] boolForKey:@"checkKeyifOn1"]) {
//        UIViewController *enterViewCtl = [[BSEnterViewController01 alloc] init];
//        
//        [self.navigationController pushViewController:enterViewCtl animated:YES];
//    }else
//    {
        [self gotoMainViewController];
        [BSUserDefaultManager setOpenAppState:Goto_Main_Interface];
//    }
}

-(void)gotoMainViewController{
    CGFloat transitAlpha = 0.75f;
//    ViewController *launchScreenVc = [[ViewController alloc] init];
//    UINavigationController *mainTabBarCtl = [[UINavigationController alloc] initWithRootViewController:launchScreenVc];
    MainTabbarVC *vc = [[MainTabbarVC alloc] init];
    vc.view.transform = CGAffineTransformMakeScale(1.0, 1.0);
    [UIView animateWithDuration:0.5f animations:^{
        self.view.alpha = transitAlpha;
    } completion:^(BOOL finished) {
        [AppDelegate getAppDelegate].window.rootViewController = vc;
        [UIView animateWithDuration:0.5f animations:^{
            vc.view.alpha = 1.0f;
            vc.view.transform = CGAffineTransformMakeScale(1.0, 1.0);
        } completion:nil];
    }];
}

@end
