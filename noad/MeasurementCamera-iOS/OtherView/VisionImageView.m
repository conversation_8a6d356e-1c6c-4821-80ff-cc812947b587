//
//  VisionImageView.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/27.
//

#import "VisionImageView.h"
#import <Vision/Vision.h>
#import <CoreML/CoreML.h>
#import <ImageIO/ImageIO.h>
#import <objc/runtime.h>

@interface VisionImageView ()

@property (readonly, nonatomic, assign) CGRect boundingBox;
@property (nonatomic, strong) NSMutableArray *landmarksLayers;

@end

@implementation VisionImageView

- (void)drawRect:(CGRect)rect {
    // Drawing code
}

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.imageView = [[UIImageView  alloc] initWithFrame:self.bounds];
        self.imageView.contentMode = UIViewContentModeScaleAspectFit;
        [self addSubview:self.imageView];
    }
    return self;
}

- (void)startVision
{
    self.imageView.image = self.photoImage;
    
    CIImage *faceCIImage = [[CIImage alloc]initWithImage:_photoImage];

    // 1.创建VNImageRequestHandler
    VNImageRequestHandler *vnRequestHeader = [[VNImageRequestHandler alloc] initWithCIImage:faceCIImage options:@{}];
       
    __weak VisionImageView *weakSelf = self;
    // 创建请求VNDetectFaceLandmarksRequest
    VNDetectFaceLandmarksRequest *faceRequest = [[VNDetectFaceLandmarksRequest alloc] initWithCompletionHandler:^(VNRequest * _Nonnull request, NSError * _Nullable error) {
        // 处理识别的数据
        [weakSelf faceLandmarks:request.results];
    }];
    // 3 VNImageRequestHandler执行VNDetectFaceLandmarksRequest
    [vnRequestHeader performRequests:@[faceRequest] error:NULL];
}

// 获取信息成功后 处理
- (void)faceLandmarks:(NSArray *)faces
{
    // 可能是多张脸
    [faces enumerateObjectsUsingBlock:^(VNFaceObservation *face, NSUInteger idx, BOOL * _Nonnull stop) {
        /*
         * face: VNFaceObservation 对象, 里面包含了 landmarks 位置信息, boundingBox 脸的大小 等等信息
         */
        
        // 取出单个脸的 landmarks
        VNFaceLandmarks2D *landmarks = face.landmarks;
        // 声明一个存关键位置的数组
        NSMutableArray *face_landmarks = [NSMutableArray array];
        
        // landmarks 是一个对象，对象中有左眼位置，右眼，鼻子，鼻梁等等属性 根据需求自己添加
        [face_landmarks addObject:landmarks.faceContour];
        [face_landmarks addObject:landmarks.leftEyebrow];
        [face_landmarks addObject:landmarks.rightEyebrow];
        [face_landmarks addObject:landmarks.leftEye];
        [face_landmarks addObject:landmarks.rightEye];
        [face_landmarks addObject:landmarks.outerLips];
        [face_landmarks addObject:landmarks.innerLips];
        [face_landmarks addObject:landmarks.nose];
        [face_landmarks addObject:landmarks.noseCrest];
        [face_landmarks addObject:landmarks.medianLine];
        [face_landmarks addObject:landmarks.outerLips];
        [face_landmarks addObject:landmarks.innerLips];
        //[face_landmarks addObject:landmarks.leftPupil];
        //[face_landmarks addObject:landmarks.rightPupil];
        
        VNFaceLandmarkRegion2D * leftPupilLandmarks = landmarks.leftPupil;
        VNFaceLandmarkRegion2D * rightPupilLandmarks = landmarks.rightPupil;
       
        CGPoint leftPupil = CGPointZero;
        CGPoint rightPupil = CGPointZero;

        for (NSUInteger i = 0; i < leftPupilLandmarks.pointCount; i++) {
            // 取出点
            leftPupil = leftPupilLandmarks.normalizedPoints[i];
            //NSLog(@"leftPupil point == %@", NSStringFromCGPoint(leftPupil));
        }
        
        for (NSUInteger i = 0; i < rightPupilLandmarks.pointCount; i++) {
            // 取出点
            rightPupil = rightPupilLandmarks.normalizedPoints[i];
            //NSLog(@"rightPupil point == %@", NSStringFromCGPoint(rightPupil));
        }
        
        //NSLog(@"\n");
        // 轮廓点的数组
        NSMutableArray * faceContours = [[NSMutableArray alloc] initWithCapacity:0];
        for (NSUInteger i = 0; i < landmarks.faceContour.pointCount; i++) {
            CGPoint point = landmarks.faceContour.normalizedPoints[i];
            //NSLog(@"faceContour point == %@", NSStringFromCGPoint(point));
            [faceContours addObject:NSStringFromCGPoint(point)];
        }
        
        NSLog(@"\n");
        for (NSUInteger i = 0; i < landmarks.medianLine.pointCount; i++) {
            CGPoint point = landmarks.medianLine.normalizedPoints[i];
            //NSLog(@"medianLine point == %@", NSStringFromCGPoint(point));
        }
        /*
        dispatch_async(dispatch_get_main_queue(), ^{
            __weak VisionImageView *weakSelf = self;
            //NSLog(@"weakSelf.m_delegate == %@", weakSelf.m_delegate);
            //[self.m_delegate sendFaceDataWithLeftPupil:leftPupil andRightPupil:rightPupil andFaceBoundingBox:face.boundingBox andFaceContours:faceContours];
        });
        */

        CGRect oldRect = face.boundingBox;
        CGFloat w = oldRect.size.width * self.bounds.size.width;
        CGFloat h = oldRect.size.height * self.bounds.size.height;
        CGFloat x = oldRect.origin.x * self.bounds.size.width;
        CGFloat y = self.bounds.size.height - (oldRect.origin.y * self.bounds.size.height) - h;
        
        // 添加矩形
        /*
        CALayer *testLayer = [[CALayer alloc]init];
        testLayer.borderWidth = 1;
        testLayer.cornerRadius = 3;
        testLayer.borderColor = [UIColor redColor].CGColor;
        testLayer.frame = CGRectMake(x, y, w, h);
        [self.layer addSublayer:testLayer];
        
        [self.landmarksLayers addObject:testLayer];
         */

        //NSLog(@"boundingBox == %@", NSStringFromCGRect(face.boundingBox));
        
        // 遍历位置信息
        [face_landmarks enumerateObjectsUsingBlock:^(VNFaceLandmarkRegion2D *obj, NSUInteger idx, BOOL * _Nonnull stop) {
            // VNFaceLandmarkRegion2D *obj 是一个对象. 表示当前的一个部位
            // 遍历当前部分所有的点
            
            for (int i = 0; i < obj.pointCount; i++) {
                // 取出点
                CGPoint point = obj.normalizedPoints[i];
                point.y = 1.0 - point.y;
                CGFloat px = point.x * w + x;
                CGFloat py = point.y * h + y;
                CGPoint p = CGPointMake(px, py);
                
                NSLog(@"p == %@", NSStringFromCGPoint(p));
                
                CALayer *layer = [CALayer layer];
                layer.backgroundColor = [UIColor orangeColor].CGColor;
                layer.frame = CGRectMake(0, 0, 2.0f, 2.0f);
                layer.position = p;
                layer.cornerRadius = 1.0f;
                
                layer.backgroundColor = [UIColor greenColor].CGColor;
                /*
                // 轮廓
                if (idx == 0) {
                    if (i == 0) {
                        layer.backgroundColor = [UIColor greenColor].CGColor;
                    } else {
                        layer.backgroundColor = [UIColor greenColor].CGColor;
                    }
                }
                // 中线
                if (idx == 9) {
                    layer.backgroundColor = [UIColor greenColor].CGColor;
                }
                 */
                [self.layer addSublayer:layer];
            }
        }];
    }];
}


@end
