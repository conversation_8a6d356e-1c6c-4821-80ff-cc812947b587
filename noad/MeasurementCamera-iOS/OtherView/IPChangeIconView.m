//
//  IPChangeIconView.m
//  ImageProcessApp
//
//  Created by fs0011 on 2023/7/5.
//

#import "IPChangeIconView.h"
#import <BlocksKit/UIView+BlocksKit.h>
@implementation IPChangeIconView
- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        
        self.backgroundColor = [UIColor whiteColor];
        self.layer.cornerRadius = 12;
        self.layer.masksToBounds = YES;
        
        
    }
    return self;
}

- (void)layout
{
    [self mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(SCREEN_WIDTH-2*tableEdge);
        make.height.mas_equalTo(120);
    }];
    UIScrollView* bac = [UIScrollView new];
    bac.showsHorizontalScrollIndicator = NO;
    [self addSubview:bac];
    
    
    UILabel* title = [UILabel createLabelWithTitle:local(@"更换主屏幕图标") textColor:darkTextColor textAlignment:NSTextAlignmentLeft font:smallFont];
    [self addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.mas_equalTo(15);
    }];
    
    [bac mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(title.mas_bottom).offset(15);
        make.left.mas_equalTo(0);
        make.width.mas_equalTo(SCREEN_WIDTH);
        make.height.mas_equalTo(60);
       
    }];
    
    UIView* bottom = nil;
    NSArray* ar = @[@"AppIcon",@"AppIcon1",@"AppIcon2",@"AppIcon3"];
    for (NSString* str in ar) {
        UIImageView * im =  [UIImageView createSizeFitImageviewName:str];
        im.layer.cornerRadius = 12;
        im.layer.masksToBounds = YES;
        [bac addSubview:im];
        [im mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.mas_equalTo(0);
            if(!bottom)
                make.left.mas_equalTo(15);
            else
                make.left.mas_equalTo(bottom.mas_right).offset(15);
            make.width.height.mas_equalTo(60);
        }];
        bottom = im;
        im.userInteractionEnabled = YES;
        [im bk_whenTapped:^{
            [AppDelegate changeIcon:[str stringByReplacingOccurrencesOfString:@"-1024" withString:@""]];
        }];
    }
    [bottom mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-15);
        make.bottom.mas_equalTo(0);
    }];
    [self layoutIfNeeded];
}


- (CGFloat)getheight
{
    return self.frame.size.height;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
