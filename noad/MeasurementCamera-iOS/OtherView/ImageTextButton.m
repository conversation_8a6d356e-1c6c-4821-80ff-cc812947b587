//
//  ImageTextButton.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "ImageTextButton.h"
#import "LanguageManager.h"

@interface ImageTextButton ()

@end

@implementation ImageTextButton

- (void)drawRect:(CGRect)rect {
    // Drawing code
}

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        
        BOOL isLanguageZH = [LanguageManager getCurrentLanguageIsZH];
        if (isLanguageZH == YES) {
            self.titleFontSize = 13.0;
        } else {
            self.titleFontSize = 11.0;
        }
        
        if (self.imgView == nil) {
            self.imgView = [[UIImageView alloc] init];
            [self addSubview:self.imgView];
        }
        
        if (self.titleLabel == nil) {
            self.titleLabel = [[UILabel alloc] init];
            [self.titleLabel setTextColor:[UIColor whiteColor]];
            [self.titleLabel setFont:[UIFont systemFontOfSize:self.titleFontSize]];
            [self.titleLabel setTextAlignment:NSTextAlignmentCenter];
            [self addSubview:self.titleLabel];
        }
        
        if (self.button == nil) {
            self.button = [UIButton buttonWithType:UIButtonTypeCustom];
            self.button.backgroundColor = [UIColor clearColor];
            [self.button setBackgroundColor:[UIColor clearColor]];
            [self.button addTarget:self action:@selector(buttonClick:) forControlEvents:UIControlEventTouchUpInside];
            [self addSubview:self.button];
        }
    }
    return self;
}

- (void)setImage:(UIImage *)image title:(NSString *)title
{
    [self.imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX).with.offset(0);
        make.top.equalTo(self.mas_top).with.offset(7);
        make.leading.equalTo(self.mas_leading).with.offset(9);
        make.trailing.equalTo(self.mas_trailing).with.offset(-9);
        make.height.equalTo(self.imgView.mas_width);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.mas_centerX).with.offset(0);
        make.bottom.equalTo(self.mas_bottom).with.offset(-7);
        make.leading.equalTo(self.mas_leading).with.offset(2);
        make.trailing.equalTo(self.mas_trailing).with.offset(-2);
        make.height.equalTo(@(self.titleFontSize+2));
    }];
    
    [self.button mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.mas_top);
        make.bottom.equalTo(self.mas_bottom);
        make.leading.equalTo(self.mas_leading);
        make.trailing.equalTo(self.mas_trailing);
    }];
    
    [self.imgView setContentMode:UIViewContentModeScaleAspectFit];
    [self.imgView setImage:image];
    
    [self.titleLabel setText:title];
}

- (void)buttonClick:(UIButton *)sender
{
    if (self.buttonClick) {
        self.buttonClick(YES);
    }
}


@end
