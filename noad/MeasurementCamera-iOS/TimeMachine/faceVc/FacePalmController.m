//
//  FacePalmController.m
//  TimeMachine
//
//  Created by fs0011 on 2024/10/10.
//

#import "FacePalmController.h"
#import "FaceResultController.h"
#import "UpgradeVIPViewController.h"
@interface FacePalmController ()
@property UIImageView* meixingloading;
@property UIImageView* bixingloading;
@property UIImageView* zuixingloading;

@property UIImageView* meixingtrik;
@property UIImageView* bixingtrik;
@property UIImageView* zuixingtrik;
@end

@implementation FacePalmController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    self.view.backgroundColor = [UIColor blackColor];

    [self initBackButton];
    // Do any additional setup after loading the view.
}

- (void)setupUI
{
    UIImageView* im = [UIImageView createSizeFitImageviewName:@"面相外框"];
    [self.view addSubview:im];
    [im mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.centerY.mas_equalTo(0);
        make.width.mas_equalTo(295*scaleX);
        make.height.mas_equalTo(491*scaleX);
    }];
    
    UIView *borderView = [UIView new];
    borderView.layer.cornerRadius = 162 * scaleX * 0.5 + 5; // 加上边框的宽度
    borderView.layer.borderWidth = 5; // 设置边框宽度
    borderView.layer.borderColor = [UIColor colorWithHexString:@"#FAEFCE" alpha:1].CGColor;
    borderView.layer.masksToBounds = NO; // 父视图不裁剪内容

    [im addSubview:borderView];

    // 子视图用于圆形图片
    UIImageView *image = [UIImageView new];
    image.image = self.image;
    image.layer.cornerRadius = 162 * scaleX * 0.5;
    image.layer.masksToBounds = YES; // 保证图片是圆形

    [borderView addSubview:image];

    // 约束父视图
    [borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(84 * scaleX);
        make.width.height.mas_equalTo(162 * scaleX + 10); // 增加边框的宽度
    }];

    // 约束UIImageView
    [image mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(5, 5, 5, 5)); // 留出边框的宽度
    }];
    
    
    UILabel* meixingla = [UILabel createLabelWithTitle:local(@"正在分析眉形") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:standMedlumFont];
    [self.view addSubview:meixingla];
    [meixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(image.mas_bottom).offset(46*scaleX);
    }];
    
    UILabel* bixingla = [UILabel createLabelWithTitle:local(@"正在分析鼻型") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:standMedlumFont];
    [self.view addSubview:bixingla];
    [bixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(meixingla.mas_bottom).offset(11*scaleX);
    }];
    
    UILabel* zuixingla = [UILabel createLabelWithTitle:local(@"正在分析嘴型") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:standMedlumFont];
    [self.view addSubview:zuixingla];
    [zuixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(bixingla.mas_bottom).offset(11*scaleX);
    }];
    
    self.meixingloading = [UIImageView createSizeFitImageviewName:@"loading"];
    [self.view addSubview:self.meixingloading];
    [self.meixingloading mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(meixingla);
        make.right.mas_equalTo(meixingla.mas_left).offset(-13*scaleX);
        make.width.height.mas_equalTo(24*scaleX);
    }];
    
    self.bixingloading = [UIImageView createSizeFitImageviewName:@"loading"];
    [self.view addSubview:self.bixingloading];
    [self.bixingloading mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(bixingla);
        make.right.mas_equalTo(bixingla.mas_left).offset(-13*scaleX);
        make.width.height.mas_equalTo(24*scaleX);
    }];
    
    self.zuixingloading = [UIImageView createSizeFitImageviewName:@"loading"];
    [self.view addSubview:self.zuixingloading];
    [self.zuixingloading mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(zuixingla);
        make.right.mas_equalTo(zuixingla.mas_left).offset(-13*scaleX);
        make.width.height.mas_equalTo(24*scaleX);
    }];
    
    self.meixingtrik = [UIImageView createSizeFitImageviewName:@"notrick"];
    [self.view addSubview:self.meixingtrik];
    [self.meixingtrik mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(meixingla);
        make.left.mas_equalTo(meixingla.mas_right).offset(7*scaleX);
        make.width.height.mas_equalTo(13.99*scaleX);
    }];
    
    self.bixingtrik = [UIImageView createSizeFitImageviewName:@"notrick"];
    [self.view addSubview:self.bixingtrik];
    [self.bixingtrik mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(bixingla);
        make.left.mas_equalTo(bixingla.mas_right).offset(7*scaleX);
        make.width.height.mas_equalTo(13.99*scaleX);
    }];
    
    self.zuixingtrik = [UIImageView createSizeFitImageviewName:@"notrick"];
    [self.view addSubview:self.zuixingtrik];
    [self.zuixingtrik mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(zuixingla);
        make.left.mas_equalTo(zuixingla.mas_right).offset(7*scaleX);
        make.width.height.mas_equalTo(13.99*scaleX);
    }];
    
    [self startannimation];
}

//- (void)viewDidAppear:(BOOL)animated {
//    [super viewDidAppear:animated];
//    [self startLoadingAnimation:self.meixingloading];
//    [self startLoadingAnimation:self.bixingloading];
//    [self startLoadingAnimation:self.zuixingloading];
//}

- (void)startannimation
{
    // 1. 开始所有的loading动画 (例如旋转动画)x
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.view layoutIfNeeded];
        [self startLoadingAnimation:self.meixingloading];
        [self startLoadingAnimation:self.bixingloading];
        [self startLoadingAnimation:self.zuixingloading];
    });
        // 2. 创建时间间隔，逐步切换每个trik图标
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 隐藏眉形loading，显示眉形trik
            self.meixingloading.hidden = YES;
            self.meixingtrik.image = [UIImage imageNamed:@"trick"];
            self.meixingtrik.hidden = NO;
        });
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(10 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 隐藏鼻型loading，显示鼻型trik
            self.bixingloading.hidden = YES;
            self.bixingtrik.image = [UIImage imageNamed:@"trick"];
            self.bixingtrik.hidden = NO;
        });
        
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(15 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            // 隐藏嘴型loading，显示嘴型trik
            self.zuixingloading.hidden = YES;
            self.zuixingtrik.image = [UIImage imageNamed:@"trick"];
            self.zuixingtrik.hidden = NO;
            
            
            UILabel* compele = [UILabel createLabelWithTitle:local(@"检测完成") textColor:[UIColor colorWithHexString:@"#B78176" alpha:1] textAlignment:NSTextAlignmentCenter font:bigMedlumFont];
            [self.view addSubview:compele];
            [compele mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.mas_equalTo(0);
                make.top.mas_equalTo(self.zuixingtrik.mas_bottom).offset(25*scaleX);
            }];
            
            UIButton* btn = [UIButton createButtonWithTitle:local(@"立即查看") color:[UIColor colorWithHexString:@"#040412" alpha:1] font:bigMedlumFont];
            btn.backgroundColor = [UIColor colorWithHexString:@"#626368" alpha:1];
            btn.layer.cornerRadius = 41*0.5*scaleX;
            btn.layer.masksToBounds = YES;
            [self.view addSubview:btn];
            [btn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(162*scaleX);
                make.height.mas_equalTo(41*scaleX);
                make.centerX.mas_equalTo(0);
                make.top.mas_equalTo(self.zuixingtrik.mas_bottom).offset(154*scaleX);
            }];
            [btn bk_whenTapped:^{
                if([APPMakeStoreIAPManager featureVip]!=YES)
                {
                    UpgradeVIPViewController *vc = [[UpgradeVIPViewController alloc] init];
                    vc.from = @"脸相";
                    [self.navigationController pushViewController:vc animated:YES];
                    return;
                }
                
                FaceResultController* re = [FaceResultController new];
                re.image = self.image;
                [self.navigationController pushViewController:re animated:YES];
            }];
        });
}

- (void)startLoadingAnimation:(UIImageView *)loadingImageView {
    // 添加旋转动画
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.toValue = @(M_PI * 2.0);
    rotationAnimation.duration = 1.0;
    rotationAnimation.cumulative = YES;
    rotationAnimation.repeatCount = HUGE_VALF;
    [loadingImageView.layer addAnimation:rotationAnimation forKey:@"rotationAnimation"];
    
    
//    [UIView animateWithDuration:1.0 delay:0 options:UIViewAnimationOptionRepeat | UIViewAnimationOptionCurveLinear animations:^{
//        self.meixingloading.transform = CGAffineTransformRotate(self.meixingloading.transform, M_PI);
//    } completion:nil];
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
