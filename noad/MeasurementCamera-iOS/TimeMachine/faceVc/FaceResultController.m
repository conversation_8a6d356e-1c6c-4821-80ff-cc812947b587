//
//  FaceResultController.m
//  TimeMachine
//
//  Created by fs0011 on 2024/10/14.
//

#import "FaceResultController.h"
#import "StarsView.h"
@interface FaceResultController ()
@property NSDictionary* meixingDic;
@property NSDictionary* bixingDic;
@property NSDictionary* zuixingDic;
@property UIImageView* im;
@end

@implementation FaceResultController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initBackButton];
    [self setUp];
    // Do any additional setup after loading the view.
}





-(void)backButtonClick:(UIButton *)sender
{
    [self.navigationController popToRootViewControllerAnimated:YES];
}

- (void)setUp
{
    UIView *borderView = [UIView new];
    borderView.layer.cornerRadius = 162 * scaleX * 0.5 + 5; // 加上边框的宽度
    borderView.layer.borderWidth = 5; // 设置边框宽度
    borderView.layer.borderColor = [UIColor colorWithHexString:@"#FAEFCE" alpha:1].CGColor;
    borderView.layer.masksToBounds = NO; // 父视图不裁剪内容

    [self.view addSubview:borderView];
    self.im = borderView;
    // 子视图用于圆形图片
    UIImageView *image = [UIImageView new];
    image.image = self.image;
    image.layer.cornerRadius = 162 * scaleX * 0.5;
    image.layer.masksToBounds = YES; // 保证图片是圆形

    [borderView addSubview:image];

    // 约束父视图
    [borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(58 * scaleX);
        make.width.height.mas_equalTo(162 * scaleX + 10); // 增加边框的宽度
    }];

    // 约束UIImageView
    [image mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(5, 5, 5, 5)); // 留出边框的宽度
    }];
    
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"face" ofType:@"json"];
    NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
    if (![currentLanguageRegion containsString:@"zh"])
    {
        filePath = [[NSBundle mainBundle] pathForResource:@"face_en" ofType:@"json"];
    }
    
    // 读取文件内容
    NSData *data = [NSData dataWithContentsOfFile:filePath];
    
    // 解析 JSON 数据
    NSError *error;
    NSArray *jsonDict = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
    [self parseJsonAndSplitIntoArrays:jsonDict];
    
}

- (void)parseJsonAndSplitIntoArrays:(NSArray *)jsonArray {
    NSMutableArray *meixingArray = [NSMutableArray array];
    NSMutableArray *bixingArray = [NSMutableArray array];
    NSMutableArray *zuixingArray = [NSMutableArray array];
    
    for (NSDictionary *item in jsonArray) {
        NSString *type = item[@"type"];
        if ([type isEqualToString:@"眉形"]) {
            [meixingArray addObject:item];
        } else if ([type isEqualToString:@"鼻型"]) {
            [bixingArray addObject:item];
        } else if ([type isEqualToString:@"嘴型"]) {
            [zuixingArray addObject:item];
        }
    }
    NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
    if (![currentLanguageRegion containsString:@"zh"])
    {
    
        for (NSDictionary *item in jsonArray) {
            NSString *type = item[@"type"];
            if ([type isEqualToString:@"Eyebrow Shape"]) {
                [meixingArray addObject:item];
            } else if ([type isEqualToString:@"Nose Shape"]) {
                [bixingArray addObject:item];
            } else if ([type isEqualToString:@"Mouth Shape"]) {
                [zuixingArray addObject:item];
            }
        }
    }
    
    
    
    NSLog(@"眉形数组: %@", meixingArray);
    NSLog(@"鼻型数组: %@", bixingArray);
    NSLog(@"嘴型数组: %@", zuixingArray);
    
    [self selectRandomDictionaryFromArrays:@[meixingArray, bixingArray, zuixingArray]];
}

- (void)selectRandomDictionaryFromArrays:(NSArray<NSArray *> *)arrays {
    for (NSUInteger i = 0; i < arrays.count; i++) {
        NSArray *array = arrays[i];
        if (array.count > 0) {
            NSUInteger randomIndex = arc4random_uniform((uint32_t)array.count);
            NSDictionary *randomDict = array[randomIndex];
            NSLog(@"随机选择的字典: %@", randomDict);
            
            // 设置对应的属性
            if (i == 0) {
                self.meixingDic = randomDict;
            } else if (i == 1) {
                self.bixingDic = randomDict;
            } else if (i == 2) {
                self.zuixingDic = randomDict;
            }
        }
    }
    [self setUPResult];
}

- (void)setUPResult
{
    UIImageView* scorebac = [UIImageView createSizeFitImageviewName:@"分数背景"];
    [self.view addSubview:scorebac];
    [scorebac mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(262.5*scaleX);
        make.height.mas_equalTo(31*scaleX);
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(self.im.mas_bottom).offset(29*scaleX);
    }];
    
    UILabel* la = [UILabel createLabelWithTitle:local(@"面部分析") textColor:[UIColor blackColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    [scorebac addSubview:la];
    [la mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(32*scaleX);
        make.centerY.mas_equalTo(0);
    }];
    
    int i1 = [self.meixingDic[@"scores"] intValue];
    int i2 = [self.bixingDic[@"scores"] intValue];
    int i3 = [self.zuixingDic[@"scores"] intValue];
    
    StarsView *personalityStartView = [[StarsView alloc] initWithStarSize:CGSizeMake(20, 20) space:5 numberOfStar:5];
    personalityStartView.score = (i1+i2+i3)/3;
    //personalityStartView.frame = CGRectMake(60, 290, personalityStartView.frame.size.width, personalityStartView.frame.size.height);
    [scorebac addSubview:personalityStartView];
    [personalityStartView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.mas_equalTo(0);
        make.left.mas_equalTo(la.mas_right).offset(30*scaleX);
        make.right.mas_equalTo(-30*scaleX);
        make.height.mas_equalTo(20*scaleX);
    }];
    
    UILabel* cankao = [UILabel createLabelWithTitle:local(@"（结果仅供参考）") textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:smallFont];
    [self.view addSubview:cankao];
    [cankao mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(scorebac.mas_bottom).offset(10*scaleX);
        make.centerX.mas_equalTo(0);
    }];
    
    UIScrollView* sc = [UIScrollView new];
    [self.view addSubview:sc];
    [sc mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(cankao.mas_bottom).offset(10*scaleX);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    
    
    UIImageView* meixingbg = [UIImageView createSizeFitImageviewName:@"面部背景"];
    [sc addSubview:meixingbg];
    [meixingbg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(0);
        make.left.mas_equalTo(16*scaleX);
        make.width.mas_equalTo(SCREEN_WIDTH-16*2*scaleX);
    }];
    
    UILabel* meixingla = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"眉形：%@"),self.meixingDic[@"name"]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:standMedlumFont];
    [meixingbg addSubview:meixingla];
    [meixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(15*scaleX);
        make.left.mas_equalTo(18*scaleX);
    }];
    
    UILabel* meixingContent = [UILabel createLabelWithTitle:self.meixingDic[@"value"] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:smallFont];
    meixingContent.numberOfLines = 0;
    [meixingbg addSubview:meixingContent];
    [meixingContent mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(38*scaleX);
        make.left.mas_equalTo(11*scaleX);
        make.right.mas_equalTo(-11*scaleX);
        make.bottom.mas_equalTo(-11*scaleX);
    }];
    

    UIImageView* bixingbg = [UIImageView createSizeFitImageviewName:@"面部背景"];
    [sc addSubview:bixingbg];
    [bixingbg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(meixingbg.mas_bottom).offset(30*scaleX);
        make.left.mas_equalTo(16*scaleX);
        make.width.mas_equalTo(SCREEN_WIDTH-16*2*scaleX);
    }];
    
    UILabel* bixingla = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"鼻形：%@"),self.bixingDic[@"name"]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:standMedlumFont];
    [bixingbg addSubview:bixingla];
    [bixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(15*scaleX);
        make.left.mas_equalTo(18*scaleX);
    }];
    
    UILabel* bixingContent = [UILabel createLabelWithTitle:self.bixingDic[@"value"] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:smallFont];
    bixingContent.numberOfLines = 0;
    [bixingbg addSubview:bixingContent];
    [bixingContent mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(38*scaleX);
        make.left.mas_equalTo(11*scaleX);
        make.right.mas_equalTo(-11*scaleX);
        make.bottom.mas_equalTo(-11*scaleX);
    }];
    
    
    UIImageView* zuixingbg = [UIImageView createSizeFitImageviewName:@"面部背景"];
    [sc addSubview:zuixingbg];
    [zuixingbg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(bixingbg.mas_bottom).offset(30*scaleX);
        make.left.mas_equalTo(16*scaleX);
        make.width.mas_equalTo(SCREEN_WIDTH-16*2*scaleX);
        make.bottom.mas_equalTo(-11*scaleX);
    }];
    
    UILabel* zuixingla = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"嘴形：%@"),self.zuixingDic[@"name"]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:standMedlumFont];
    [zuixingbg addSubview:zuixingla];
    [zuixingla mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(15*scaleX);
        make.left.mas_equalTo(18*scaleX);
    }];
    
    UILabel* zuixingContent = [UILabel createLabelWithTitle:self.zuixingDic[@"value"] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:smallFont];
    zuixingContent.numberOfLines = 0;
    [zuixingbg addSubview:zuixingContent];
    [zuixingContent mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(38*scaleX);
        make.left.mas_equalTo(11*scaleX);
        make.right.mas_equalTo(-11*scaleX);
        make.bottom.mas_equalTo(-11*scaleX);
    }];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
