//
//  MyImagePickerMaskView.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/7.
//

#import "MyImagePickerMaskView.h"

@interface MyImagePickerMaskView ()

@property (nonatomic, strong) UIView *maskPicView;

@end

@implementation MyImagePickerMaskView


- (void)drawRect:(CGRect)rect
{
    // Drawing code
}
 

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];

        self.maskPicView = [[UIView alloc] initWithFrame:self.bounds];
        self.maskPicView.backgroundColor = [UIColor blackColor];
        self.maskPicView.alpha = 0.6;
        [self addSubview:self.maskPicView];
        
        self.maskPicView.layer.mask = [self maskStyle:self.maskPicView.bounds];
        [self.maskPicView.layer addSublayer:[self cycleLine:self.maskPicView.bounds]];
    }
    return self;
}

- (CAShapeLayer *)maskStyle:(CGRect)rect
{
    //
    UIBezierPath *path = [UIBezierPath bezierPathWithRect:rect];
    
    CGFloat x = rect.size.width/2.0;
    CGFloat y = rect.size.height/2.0;
    CGFloat radius = MIN(x, y)*0.8;
    //
    UIBezierPath *cycle = [UIBezierPath bezierPathWithArcCenter:CGPointMake(x, y)
                                                         radius:radius
                                                     startAngle:0.0
                                                       endAngle:2*M_PI
                                                      clockwise:YES];
    [path appendPath:cycle];
    //
    CAShapeLayer *maskLayer = [CAShapeLayer layer];
    maskLayer.path = [path CGPath];
    maskLayer.fillRule = kCAFillRuleEvenOdd;
    
    return maskLayer;
}

- (CAShapeLayer *)cycleLine:(CGRect)rect
{
    CGFloat x = rect.size.width/2.0;
    CGFloat y = rect.size.height/2.0;
    CGFloat radius = MIN(x, y)*0.8;
    //
    UIBezierPath *cycle = [UIBezierPath bezierPathWithArcCenter:CGPointMake(x, y)
                                                         radius:radius
                                                     startAngle:0.0
                                                       endAngle:2*M_PI
                                                      clockwise:YES];

    CAShapeLayer *cycleLayer = [CAShapeLayer layer];
    cycleLayer.path = [cycle CGPath];
    cycleLayer.lineWidth = 5;
    cycleLayer.strokeColor= [UIColor whiteColor].CGColor;//边框颜色
    
    return cycleLayer;
}

@end
