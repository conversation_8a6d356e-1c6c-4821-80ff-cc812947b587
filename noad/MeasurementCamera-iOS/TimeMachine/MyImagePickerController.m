//
//  MyImagePickerController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import "MyImagePickerController.h"
#import "MyImagePickerMaskView.h"

@interface MyImagePickerController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) UIButton *photoAlbumButton;
@property (nonatomic, strong) UIButton *photoGraphButton;
@property (nonatomic, strong) UIButton *flashlightButton;

@property (nonatomic) BOOL isIPad;

@end

@implementation MyImagePickerController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isIPad = [self getIsIPad];
    
    
    self.showsCameraControls = NO;
    self.cameraFlashMode = UIImagePickerControllerCameraFlashModeOff;
    
    if (self.isIPad) {
        MyImagePickerMaskView *maskView = [[MyImagePickerMaskView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight-150)];
        [self.view addSubview:maskView];
        
        UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(5, 0, 50, 50)];
        [backButton setImage:[UIImage imageNamed:@"返回"] forState:UIControlStateNormal];
        [backButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.view addSubview:backButton];
        //NSLog(@"kScreenWidth == %f    kScreenHeight == %f", kScreenWidth, kScreenHeight);
        
        //底部黑色底图
        UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, kScreenHeight-150, kScreenWidth, 150)];
        bottomView.backgroundColor = [UIColor blackColor];
        [self.view addSubview:bottomView];
        
        // 相册按钮
        _photoAlbumButton = [[UIButton alloc] init];
        [_photoAlbumButton setImage:[UIImage imageNamed:@"拍照相册"] forState:UIControlStateNormal];
        [bottomView addSubview:_photoAlbumButton];
        [_photoAlbumButton addTarget:self action:@selector(photoAlbumButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        
        // 拍照按钮
        _photoGraphButton = [[UIButton alloc] init];
        [_photoGraphButton setImage:[UIImage imageNamed:@"拍照圆形"] forState:UIControlStateNormal];
        [bottomView addSubview:_photoGraphButton];
        [_photoGraphButton addTarget:self action:@selector(photoGraphButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        
        // 闪光灯按钮
        _flashlightButton = [[UIButton alloc] init];
        [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯关闭"] forState:UIControlStateNormal];
        [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯开启"] forState:UIControlStateSelected];
        [_flashlightButton setSelected:NO];
        [bottomView addSubview:_flashlightButton];
        [_flashlightButton addTarget:self action:@selector(flashlightButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        
        [_photoGraphButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(bottomView.mas_centerX).with.offset(0);
            make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
            make.width.equalTo(@70);
            make.height.equalTo(@70);
        }];
        
        [_photoAlbumButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(_photoGraphButton.mas_leading).with.offset(-kScreenWidth/4+18+22);
            make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
            make.width.equalTo(@44);
            make.height.equalTo(@44);
        }];
        
        [_flashlightButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(_photoGraphButton.mas_trailing).with.offset(kScreenWidth/4-18-22);
            make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
            make.width.equalTo(@44);
            make.height.equalTo(@44);
        }];
    } else {
        if (kIs_iPhoneX) {
            [self.view.layer setBounds:CGRectMake(0, -kStatusBarHeight-60, kScreenWidth, 0)];
            [self.view.layer setContentsRect:self.view.bounds];
            
            MyImagePickerMaskView *maskView = [[MyImagePickerMaskView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight-344)];
            [self.view addSubview:maskView];
            
            UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(5, -60, 50, 50)];
            [backButton setImage:[UIImage imageNamed:@"返回"] forState:UIControlStateNormal];
            [backButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            [self.view addSubview:backButton];
            //NSLog(@"kScreenWidth == %f    kScreenHeight == %f", kScreenWidth, kScreenHeight);
            
            //底部黑色底图
            UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, kScreenHeight-150-kBottomSafeHeight-kStatusBarHeight-100, kScreenWidth, 150)];
            bottomView.backgroundColor = [UIColor blackColor];
            [self.view addSubview:bottomView];
            
            // 相册按钮
            _photoAlbumButton = [[UIButton alloc] init];
            [_photoAlbumButton setImage:[UIImage imageNamed:@"拍照相册"] forState:UIControlStateNormal];
            [bottomView addSubview:_photoAlbumButton];
            [_photoAlbumButton addTarget:self action:@selector(photoAlbumButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            // 拍照按钮
            _photoGraphButton = [[UIButton alloc] init];
            [_photoGraphButton setImage:[UIImage imageNamed:@"拍照圆形"] forState:UIControlStateNormal];
            [bottomView addSubview:_photoGraphButton];
            [_photoGraphButton addTarget:self action:@selector(photoGraphButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            // 闪光灯按钮
            _flashlightButton = [[UIButton alloc] init];
            [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯关闭"] forState:UIControlStateNormal];
            [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯开启"] forState:UIControlStateSelected];
            [_flashlightButton setSelected:NO];
            [bottomView addSubview:_flashlightButton];
            [_flashlightButton addTarget:self action:@selector(flashlightButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            [_photoGraphButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(bottomView.mas_centerX).with.offset(0);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@70);
                make.height.equalTo(@70);
            }];
            
            [_photoAlbumButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(_photoGraphButton.mas_leading).with.offset(-kScreenWidth/4+18+22);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@44);
                make.height.equalTo(@44);
            }];
            
            [_flashlightButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(_photoGraphButton.mas_trailing).with.offset(kScreenWidth/4-18-22);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@44);
                make.height.equalTo(@44);
            }];
        } else {
            [self.view.layer setBounds:CGRectMake(0, -60, kScreenWidth, 0)];
            [self.view.layer setContentsRect:self.view.bounds];

            
            MyImagePickerMaskView *maskView = [[MyImagePickerMaskView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight-123-60)];
            [self.view addSubview:maskView];
            
            UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(5, -55, 50, 50)];
            [backButton setImage:[UIImage imageNamed:@"返回"] forState:UIControlStateNormal];
            [backButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            [self.view addSubview:backButton];
            //NSLog(@"kScreenWidth == %f    kScreenHeight == %f", kScreenWidth, kScreenHeight);
            
            //底部黑色底图
            UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, kScreenHeight-130-60, kScreenWidth, 130)];
            bottomView.backgroundColor = [UIColor blackColor];
            [self.view addSubview:bottomView];
            
            // 相册按钮
            _photoAlbumButton = [[UIButton alloc] init];
            [_photoAlbumButton setImage:[UIImage imageNamed:@"拍照相册"] forState:UIControlStateNormal];
            [bottomView addSubview:_photoAlbumButton];
            [_photoAlbumButton addTarget:self action:@selector(photoAlbumButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            // 拍照按钮
            _photoGraphButton = [[UIButton alloc] init];
            [_photoGraphButton setImage:[UIImage imageNamed:@"拍照圆形"] forState:UIControlStateNormal];
            [bottomView addSubview:_photoGraphButton];
            [_photoGraphButton addTarget:self action:@selector(photoGraphButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            // 闪光灯按钮
            _flashlightButton = [[UIButton alloc] init];
            [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯关闭"] forState:UIControlStateNormal];
            [_flashlightButton setImage:[UIImage imageNamed:@"闪光灯开启"] forState:UIControlStateSelected];
            [_flashlightButton setSelected:NO];
            [bottomView addSubview:_flashlightButton];
            [_flashlightButton addTarget:self action:@selector(flashlightButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            
            [_photoGraphButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(bottomView.mas_centerX).with.offset(0);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@70);
                make.height.equalTo(@70);
            }];
            
            [_photoAlbumButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.trailing.equalTo(_photoGraphButton.mas_leading).with.offset(-kScreenWidth/4+18+22);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@44);
                make.height.equalTo(@44);
            }];
            
            [_flashlightButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(_photoGraphButton.mas_trailing).with.offset(kScreenWidth/4-18-22);
                make.centerY.equalTo(bottomView.mas_centerY).with.offset(0);
                make.width.equalTo(@44);
                make.height.equalTo(@44);
            }];
        }
    }
}

- (void)photoAlbumButtonClick:(UIButton *)sender
{
    PHAuthorizationStatus authorizationStatus = [PHPhotoLibrary authorizationStatus];
    NSLog(@"authorizationStatus == %ld", (long)authorizationStatus);
    if (authorizationStatus == PHAuthorizationStatusNotDetermined) {
        //第一次使用
        [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
            if (status == PHAuthorizationStatusAuthorized) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    UIImagePickerController *vc = [[UIImagePickerController alloc] init];
                    vc.allowsEditing = YES;
                    vc.delegate = self;
                    vc.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
                    [self presentViewController:vc animated:YES completion:nil];
                });
            }
        }];
    } else if (authorizationStatus == PHAuthorizationStatusDenied || authorizationStatus == PHAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"AlbumPowerTips1", nil) message:NSLocalizedString(@"AlbumPowerTips2", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authorizationStatus == PHAuthorizationStatusAuthorized) {
        //用户已授权
        UIImagePickerController *vc = [[UIImagePickerController alloc] init];
        vc.allowsEditing = YES;
        vc.delegate = self;
        vc.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
        [self presentViewController:vc animated:YES completion:nil];
    }
}

- (void)photoGraphButtonClick:(UIButton *)sender
{
    [self takePicture];
}

- (void)flashlightButtonClick:(UIButton *)sender
{
    if (sender.isSelected == YES) {
        [sender setSelected:NO];
        self.cameraFlashMode = UIImagePickerControllerCameraFlashModeOff;
        [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"CloseFlashLight", nil)];
        //NSLog(@"isSelected == %d", sender.isSelected);
    } else {
        [sender setSelected:YES];
        self.cameraFlashMode = UIImagePickerControllerCameraFlashModeOn;
        [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"OpenFlashLight", nil)];
        //NSLog(@"isSelected == %d", sender.isSelected);
    }
}


- (void)backButtonClick:(UIButton *)sender
{
    [self dismissViewControllerAnimated:YES completion:^{
        
    }];
}

#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    UIImage *photoImage = [info valueForKey:UIImagePickerControllerEditedImage];
    NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
    [self dismissViewControllerAnimated:NO completion:^{
        if (self.openAlbum) {
            self.openAlbum(photoImage);
        }
    }];
}
- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:^{
            
    }];
}
//如果想要判断设备是ipad，要用如下方法
- (BOOL)getIsIPad
{
    NSString *deviceType = [UIDevice currentDevice].model;
    
    if([deviceType isEqualToString:@"iPhone"]) {
        //iPhone
        return NO;
    }
    else if([deviceType isEqualToString:@"iPod touch"]) {
        //iPod Touch
        return NO;
    }
    else if([deviceType isEqualToString:@"iPad"]) {
        //iPad
        return YES;
    }
    return NO;
}


@end
