//
//  CartoonViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "CartoonViewController.h"
#import "CartoonResultVC.h"
#import "MyImagePickerController.h"
#import "UIImage+FixOrientation.h"
#import "MyImagePickerResultVC.h"
#import "CheckImageHaveFace.h"
#import "ImageManager.h"

@interface CartoonViewController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate, CartoonResultVCDelegate>

@property (nonatomic, strong) UILabel *labelTips;
@property (nonatomic, strong) UIImage *photoImage;
@property (nonatomic, strong) UIButton *cartoonButton;
@property (nonatomic, strong) UIImageView *cutImageView;

@end

@implementation CartoonViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isTopButton = NO;
    
    if (self.cutImageView == nil) {
        self.cutImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, kStatusBarHeight+60, kScreenWidth, kScreenHeight-kStatusBarHeight-60-kBottomSafeHeight-20)];
        self.cutImageView.contentMode = UIViewContentModeScaleAspectFit;
        self.cutImageView.alpha = 0;
        [self.view addSubview:self.cutImageView];
    }
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    if (self.isIPad == YES) {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 120, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        } else {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 100, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        }
    } else {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
    
    [self initAddButtonAndAvatar];
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"CartoonEffectsTips1", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-66);
                make.width.equalTo(@220);
            }];
        }
    }
    
    if (self.cartoonButton == nil) {
        self.cartoonButton = [[UIButton alloc] init];
        [self.cartoonButton setTitle:NSLocalizedString(@"StartToChange", nil) forState:UIControlStateNormal];
        [self.cartoonButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [self.cartoonButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
        self.cartoonButton.layer.cornerRadius = 20;
        self.cartoonButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
        self.cartoonButton.userInteractionEnabled = YES;
        [self.view addSubview:self.cartoonButton];
        [self.cartoonButton setFrame:CGRectMake((self.view.bounds.size.width-160)/2, self.imageBox.frame.origin.y+self.imageBox.bounds.size.height+40, 160, 40)];
        [self.cartoonButton addTarget:self action:@selector(cartoonButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (void)cartoonButtonClick:(UIButton *)sender
{
    if (_photoImage) {
        UIImage *finalImage = [ImageManager compressImage:_photoImage maxSize:1300];
        NSLog(@"finalImage.size = %@", NSStringFromCGSize(finalImage.size));
        if ([CheckImageHaveFace checkHaveFaceWithImage:_photoImage]) {
            CartoonResultVC *vc = [[CartoonResultVC alloc] init];
            vc.photoImage = finalImage;
            vc.delegate = self;
            [self.navigationController pushViewController:vc animated:YES];
        } else {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips1", nil)];
        }
    }
}
//  Delegate
- (void)reselectPhotoImage
{
    self.avatarImageView.image = nil;
    self.avatarImageView.alpha = 0;
    
    self.photoImage = nil;
    
    self.cartoonButton.userInteractionEnabled = NO;
    self.cartoonButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
}


- (void)addPhotoButtonClick:(UIButton *)sender
{
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    [self presentImagePickerController];
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"CameraPowerTips1", nil) message:NSLocalizedString(@"CameraPowerTips2", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        [self presentImagePickerController];
    }
}

- (void)presentImagePickerController
{
    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        MyImagePickerController *vc = [[MyImagePickerController alloc] init];
        vc.delegate = self; //delegate遵循了两个代理
        vc.allowsEditing = NO;
        vc.sourceType = UIImagePickerControllerSourceTypeCamera;
        vc.cameraDevice = UIImagePickerControllerCameraDeviceFront;
        vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
        vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
        [self presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.openAlbum = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                self.cutImageView.alpha = 1.0;
                self.cutImageView.image = image;
                UIImage *avatarImage = [ImageManager cutImageFromImageView:self.cutImageView];
                
                self.avatarImageView.alpha = 1;
                self.avatarImageView.image = avatarImage;
                
                self.photoImage = image;
                
                self.cartoonButton.userInteractionEnabled = YES;
                self.cartoonButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
                
                self.cutImageView.alpha= 0;
            }];
        };
    }
}


#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    UIImage *photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
    photoImage = [photoImage fixOrientation];
    //改变该图片的方向
    photoImage = [UIImage imageWithCGImage:photoImage.CGImage scale:photoImage.scale orientation:UIImageOrientationUpMirrored];
    NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
    //  截取图像
    
    MyImagePickerResultVC *vc = [[MyImagePickerResultVC alloc] init];
    vc.photoImage = photoImage;
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [picker presentViewController:vc animated:YES completion:^{}];
    __weak typeof(vc) weakVC = vc;
    vc.dismissVC = ^(UIImage * _Nonnull image) {
        [weakVC dismissViewControllerAnimated:YES completion:^{
            [picker dismissViewControllerAnimated:NO completion:^{
            }];
            self.photoImage = photoImage;
            self.avatarImageView.alpha = 1;
            self.avatarImageView.image = image;
            
            self.cartoonButton.userInteractionEnabled = YES;
            self.cartoonButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
        }];
    };
}


- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:^{
            
    }];
}
@end
