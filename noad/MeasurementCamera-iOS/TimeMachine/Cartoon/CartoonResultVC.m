//
//  CartoonResultVC.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "CartoonResultVC.h"
#import "ImageTextButton.h"
#import "RSA.h"
#import "ESBFWaitView.h"
#import "FileManager.h"
#import "TimeTool.h"

@interface CartoonResultVC ()

@property (nonatomic, strong) UILabel *labelTips;

@end

@implementation CartoonResultVC

static NSString *ESNTKS_RequestUrl = @"https://ukey.godimage.mobi:8886/authkey/query";///<获取密钥的请求链接
static NSString *ESNTKS_CartoonFaceUrl = @"http://cartoon.photoeditortechnology.com:8002/process";///<获取密钥的请求链接
static NSString *ESNTKS_Pubkey = @"-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCblVmnev6bID5A9PgvPpgaH7aY\nCoroVm8ejy8oClnUprPtLLbfQ/LJV7jjFIqac2yxYgOOX10J+cDeU3GMMggoXs1w\nABBAX07E7vM4TMmMPLurckrmXn/H02Hi0loU5S6UQYyFm1TCHIlWrNBgpRHh4AO8\nxJl5pn03BZ9xbqJZRwIDAQAB\n-----END PUBLIC KEY-----";

static NSString *ESNTKS_AppIdKey = @"appId";///<应用BundleID
static NSString *ESNTKS_AppKeyKey = @"appKeys";///<密钥检索键值
static NSString *ESNTKS_AppSecretKey = @"appSecret";///<密钥密文
static NSString *ESNTKS_TimestampKey = @"timestamp";///<时间戳
static NSString *ESNTKS_Biggerlens_CartoonFace = @"biggerlens_addcolor";///<贝格蓝斯的[黑白照片上色]API
static NSString *ESNTKS_AccesskeySecret = @"accesskey_secret";///<密钥主键

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [SVProgressHUD showWithStatus:NSLocalizedString(@"CartoonEffectsTips2", nil)];
    
    self.isTopButton = YES;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    if (self.isIPad == YES) {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 120, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        } else {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 100, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        }
    } else {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
    
    [self initAddButtonAndAvatar];
    self.addButton.alpha = 0;
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"CartoonEffectsTips2", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-66);
                make.width.equalTo(@220);
            }];
        }
    }
    
    // 重新选择照片按钮
    UIButton *reselectImageButton = [[UIButton alloc] init];
    reselectImageButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    [reselectImageButton setTitle:NSLocalizedString(@"ReselectPhoto", nil) forState:UIControlStateNormal];
    [reselectImageButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [reselectImageButton addTarget:self action:@selector(reselectImageButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:reselectImageButton];
    [reselectImageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.view.mas_trailing).with.offset(0);
        make.bottom.equalTo(self.imageBox.mas_top).with.offset(-[iPhoneXTool setCommonValue:20 orStrangeValue:25]);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    if (self.isLanguageZH == YES) {
        [reselectImageButton.titleLabel setFont:[UIFont systemFontOfSize:14.0]];
    } else {
        [reselectImageButton.titleLabel setFont:[UIFont systemFontOfSize:12.0]];
    }
    
    [self.view layoutIfNeeded];
    
    if (reselectImageButton) {
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:reselectImageButton.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(20, 20)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.path = maskPath.CGPath;
        reselectImageButton.layer.masksToBounds = YES;
        reselectImageButton.layer.mask = maskLayer;
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    [self filterImage];
}

- (void)initViews
{
    ImageTextButton *saveButton = [[ImageTextButton alloc] init];
    [saveButton setImage:[UIImage imageNamed:@"保存"] title:NSLocalizedString(@"Save", nil)];
    [self.view addSubview:saveButton];
    saveButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"saveButton");
        
        UIImageWriteToSavedPhotosAlbum(self.avatarImageView.image, self, @selector(image:didFinishSavingWithError:contextInfo:), NULL);
    };
    
    ImageTextButton *shareButton = [[ImageTextButton alloc] init];
    [shareButton setImage:[UIImage imageNamed:@"分享"] title:NSLocalizedString(@"Share", nil)];
    [self.view addSubview:shareButton];
    shareButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"shareButton");
        [SVProgressHUD show];
        
        NSData *imageData = UIImageJPEGRepresentation(self.avatarImageView.image, 1.0);
        NSString *imagePath = [[FileManager pathForDocumentsFileName:ShareImage] stringByAppendingPathComponent:[NSString stringWithFormat:@"photo%@.jpg", [TimeTool getCurrentTime]]];
        [imageData writeToFile:imagePath atomically:YES];
        //NSLog(@"imagePath == %@", imagePath);
        
        // 弹出分享框并进行分享
        NSArray *items = [NSArray arrayWithObjects:[NSURL fileURLWithPath:imagePath], nil];
        UIActivityViewController *activityViewController =
        [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
        activityViewController.excludedActivityTypes = @[UIActivityTypeAirDrop];
        
        // 分享之后的回调
        activityViewController.completionWithItemsHandler = ^(UIActivityType  _Nullable activityType, BOOL completed, NSArray * _Nullable returnedItems, NSError * _Nullable activityError) {
            if (completed) {
                
            } else if (activityError) {
                
            }
        };
        if (self.isIPad) {
            activityViewController.popoverPresentationController.sourceView = self.view;
            activityViewController.popoverPresentationController.sourceRect = CGRectMake(self.view.frame.size.width/2.0, self.view.frame.size.height/2.0, 1, 1);
            activityViewController.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionUp;
        }
        [self presentViewController:activityViewController animated:YES completion:^{
            [SVProgressHUD dismiss];
        }];
    };
    
    [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(-[iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
    
    [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset([iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
}

- (void)reselectImageButtonClick:(UIButton *)sender
{
    if ([self.delegate respondsToSelector:@selector(reselectPhotoImage)]) {
        [self.delegate reselectPhotoImage];
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    if(error != NULL) {
        [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"PictureSaveFailedTips", nil)];
    } else {
        [SVProgressHUD showSuccessWithStatus:NSLocalizedString(@"PictureSaveSucceedTips", nil)];
    }
}

- (void)filterImage
{
    [[ESBFWaitView share] addDarkCurtainView:self.view];
    [self loadKeySecretSuccess:^(NSDictionary * _Nullable responseObject) {
        NSDictionary *secretDict = responseObject[ESNTKS_Biggerlens_CartoonFace];
        NSString *secret = secretDict[ESNTKS_AccesskeySecret];
        NSString *url = ESNTKS_CartoonFaceUrl;
        NSLog(@"密钥获取成功!");
        [self post:url
         fileImage:self.photoImage
            secret:secret
           success:^(id responseObj) {
            [SVProgressHUD dismiss];
            UIImage *image = responseObj;
            self.avatarImageView.alpha = 1.0;
            [self.avatarImageView setImage:image];
            [self.labelTips setText:NSLocalizedString(@"CartoonEffectsTips3", nil)];
            [self initViews];
            [[ESBFWaitView share] removeDarkCurtainView];
        } failure:^(NSError *error) {
            NSLog(@"%@", error);
            [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"CartoonEffectsTips4", nil)];
            [self.labelTips setText:NSLocalizedString(@"CartoonEffectsTips4", nil)];
            [[ESBFWaitView share] removeDarkCurtainView];
        }];
    } failure:^(NSError *error) {
        NSLog(@"%@", error);
        [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"CartoonEffectsTips4", nil)];
        [self.labelTips setText:NSLocalizedString(@"CartoonEffectsTips4", nil)];
        [[ESBFWaitView share] removeDarkCurtainView];
    }];
}

/// 请求卡通人脸的api
- (NSURLSessionDataTask *)post:(NSString *)url
                     fileImage:(UIImage *)fileImage
                        secret:(NSString *)secret
                       success:(void(^)(id responseObj))success
                       failure:(void(^)(NSError *error))failure {
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer = [AFHTTPRequestSerializer serializer];
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    [manager.requestSerializer setHTTPShouldHandleCookies:NO];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json",
                                                         @"text/json",
                                                         @"text/plain",
                                                         @"text/html",
                                                         @"image/jpeg",
                                                         @"image/png",
                                                         @"multipart/form-data",
                                                         nil];
    NSString *pubkey = ESNTKS_Pubkey;
    NSDictionary *paras = @{@"appKey":secret, @"timestamp": @((long)([NSDate date].timeIntervalSince1970))};
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:paras options:NSJSONWritingFragmentsAllowed error:nil];
    NSString *originString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSString *encryptString = [RSA encryptString:originString publicKey:pubkey];
    
    NSMutableDictionary *parameters = [NSMutableDictionary  dictionary];
    [parameters setValue:encryptString forKey:@"para"];
    NSURLSessionDataTask *dataTask = [manager POST:url
                                        parameters:parameters
                                           headers:@{}
                         constructingBodyWithBlock:^(id<AFMultipartFormData>  _Nonnull formData) {
        NSData *imageData = fileImage.sd_imageData;
        [formData appendPartWithFileData:imageData name:@"file" fileName:@"file.png" mimeType:@"image/png"];
    } progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        UIImage *image = [UIImage imageWithData:responseObject];
        if (success) {
            success(image);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (failure) {
            failure(error);
        }
    }];
    return dataTask;
}

/// 获取线上密钥集合
- (void)loadKeySecretSuccess:(void (^ __nullable)(NSDictionary * _Nullable responseObject))success failure:(nullable void (^)(NSError *error))failure{
    NSDictionary *queryDataSource = @{
        ESNTKS_AppIdKey:  @"com.pkk.dev",
        ESNTKS_AppKeyKey: @[ESNTKS_Biggerlens_CartoonFace],
        ESNTKS_TimestampKey : @([NSDate date].timeIntervalSince1970)
    };
    NSMutableDictionary *requestBody = [NSMutableDictionary dictionaryWithDictionary:queryDataSource];
    [requestBody setObject:@([NSDate date].timeIntervalSince1970) forKey:ESNTKS_TimestampKey];
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:requestBody options:NSJSONWritingFragmentsAllowed error:nil];
    NSString *originString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSString *encryptString = [RSA encryptString:originString publicKey:ESNTKS_Pubkey];
    AFHTTPSessionManager *manager = [AFHTTPSessionManager manager];
    manager.requestSerializer.timeoutInterval = 8.0f;
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"application/json", @"text/html", nil];
    manager.requestSerializer.cachePolicy = NSURLRequestReloadIgnoringCacheData;
    [manager POST:ESNTKS_RequestUrl parameters:@{@"para":encryptString}
          headers:@{}
         progress:nil
          success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSDictionary *dataDict = ((NSDictionary*)responseObject)[@"data"];
        NSArray <NSDictionary*> *keySecretArr = dataDict[@"appKeys"];
        NSMutableDictionary *keySecretMdict = [NSMutableDictionary dictionary];
        for (NSDictionary *keySecret in keySecretArr) {
            NSString *keySecretStr = keySecret[ESNTKS_AppSecretKey];
            NSData *keySecretData = [keySecretStr dataUsingEncoding:NSUTF8StringEncoding];
            NSDictionary *keySecretDict = [NSJSONSerialization JSONObjectWithData:keySecretData options:NSJSONReadingAllowFragments error:nil];
            [keySecretMdict setObject:keySecretDict forKey:keySecret[@"appKey"]];
        }
        if(success){
            success(keySecretMdict);
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if (failure) {
            failure(error);
        }
    }];
}



@end
