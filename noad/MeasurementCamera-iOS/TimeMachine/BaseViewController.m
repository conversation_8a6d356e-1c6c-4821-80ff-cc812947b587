//
//  BaseViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import "BaseViewController.h"

@interface BaseViewController ()<MyAdManagerDelegate>
@property (nonatomic, strong) MyAdManager *adManager;
@end

@implementation BaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.view.backgroundColor = [MyColor colorWithHexString:@"#040412" alpha:1.0];
    
    self.isIPad = [self getIsIPad];
    
    self.isLanguageZH = [self getCurrentLanguageIsZH];
}

- (void)initBgImgView
{
    UIImageView *bgImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, self.view.bounds.size.height)];
    [bgImgView setImage:[UIImage imageNamed:@"首页背景"]];
    [bgImgView setContentMode:UIViewContentModeScaleAspectFill];
    [self.view addSubview:bgImgView];
}

- (void)initBackButton
{
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(5, kStatusBarHeight, 50, 50)];
    [backButton setImage:[UIImage imageNamed:@"返回"] forState:UIControlStateNormal];
    [backButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:backButton];
    self.backButton = backButton;
}

- (void)creatTitleLabel:(NSString*)label
{
    UILabel *tiltleLabel = [UILabel createLabelWithTitle:label textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentCenter font:bigMedlumFont];
    [tiltleLabel sizeToFit];
    [self.view addSubview:tiltleLabel];
    tiltleLabel.centerY = self.backButton.centerY;
    tiltleLabel.centerX = SCREEN_WIDTH*1.0/2;
    self.tiltleLabel = tiltleLabel;
}

- (void)initImageBox
{
    if (self.imageBox == nil) {
        self.imageBox = [[UIImageView alloc] init];
        [self.imageBox setImage:[UIImage imageNamed:@"背景框1"]];
        [self.imageBox setContentMode:UIViewContentModeScaleAspectFit];
        [self.imageBox setUserInteractionEnabled:YES];
        [self.view addSubview:self.imageBox];
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
}

- (void)initAddButtonAndAvatar
{
    if (self.addButton == nil  &&  self.imageBox != nil) {
        self.addButton = [[UIButton alloc] init];
        [self.addButton setImage:[UIImage imageNamed:@"添加"] forState:UIControlStateNormal];
        [self.addButton addTarget:self action:@selector(addPhotoButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.imageBox addSubview:self.addButton];
        
        [_addButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_imageBox.mas_centerX).with.offset(0);
            make.centerY.equalTo(_imageBox.mas_centerY).with.offset(-(self.imageBox.bounds.size.width*0.05));
            make.width.equalTo(@(self.imageBox.bounds.size.width*0.5));
            make.height.equalTo(@(self.imageBox.bounds.size.width*0.5));
        }];
        
        //  有添加按钮就会有头像。  一开始头像是隐藏的
        if (self.avatarImageView == nil) {
            self.avatarImageView = [[UIImageView alloc] init];
            self.avatarImageView.backgroundColor = [UIColor clearColor];
            self.avatarImageView.layer.masksToBounds = YES;
            self.avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
            self.avatarImageView.clipsToBounds = YES;
            [self.imageBox addSubview:self.avatarImageView];
            self.avatarImageView.alpha = 0;
            CGFloat avatarSize = self.imageBox.bounds.size.width*0.53;
            self.avatarImageView.layer.cornerRadius = avatarSize / 2;
            [_avatarImageView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_addButton.mas_centerX).with.offset(0);
                make.centerY.equalTo(_addButton.mas_centerY).with.offset(0);
                make.width.equalTo(@(avatarSize));
                make.height.equalTo(@(avatarSize));
            }];
        }
    }
}

- (void)backButtonClick:(UIButton *)sender
{
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)addPhotoButtonClick:(UIButton *)sender
{
    NSLog(@"addPhotoButtonClick");
}

//如果想要判断设备是ipad，要用如下方法
- (BOOL)getIsIPad
{
    NSString *deviceType = [UIDevice currentDevice].model;
    
    if([deviceType isEqualToString:@"iPhone"]) {
        //iPhone
        return NO;
    }
    else if([deviceType isEqualToString:@"iPod touch"]) {
        //iPod Touch
        return NO;
    }
    else if([deviceType isEqualToString:@"iPad"]) {
        //iPad
        return YES;
    }
    return NO;
}

// 判断是中文还是其他语言
- (BOOL)getCurrentLanguageIsZH
{
    BOOL isZH = NO;
    
    NSString *currentLanguage1 = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"][0];
    NSString *currentLanguage2 = [[NSBundle mainBundle] preferredLocalizations][0];
    NSLog(@"language1 == %@   language2 == %@", currentLanguage1, currentLanguage2);

    if ([currentLanguage1 containsString:@"zh-Ha"]  ||  [currentLanguage2 containsString:@"zh-Ha"]) {
        isZH = YES;
        NSLog(@"这个是中文");
    }
    
    return isZH;
}

- (void)showFullscreenVideoAd
{
    
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count&&[APPMakeStoreIAPManager featureVip]!=YES) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        //NSLog(@"showInterstitialAdCount = %d", appDelegate.showInterstitialAdCount);
        NSLog(@"appDelegate.network = %ld", (long)appDelegate.network);
        if (appDelegate.network > 0  ) {
            
            if (self.adManager == nil) {
                self.adManager = [[MyAdManager alloc] init];
                self.adManager.delegate = self;
            }
                [self.adManager showFullscreenVideoAdWithViewController:self];
            
        }
    }
}
- (void)showBannerView:(CGRect)frame size:(CGSize)size
{
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count&&[APPMakeStoreIAPManager featureVip]!=YES) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        //NSLog(@"showInterstitialAdCount = %d", appDelegate.showInterstitialAdCount);
        NSLog(@"appDelegate.network = %ld", (long)appDelegate.network);
        if (appDelegate.network > 0 ) {
            
            if (self.adManager == nil) {
                self.adManager = [[MyAdManager alloc] init];
                self.adManager.delegate = self;
            }
                [self.adManager showBannerAdWithViewController:self frame:frame size:size];
            
        }
    }
}
@end
