//
//  AppDelegate.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "AppDelegate.h"
#import <StoreKit/StoreKit.h>
#import "Reachability.h"
#import "BSLaunchScreenVC.h"
#import "ViewController.h"
#import "MainTabbarVC.h"
@interface AppDelegate ()<BUSplashAdDelegate>
@property (nonatomic, strong) Reachability *internetReachability;
@property (nonatomic, strong) BUSplashAd *splashAd;
@end

@implementation AppDelegate


- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // Override point for customization after application launch.
    self.window = [[UIWindow alloc]initWithFrame:[UIScreen mainScreen].bounds];
    
    self.showGuide = YES;
    

    AFHTTPSessionManager *_sessionManager = [AFHTTPSessionManager manager];;
    _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
    [_sessionManager.requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
    _sessionManager.requestSerializer.cachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
    [_sessionManager GET:updateJsonUrl parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSDictionary* dic = responseObject;
        if([dic isKindOfClass:[NSDictionary class]])
        {
           
            self.showGuide = [dic[@"showGuide"] boolValue];
//
//            self.showWebLink = YES;
//            self.isAnyOK =  YES;
            [self enterLaunchView];
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        self.showGuide = YES;
        [self enterLaunchView];
    }];
    [self enterLaunchView];
    
    
    [APPMakeStoreIAPManager sharedManager];
    [[UIApplication sharedApplication] setStatusBarStyle:UIStatusBarStyleLightContent];
    
    NSInteger timers = [W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]+1;
        W_PERSISTENT_SET_OBJECT(@(timers), startWithADTimes);
    
    
    UpdateManager* date = [UpdateManager shareManagerWith:updateJsonUrl];
    date.complete = ^(id  _Nonnull sender) {
        
    };
    if(W_PERSISTENT_GET_OBJECT(@"ad_count"))
        date.AD_count = [W_PERSISTENT_GET_OBJECT(@"ad_count") intValue];
    else
        date.AD_count = 3;

    //[[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"isFirstMagicFace"];
    NSString *isFirstMagicFace = [[NSUserDefaults standardUserDefaults] objectForKey:@"isFirstMagicFace"];
    if ([isFirstMagicFace isEqualToString:@""] || isFirstMagicFace.length == 0) {
        [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"isFirstMagicFace"];
    }
    BUAdSDKConfiguration *configuration = [BUAdSDKConfiguration configuration];
        configuration.appID = @"5407162";
    [BUAdSDKManager startWithAsyncCompletionHandler:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            [self showSplashAd];
        }
    }];
    
    
    // 监听网络状态改变
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(networkStateChanged:) name:kReachabilityChangedNotification object:nil];
    // 创建Reachability
    self.internetReachability = [Reachability reachabilityForInternetConnection];
    // 开始监控网络,若网络状态改变, 就会发出通知kReachabilityChangedNotification
    [self.internetReachability startNotifier];
    switch ([self.internetReachability currentReachabilityStatus]) {
        case NotReachable:
            //没有网
            NSLog(@"没有网络");
            self.network = 0;
            break;
        case ReachableViaWiFi:
            NSLog(@"WIFI网络");
            self.network = 1;
            
            break;
        case ReachableViaWWAN:
            NSLog(@"手机自带网络");
            self.network = 2;
            
            break;
        default:
            break;
    }
    
    
    [UMConfigure setEncryptEnabled:NO];
    [UMConfigure setLogEnabled:NO];
    UMAPMConfig* config = [UMAPMConfig defaultConfig];

    config.crashAndBlockMonitorEnable = YES;
    config.memMonitorEnable = YES;
    config.oomMonitorEnable = YES;
    config.launchMonitorEnable = YES;
    config.networkEnable = YES;
    config.pageMonitorEnable = YES;
    
    [UMCrashConfigure setAPMConfig:config];
    
    [UMConfigure initWithAppkey:@"64f57e1c8efadc41dcd3d56c" channel:@"App Store"];
//    [MobClick setScenarioType:E_UM_NORMAL];//支持普通场景

    [MobClick setAutoPageEnabled:YES];
    
    [SVProgressHUD setDefaultMaskType:SVProgressHUDMaskTypeClear];
    
    return YES;
    return YES;
}
- (void)enterLaunchView
{
    if(![APPMakeStoreIAPManager featureVip])
    {
        BSLaunchScreenVC *launchScreenVc = [[BSLaunchScreenVC alloc] init];
        UINavigationController *launchScreenNav = [[UINavigationController alloc] initWithRootViewController:launchScreenVc];
        [self.window setRootViewController:launchScreenNav];
        [self.window makeKeyAndVisible];

    }
    else
    {
//        ViewController *launchScreenVc = [[ViewController alloc] init];
//        UINavigationController *launchScreenNav = [[UINavigationController alloc] initWithRootViewController:launchScreenVc];
//        [self.window setRootViewController:launchScreenNav];
        MainTabbarVC *vc = [[MainTabbarVC alloc] init];
        [self.window setRootViewController:vc];
        [self.window makeKeyAndVisible];

    }
}
- (void)networkStateChanged:(NSNotification *)note
{
    NSLog(@"networkStateChanged");
    //Reachability *reachability = [Reachability reachabilityWithHostName:@"www.apple.com"];
    Reachability *reachability = [note object];
    switch ([reachability currentReachabilityStatus]) {
        case NotReachable:
            //没有网
            NSLog(@"没有网络");
            self.network = 0;
            break;
        case ReachableViaWiFi:
            NSLog(@"WIFI网络");
            self.network = 1;
            break;
        case ReachableViaWWAN:
            NSLog(@"手机自带网络");
            self.network = 2;
            break;
        default:
            break;
    }
}


#pragma mark - UISceneSession lifecycle


- (void)showSplashAd
{
    BOOL isZH = [self getCurrentLanguageIsZH];
    if ([W_PERSISTENT_GET_OBJECT(startWithADTimes) integerValue]>=[UpdateManager shareManagerWith:updateJsonUrl].AD_count&&[APPMakeStoreIAPManager featureVip]!=YES) {
        self.splashAd = [[BUSplashAd alloc] initWithSlotID:@"888356142" adSize:[UIScreen mainScreen].bounds.size];
        self.splashAd.delegate = self;
        [self.splashAd loadAdData];
    }
}

- (void)dismissSplashAd
{
    if (self.splashAd) {
        self.splashAd = nil;
    }
    [[NSNotificationCenter defaultCenter] postNotificationName:@"DismissSplashAdNotification" object:nil userInfo:nil];
}

- (void)splashAdLoadSuccess:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdLoadSuccess");
    if (splashAd == self.splashAd) {
        dispatch_async(dispatch_get_main_queue(), ^{
            UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
                [self.splashAd showSplashViewInRootViewController:keyWindow.rootViewController];
        });
    }
}

/// This method is called when material load failed
- (void)splashAdLoadFail:(BUSplashAd *)splashAd error:(BUAdError *_Nullable)error
{
    NSLog(@"splashAdLoadFail");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view render successful
- (void)splashAdRenderSuccess:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdRenderSuccess");
}

/// This method is called when splash view render failed
- (void)splashAdRenderFail:(BUSplashAd *)splashAd error:(BUAdError *_Nullable)error
{
    NSLog(@"splashAdRenderFail");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view will show
- (void)splashAdWillShow:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdWillShow");
}

/// This method is called when splash view did show
- (void)splashAdDidShow:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdDidShow");
}

/// This method is called when splash view is clicked.
- (void)splashAdDidClick:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdDidClick");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash view is closed.
- (void)splashAdDidClose:(BUSplashAd *)splashAd closeType:(BUSplashAdCloseType)closeType
{
    NSLog(@"splashAdDidClose");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}

/// This method is called when splash viewControllr is closed.
- (void)splashAdViewControllerDidClose:(BUSplashAd *)splashAd
{
    NSLog(@"splashAdViewControllerDidClose");
    if (splashAd == self.splashAd) {
        [self dismissSplashAd];
    }
}


// 判断是中文还是其他语言
- (BOOL)getCurrentLanguageIsZH
{
    BOOL isZH = NO;
    
    NSString *currentLanguage1 = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"][0];
    NSString *currentLanguage2 = [[NSBundle mainBundle] preferredLocalizations][0];
    NSLog(@"language1 == %@   language2 == %@", currentLanguage1, currentLanguage2);

    if ([currentLanguage1 containsString:@"zh-Ha"]  ||  [currentLanguage2 containsString:@"zh-Ha"]) {
        isZH = YES;
        NSLog(@"这个是中文");
    }
    
    return isZH;
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    
    BaseViewController * vc = [AppDelegate getCurrentVC];
    if([vc isKindOfClass:[BaseViewController class]])
    {
        [vc showFullscreenVideoAd];
        
    }
    
    // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
}

+ (UIViewController *)getCurrentVC
{
    UIViewController *rootViewController = [UIApplication sharedApplication].keyWindow.rootViewController;
    
    UIViewController *currentVC = [self getCurrentVCFrom:rootViewController];
    
    return currentVC;
}

+ (UIViewController *)getCurrentVCFrom:(UIViewController *)rootVC
{
    UIViewController *currentVC;
    
    if ([rootVC presentedViewController]) {
        // 视图是被presented出来的
        rootVC = [rootVC presentedViewController];
    }
    
    if ([rootVC isKindOfClass:[UITabBarController class]]) {
        // 根视图为UITabBarController
        currentVC = [self getCurrentVCFrom:[(UITabBarController *)rootVC selectedViewController]];
    } else if ([rootVC isKindOfClass:[UINavigationController class]]){
        // 根视图为UINavigationController
        currentVC = [self getCurrentVCFrom:[(UINavigationController *)rootVC visibleViewController]];
    } else {
        // 根视图为非导航类
        currentVC = rootVC;
    }
    
    return currentVC;
}

+ (void)changeIcon:(NSString *)btn {
   
    if (@available(iOS 10.3, *)) {
        // 判断系统是否支持
        if (![[UIApplication sharedApplication] supportsAlternateIcons]) {
            return;
        }
    }
        
    if (@available(iOS 10.3, *)) {
        // 设备指定图片，iconName为info.plist中的key
        [[UIApplication sharedApplication] setAlternateIconName:btn completionHandler:^(NSError * error) {
            if (error) {
                NSLog(@"更换app图标发生错误了 ： %@",error);
            }
        }];
    }
}


+ (instancetype)getAppDelegate {
    return (AppDelegate *)[UIApplication sharedApplication].delegate;
}
@end
