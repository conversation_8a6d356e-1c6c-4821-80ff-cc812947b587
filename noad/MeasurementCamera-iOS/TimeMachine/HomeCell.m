//
//  HomeCell.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import "HomeCell.h"

@implementation HomeCell


- (id)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        // Initialization code

        [self setBackgroundColor:[MyColor colorWithHexString:@"#11111D" alpha:1.0]];
        [self setSelectionStyle:UITableViewCellSelectionStyleNone];
        [self initView];
        
    }
    return self;
}

- (void)initView
{
    CGFloat cellHeight = 80.0;
    [self setFrame:CGRectMake(0, 0, kScreenWidth, cellHeight)];
    
    if (self.bgView == nil) {
        self.bgView = [[UIView alloc] initWithFrame:CGRectMake(12, 5, self.bounds.size.width-24, cellHeight-10)];
        self.bgView.backgroundColor = [MyColor colorWithHexString:@"#040412" alpha:1.0];
        self.bgView.layer.cornerRadius = 6.0;
        [self addSubview:self.bgView];
    }
    
    if (self.labelTitle == nil) {
        self.labelTitle = [[UILabel alloc] init];
        [self.labelTitle setTextColor:[UIColor whiteColor]];
        [self.labelTitle setFont:[UIFont systemFontOfSize:15.0]];
        //self.labelTitle.backgroundColor = [UIColor orangeColor];
        [self.contentView addSubview:self.labelTitle];
        
        [_labelTitle mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_bgView.mas_top).with.offset(16);
            make.leading.equalTo(_bgView.mas_leading).with.offset(12);
            make.width.equalTo(@(self.bounds.size.width-110));
            make.height.equalTo(@18);
        }];
    }
    
    if (self.labelDesc == nil) {
        self.labelDesc = [[UILabel alloc] init];
        [self.labelDesc setTextColor:[UIColor lightGrayColor]];
        [self.labelDesc setFont:[UIFont systemFontOfSize:12.5]];
        [self.contentView addSubview:self.labelDesc];
        
        [_labelDesc mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_labelTitle.mas_bottom).with.offset(5);
            make.leading.equalTo(_bgView.mas_leading).with.offset(12);
            make.width.equalTo(@(self.bounds.size.width-110));
            make.height.equalTo(@14);
        }];
    }
    
    if (self.imgView == nil) {
        self.imgView = [[UIImageView alloc] init];
        self.imgView.image = [UIImage imageNamed:@"首页心理牌"];
        [self.imgView setContentMode:UIViewContentModeScaleAspectFit];
        [self.contentView addSubview:self.imgView];
        
        [_imgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(_bgView.mas_top).with.offset(12);
            make.trailing.equalTo(_bgView.mas_trailing).with.offset(-15);
            make.width.equalTo(@(cellHeight-10-24));
            make.height.equalTo(@(cellHeight-10-23));
        }];
    }
}


- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end
