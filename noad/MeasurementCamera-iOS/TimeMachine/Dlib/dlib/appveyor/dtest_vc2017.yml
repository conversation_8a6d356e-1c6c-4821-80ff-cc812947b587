version: "{build}"

configuration: Release

image: Visual Studio 2017

build_script:
  # build test
  - mkdir %APPVEYOR_BUILD_FOLDER%\build_test
  - cd %APPVEYOR_BUILD_FOLDER%\build_test
  - cmake -G "Visual Studio 15 2017 Win64" -T host=x64 ../dlib/test
  - cmake --build . --config %CONFIGURATION% --target dlib_all_source_cpp 
  - cmake --build . --config %CONFIGURATION% --target imglab 
  - cmake --build . --config %CONFIGURATION% --target htmlify 
  - cmake --build . --config %CONFIGURATION% --target gui 
  - cmake --build . --config %CONFIGURATION% --target dtest

test_script:
  # run test
  - cd %APPVEYOR_BUILD_FOLDER%\build_test\%CONFIGURATION%
  - dtest --runall
