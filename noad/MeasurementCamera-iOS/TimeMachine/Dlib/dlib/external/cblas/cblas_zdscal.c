/*
 * cblas_zdscal.c
 *
 * The program is a C interface to zdscal.
 *
 * Written by <PERSON><PERSON>.  2/11/1998
 *
 */
#include "cblas.h"
#include "cblas_f77.h"
void cblas_zdscal( const CBLAS_INT_TYPE N, const double alpha, void  *X,
                       const CBLAS_INT_TYPE incX)
{
#ifdef F77_INT
   F77_INT F77_N=N, F77_incX=incX;
#else 
   #define F77_N N
   #define F77_incX incX
#endif
   F77_zdscal( &F77_N, &alpha, X, &F77_incX);
}
