//
//  YoungViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import "YoungViewController.h"
#import "MyImagePickerController.h"
#import <AssetsLibrary/AssetsLibrary.h>

@interface YoungViewController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) NSArray *ageArray;
//@property (nonatomic, strong) MyImagePickerController *imagePickerController;
@property (nonatomic, strong) UILabel *labelTips;

@end

@implementation YoungViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBarHidden = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.ageArray = [NSArray arrayWithObjects:@"10岁", @"20岁",nil];
    
    self.isTopButton = NO;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    
    [self initAddButtonAndAvatar];
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:@"可以帮你重返年轻啦!\n添加照片试试吧~"];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        
        [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
            make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-80);
            make.width.equalTo(@220);
        }];
    }
    
    // 选择年龄按钮
    CGFloat selectAgeButtonEdge = [iPhoneXTool setCommonValue:70 orStrangeValue:60];
    for (int i = 0; i < _ageArray.count; i++) {
        UIButton *selectAgeButton = [[UIButton alloc] init];
        [selectAgeButton setTitle:_ageArray[i] forState:UIControlStateNormal];
        [selectAgeButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [selectAgeButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
        selectAgeButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
        selectAgeButton.layer.cornerRadius = 20;
        [self.view addSubview:selectAgeButton];
        
        switch (i) {
            case 0:
                [selectAgeButton setFrame:CGRectMake(selectAgeButtonEdge, self.imageBox.frame.origin.y+self.imageBox.bounds.size.height+40, (kScreenWidth-selectAgeButtonEdge*2-30)/2, 40)];
                break;
            case 1:
                [selectAgeButton setFrame:CGRectMake(kScreenWidth/2+15, self.imageBox.frame.origin.y+self.imageBox.bounds.size.height+40, (kScreenWidth-selectAgeButtonEdge*2-30)/2, 40)];
                break;
                
            default:
                break;
        }
    }
}

- (void)addPhotoButtonClick:(UIButton *)sender
{
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    //[imagePickerController dismissViewControllerAnimated:YES completion:nil];
                    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                        MyImagePickerController *imagePickerController = [[MyImagePickerController alloc] init];
                        imagePickerController.delegate = self; //delegate遵循了两个代理
                        imagePickerController.allowsEditing = NO;
                        imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
                        imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceFront;
                        imagePickerController.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
                        imagePickerController.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
                        [self presentViewController:imagePickerController animated:YES completion:^{
                        
                        }];
                    }
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"未获得相机权限" message:@"开启后才能使用拍照功能" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"好的" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
            MyImagePickerController *imagePickerController = [[MyImagePickerController alloc] init];
            imagePickerController.delegate = self; //delegate遵循了两个代理
            imagePickerController.allowsEditing = NO;
            imagePickerController.sourceType = UIImagePickerControllerSourceTypeCamera;
            imagePickerController.cameraDevice = UIImagePickerControllerCameraDeviceFront;
            imagePickerController.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
            imagePickerController.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
            [self presentViewController:imagePickerController animated:YES completion:^{
            
            }];
        }
    }
}


#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    [picker dismissViewControllerAnimated:YES completion:^{
    }];
    UIImage *image = [info valueForKey:UIImagePickerControllerOriginalImage];
    NSLog(@"image == %@", image);
    self.avatarImageView.contentMode = UIViewContentModeScaleAspectFill;
    self.avatarImageView.alpha = 1;
    self.avatarImageView.image = image;
}


@end
