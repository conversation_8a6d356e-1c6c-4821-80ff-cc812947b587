//
//  BabyViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "BabyViewController.h"
#import "BabyResultVC.h"
#import "MyImagePickerController.h"
#import "UIImage+FixOrientation.h"
#import "MyImagePickerResultVC.h"
#import "CheckImageHaveFace.h"
#import "ImageManager.h"

@interface BabyViewController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) UIImageView *bgImageBox;
@property (nonatomic, strong) UIButton *addFatherButton;
@property (nonatomic, strong) UIButton *addMotherButton;
@property (nonatomic, strong) UIImageView *fatherImageView;
@property (nonatomic, strong) UIImageView *motherImageView;
@property (nonatomic, strong) UIImage *fatherImage;
@property (nonatomic, strong) UIImage *motherImage;
@property (nonatomic) BOOL isSelectFatherImage;

@property (nonatomic, strong) NSArray *sexArray;
@property (nonatomic, strong) UILabel *labelTips;

@property (nonatomic, strong) UIImageView *cutImageView;

@end

@implementation BabyViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBarHidden = YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.sexArray = [NSArray arrayWithObjects:@"男宝宝", @"女宝宝",nil];
    
    if (self.cutImageView == nil) {
        self.cutImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, kStatusBarHeight+60, kScreenWidth, kScreenHeight-kStatusBarHeight-60-kBottomSafeHeight-20)];
        self.cutImageView.contentMode = UIViewContentModeScaleAspectFit;
        self.cutImageView.alpha = 0;
        [self.view addSubview:self.cutImageView];
        self.cutImageView.backgroundColor = [UIColor orangeColor];
    }
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initViews];
}

- (void)initViews
{
    
    if (self.bgImageBox == nil) {
        self.bgImageBox = [[UIImageView alloc] init];
        [self.bgImageBox setImage:[UIImage imageNamed:@"背景框2"]];
        [self.bgImageBox setContentMode:UIViewContentModeScaleAspectFit];
        [self.bgImageBox setUserInteractionEnabled:YES];
        [self.view addSubview:self.bgImageBox];
        [self.bgImageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:57 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-114 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-114)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        NSLog(@"bgImageBox.frame == %@",  NSStringFromCGRect(self.bgImageBox.frame));
    }
    
    CGFloat addButtonLeading = _bgImageBox.bounds.size.width * 0.398;
    if (self.addFatherButton == nil) {
        CGFloat addButtonTop = _bgImageBox.bounds.size.height * 0.262;
        
        UIView *addFatherView = [[UIView alloc] initWithFrame:CGRectMake(addButtonLeading, addButtonTop, 1, 1)];
        addFatherView.backgroundColor = [UIColor whiteColor];
        [self.bgImageBox addSubview:addFatherView];
        
        self.addFatherButton = [[UIButton alloc] init];
        [self.addFatherButton setImage:[UIImage imageNamed:@"添加"] forState:UIControlStateNormal];
        [self.addFatherButton addTarget:self action:@selector(addFatherButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.bgImageBox addSubview:self.addFatherButton];
        [_addFatherButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(addFatherView.mas_centerX).with.offset(0);
            make.centerY.equalTo(addFatherView.mas_centerY).with.offset(0);
            make.width.equalTo(@88);
            make.height.equalTo(@88);
        }];
        
        UILabel *label = [[UILabel alloc] init];
        label.text = @"爸爸";
        label.textColor = [UIColor whiteColor];
        label.font = [UIFont systemFontOfSize:14.0];
        [_bgImageBox addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(_bgImageBox.mas_leading).with.offset(_bgImageBox.bounds.size.width * 0.68);
            make.centerY.equalTo(addFatherView.mas_centerY).with.offset(0);
            make.width.equalTo(@32);
            make.height.equalTo(@16);
        }];
    }
    if (self.addMotherButton == nil) {
        CGFloat addButtonTop = _bgImageBox.bounds.size.height * 0.622;
        UIView *addMotherView = [[UIView alloc] initWithFrame:CGRectMake(addButtonLeading, addButtonTop, 1, 1)];
        addMotherView.backgroundColor = [UIColor whiteColor];
        [self.bgImageBox addSubview:addMotherView];
        
        self.addMotherButton = [[UIButton alloc] init];
        [self.addMotherButton setImage:[UIImage imageNamed:@"添加"] forState:UIControlStateNormal];
        [self.addMotherButton addTarget:self action:@selector(addMotherButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.bgImageBox addSubview:self.addMotherButton];
        [_addMotherButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(addMotherView.mas_centerX).with.offset(0);
            make.centerY.equalTo(addMotherView.mas_centerY).with.offset(0);
            make.width.equalTo(@88);
            make.height.equalTo(@88);
        }];
        
        UILabel *label = [[UILabel alloc] init];
        label.text = @"妈妈";
        label.textColor = [UIColor whiteColor];
        label.font = [UIFont systemFontOfSize:14.0];
        [_bgImageBox addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(_bgImageBox.mas_leading).with.offset(_bgImageBox.bounds.size.width * 0.68);
            make.centerY.equalTo(addMotherView.mas_centerY).with.offset(0);
            make.width.equalTo(@32);
            make.height.equalTo(@16);
        }];
    }
    
    CGFloat avatarSize = self.bgImageBox.bounds.size.width / 300 * 108;
    if (self.fatherImageView == nil) {
        self.fatherImageView = [[UIImageView alloc] init];
        self.fatherImageView.backgroundColor = [UIColor whiteColor];
        self.fatherImageView.layer.masksToBounds = YES;
        self.fatherImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.fatherImageView.clipsToBounds = true;
        [self.bgImageBox addSubview:self.fatherImageView];
        self.fatherImageView.alpha = 1;
        self.fatherImageView.layer.cornerRadius = avatarSize / 2;
        [_fatherImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_addFatherButton.mas_centerX).with.offset(0);
            make.centerY.equalTo(_addFatherButton.mas_centerY).with.offset(0);
            make.width.equalTo(@(avatarSize));
            make.height.equalTo(@(avatarSize));
        }];
    }
    
    if (self.motherImageView == nil) {
        self.motherImageView = [[UIImageView alloc] init];
        self.motherImageView.backgroundColor = [UIColor whiteColor];
        self.motherImageView.layer.masksToBounds = YES;
        self.motherImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.motherImageView.clipsToBounds = true;
        [self.bgImageBox addSubview:self.motherImageView];
        self.motherImageView.alpha = 1;
        self.motherImageView.layer.cornerRadius = avatarSize / 2;
        [_motherImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_addMotherButton.mas_centerX).with.offset(0);
            make.centerY.equalTo(_addMotherButton.mas_centerY).with.offset(0);
            make.width.equalTo(@(avatarSize));
            make.height.equalTo(@(avatarSize));
        }];
    }
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:@"添加爸爸和妈妈的照片吧~"];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.bgImageBox addSubview:self.labelTips];
        
        [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_bgImageBox.mas_centerX).with.offset(0);
            make.bottom.equalTo(_bgImageBox.mas_bottom).with.offset(-72);
            make.width.equalTo(@220);
        }];
    }
    
    // 选择性别按钮
    CGFloat selectSexButtonEdge = [iPhoneXTool setCommonValue:70 orStrangeValue:60];
    for (int i = 0; i < _sexArray.count; i++) {
        UIButton *selectSexButton = [[UIButton alloc] init];
        [selectSexButton setTitle:_sexArray[i] forState:UIControlStateNormal];
        [selectSexButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [selectSexButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
        selectSexButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
        selectSexButton.layer.cornerRadius = 20;
        selectSexButton.tag = 10000+i;
        [self.view addSubview:selectSexButton];
        [selectSexButton addTarget:self action:@selector(selectSexButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        
        switch (i) {
            case 0:
                [selectSexButton setFrame:CGRectMake(selectSexButtonEdge, _bgImageBox.frame.origin.y+_bgImageBox.bounds.size.height+40, (kScreenWidth-selectSexButtonEdge*2-30)/2, 40)];
                break;
            case 1:
                [selectSexButton setFrame:CGRectMake(kScreenWidth/2+15, _bgImageBox.frame.origin.y+_bgImageBox.bounds.size.height+40, (kScreenWidth-selectSexButtonEdge*2-30)/2, 40)];
                break;
                
            default:
                break;
        }
    }
}

- (void)selectSexButtonClick:(UIButton *)sender
{
    NSInteger tag = sender.tag - 10000;
    switch (tag) {
        case 0:
        {
            if (_fatherImage  &&  _motherImage) {
                UIImage *finalFatherImage = [ImageManager compressImage:_fatherImage maxSize:2000];
                UIImage *finalMotherImage = [ImageManager compressImage:_motherImage maxSize:2000];
                NSLog(@"finalFatherImage.size = %@     finalMotherImage.size = %@", NSStringFromCGSize(finalFatherImage.size), NSStringFromCGSize(finalMotherImage.size));
                if ([CheckImageHaveFace checkHaveFaceWithImage:finalFatherImage]  &&  [CheckImageHaveFace checkHaveFaceWithImage:finalMotherImage]) {
                    BabyResultVC *vc = [[BabyResultVC alloc] init];
                    [self.navigationController pushViewController:vc animated:YES];
                } else {
                    [SVProgressHUD showInfoWithStatus:@"照片不包含人脸，请重新添加带人脸的照片"];
                }
            }
        }
            break;
        case 1:
        {
            if (_fatherImage  &&  _motherImage) {
                UIImage *finalFatherImage = [ImageManager compressImage:_fatherImage maxSize:2000];
                UIImage *finalMotherImage = [ImageManager compressImage:_motherImage maxSize:2000];
                NSLog(@"finalFatherImage.size = %@     finalMotherImage.size = %@", NSStringFromCGSize(finalFatherImage.size), NSStringFromCGSize(finalMotherImage.size));
                if ([CheckImageHaveFace checkHaveFaceWithImage:finalFatherImage]  &&  [CheckImageHaveFace checkHaveFaceWithImage:finalMotherImage]) {
                    BabyResultVC *vc = [[BabyResultVC alloc] init];
                    [self.navigationController pushViewController:vc animated:YES];
                } else {
                    [SVProgressHUD showInfoWithStatus:@"照片不包含人脸，请重新添加带人脸的照片"];
                }
            }
        }
            break;
            
        default:
            break;
    }
}

//  Delegate
- (void)reselectPhotoImage
{
    self.fatherImageView.image = nil;
    self.fatherImageView.alpha = 0;
    
    self.motherImageView.image = nil;
    self.motherImageView.alpha = 0;
    
    for (int i = 0; i < _sexArray.count; i++) {
        UIButton *selectSexButton = (UIButton *)[self.view viewWithTag:10000+i];
        selectSexButton.userInteractionEnabled = NO;
        selectSexButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
    }
}

- (void)addFatherButtonClick:(UIButton *)sender
{
    _isSelectFatherImage = YES;
    
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    [self presentImagePickerController];
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"未获得相机权限" message:@"开启后才能使用拍照功能" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"好的" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        [self presentImagePickerController];
    }
}

- (void)addMotherButtonClick:(UIButton *)sender
{
    _isSelectFatherImage = NO;
    
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    [self presentImagePickerController];
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"未获得相机权限" message:@"开启后才能使用拍照功能" preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"好的" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:@"取消" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        [self presentImagePickerController];
    }
}

- (void)presentImagePickerController
{
    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        MyImagePickerController *vc = [[MyImagePickerController alloc] init];
        vc.delegate = self; //delegate遵循了两个代理
        vc.allowsEditing = NO;
        vc.sourceType = UIImagePickerControllerSourceTypeCamera;
        vc.cameraDevice = UIImagePickerControllerCameraDeviceFront;
        vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
        vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
        [self presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.openAlbum = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                NSLog(@"Baby功能 - 相册选择图像尺寸: %@, 方向: %ld", NSStringFromCGSize(image.size), (long)image.imageOrientation);

                self.cutImageView.alpha = 1.0;
                self.cutImageView.image = image;
                UIImage *avatarImage = [ImageManager cutImageFromImageView:self.cutImageView];

                // 确保相册图像也经过方向修正，与拍摄图像保持一致
                UIImage *correctedImage = [image fixOrientation];

                if (self.isSelectFatherImage == YES) {
                    self.fatherImage = correctedImage;  // 使用修正后的图像
                    self.fatherImageView.alpha = 1.0;
                    self.fatherImageView.image = avatarImage;
                } else {
                    self.motherImage = correctedImage;  // 使用修正后的图像
                    self.motherImageView.alpha = 1.0;
                    self.motherImageView.image = avatarImage;
                }
                if (self.fatherImage  &&  self.motherImage) {
                    for (int i = 0; i < self.sexArray.count; i++) {
                        UIButton *selectSexButton = (UIButton *)[self.view viewWithTag:10000+i];
                        selectSexButton.userInteractionEnabled = YES;
                        selectSexButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
                    }
                }
                
                self.cutImageView.alpha= 0;
            }];
        };
    }
}


#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    UIImage *photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
    photoImage = [photoImage fixOrientation];
    //改变该图片的方向
    photoImage = [UIImage imageWithCGImage:photoImage.CGImage scale:photoImage.scale orientation:UIImageOrientationUpMirrored];
    NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
    //  截取图像
    
    MyImagePickerResultVC *vc = [[MyImagePickerResultVC alloc] init];
    vc.photoImage = photoImage;
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [picker presentViewController:vc animated:YES completion:^{}];
    __weak typeof(vc) weakVC = vc;
    vc.dismissVC = ^(UIImage * _Nonnull image) {
        [weakVC dismissViewControllerAnimated:YES completion:^{
            [picker dismissViewControllerAnimated:NO completion:^{
            }];
            
            NSLog(@"Baby功能 - 拍摄图像处理后尺寸: %@, 方向: %ld", NSStringFromCGSize(photoImage.size), (long)photoImage.imageOrientation);

            if (self.isSelectFatherImage == YES) {
                self.fatherImage = photoImage;  // 保存处理后的图像
                self.fatherImageView.alpha = 1.0;
                self.fatherImageView.image = image;
            } else {
                self.motherImage = photoImage;  // 保存处理后的图像
                self.motherImageView.alpha = 1.0;
                self.motherImageView.image = image;
            }
            
            if (self.fatherImage  &&  self.motherImage) {
                for (int i = 0; i < self.sexArray.count; i++) {
                    UIButton *selectSexButton = (UIButton *)[self.view viewWithTag:10000+i];
                    selectSexButton.userInteractionEnabled = YES;
                    selectSexButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
                }
            }
        }];
    };
}


@end
