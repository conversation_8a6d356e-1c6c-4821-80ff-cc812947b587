//
//  BabyResultVC.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "BabyResultVC.h"
#import "ImageTextButton.h"

@interface BabyResultVC ()

@property (nonatomic, strong) UILabel *labelTips;

@end

@implementation BabyResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isTopButton = YES;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    
    [self initAddButtonAndAvatar];
    self.addButton.userInteractionEnabled = NO;
    self.avatarImageView.alpha = 1.0;
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:@"可以重新选择性别和照片"];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        
        [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
            make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-72);
            make.width.equalTo(@220);
        }];
    }
    
    // 修改性别按钮
    UIButton *changeSexButton = [[UIButton alloc] init];
    changeSexButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    [changeSexButton setTitle:@"换个性别" forState:UIControlStateNormal];
    [changeSexButton.titleLabel setFont:[UIFont systemFontOfSize:14.0]];
    [changeSexButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [self.view addSubview:changeSexButton];
    [changeSexButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.view.mas_leading).with.offset(0);
        make.bottom.equalTo(self.imageBox.mas_top).with.offset(-[iPhoneXTool setCommonValue:20 orStrangeValue:25]);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    
    // 重新选择照片按钮
    UIButton *reselectImageButton = [[UIButton alloc] init];
    reselectImageButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    [reselectImageButton setTitle:@"重新选择照片" forState:UIControlStateNormal];
    [reselectImageButton.titleLabel setFont:[UIFont systemFontOfSize:14.0]];
    [reselectImageButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [self.view addSubview:reselectImageButton];
    [reselectImageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.view.mas_trailing).with.offset(0);
        make.bottom.equalTo(self.imageBox.mas_top).with.offset(-[iPhoneXTool setCommonValue:20 orStrangeValue:25]);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    
    [self.view layoutIfNeeded];
    
    if (changeSexButton) {
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:changeSexButton.bounds byRoundingCorners:UIRectCornerTopRight | UIRectCornerBottomRight cornerRadii:CGSizeMake(20, 20)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.path = maskPath.CGPath;
        changeSexButton.layer.masksToBounds = YES;
        changeSexButton.layer.mask = maskLayer;
    }
    if (reselectImageButton) {
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:reselectImageButton.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(20, 20)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.path = maskPath.CGPath;
        reselectImageButton.layer.masksToBounds = YES;
        reselectImageButton.layer.mask = maskLayer;
    }
    
    ImageTextButton *saveButton = [[ImageTextButton alloc] init];
    [saveButton setImage:[UIImage imageNamed:@"保存"] title:@"保存"];
    [self.view addSubview:saveButton];
    saveButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"saveButton");
    };
    
    ImageTextButton *shareButton = [[ImageTextButton alloc] init];
    [shareButton setImage:[UIImage imageNamed:@"分享"] title:@"分享"];
    [self.view addSubview:shareButton];
    shareButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"shareButton");
    };
    
    [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(-[iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
    
    [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset([iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
    
}


@end
