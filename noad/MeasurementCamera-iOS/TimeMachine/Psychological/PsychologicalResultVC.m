//
//  PsychologicalResultVC.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "PsychologicalResultVC.h"
#import "ImageTextButton.h"
#import "ImageManager.h"
#import "FileManager.h"
#import "TimeTool.h"

@interface PsychologicalResultVC ()

@end

@implementation PsychologicalResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self initBgImgView];
    
    [self initBackButton];
    
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = [NSString stringWithFormat:@"%@%@", _pTitle, NSLocalizedString(@"Results", nil)];
    titleLabel.textColor = [UIColor lightGrayColor];
    titleLabel.font = [UIFont systemFontOfSize:15.0];
    titleLabel.numberOfLines = 0;
    [self.view addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).with.offset(kStatusBarHeight + 50 + 20);
        make.leading.equalTo(self.view.mas_leading).with.offset(20);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-20);
    }];
    
    /*
    UILabel *resultLabel = [[UILabel alloc] init];
    resultLabel.text = @"你是一个渴望爱的人。你是一个渴望爱的人。你是一个渴望爱的人。";
    resultLabel.textColor = [UIColor whiteColor];
    resultLabel.font = [UIFont systemFontOfSize:17.0];
    resultLabel.numberOfLines = 0;
    [self.view addSubview:resultLabel];
    [resultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).with.offset(10);
        make.leading.equalTo(self.view.mas_leading).with.offset(20);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-20);
    }];
     */
    
    UIView *bgBoxView = [[UIView alloc] init];
    bgBoxView.backgroundColor = [MyColor colorWithHexString:@"#FEF7D9" alpha:1.0];
    bgBoxView.layer.cornerRadius = 10.0;
    bgBoxView.layer.masksToBounds = YES;
    bgBoxView.layer.borderWidth = 4.0;
    bgBoxView.layer.borderColor = [MyColor colorWithHexString:@"#FAEABF" alpha:1.0].CGColor;
    [self.view addSubview:bgBoxView];
    
    UILabel *contentLabel = [[UILabel alloc] init];
    contentLabel.text = _result;
    contentLabel.textColor = [MyColor colorWithHexString:@"#683F24" alpha:1.0];
    contentLabel.font = [UIFont systemFontOfSize:16.0];
    contentLabel.numberOfLines = 0;
    [bgBoxView addSubview:contentLabel];
    [contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).with.offset(60);
        make.leading.equalTo(self.view.mas_leading).with.offset(32);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-32);
    }];
    
    UILabel *tipsLabel = [[UILabel alloc] init];
    tipsLabel.text = NSLocalizedString(@"ResultReference", nil);
    tipsLabel.textColor = [MyColor colorWithHexString:@"#D8CAA3" alpha:1.0];
    tipsLabel.font = [UIFont systemFontOfSize:12.0];
    tipsLabel.numberOfLines = 0;
    [bgBoxView addSubview:tipsLabel];
    [tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(contentLabel.mas_bottom).with.offset(60);
        make.leading.equalTo(self.view.mas_leading).with.offset(32);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-32);
    }];
    
    [bgBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(contentLabel.mas_top).with.offset(-18);
        make.leading.equalTo(contentLabel.mas_leading).with.offset(-12);
        make.trailing.equalTo(contentLabel.mas_trailing).with.offset(12);
        make.bottom.equalTo(tipsLabel.mas_bottom).with.offset(18);
    }];
    
    ImageTextButton *saveButton = [[ImageTextButton alloc] init];
    [saveButton setImage:[UIImage imageNamed:@"保存"] title:NSLocalizedString(@"Save", nil)];
    [self.view addSubview:saveButton];
    saveButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"saveButton");
        
        UIImage *cutImage = [ImageManager cutImageFromView:bgBoxView];
        UIImageWriteToSavedPhotosAlbum(cutImage, self, @selector(image:didFinishSavingWithError:contextInfo:), NULL);
    };
    
    ImageTextButton *shareButton = [[ImageTextButton alloc] init];
    [shareButton setImage:[UIImage imageNamed:@"分享"] title:NSLocalizedString(@"Share", nil)];
    [self.view addSubview:shareButton];
    shareButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"shareButton");
        [SVProgressHUD show];
        
        UIImage *cutImage = [ImageManager cutImageFromView:bgBoxView];
        NSData *imageData = UIImageJPEGRepresentation(cutImage, 0.5);
        NSString *imagePath = [[FileManager pathForDocumentsFileName:ShareImage] stringByAppendingPathComponent:[NSString stringWithFormat:@"photo%@.jpg", [TimeTool getCurrentTime]]];
        [imageData writeToFile:imagePath atomically:YES];
        //NSLog(@"imagePath == %@", imagePath);
        
        // 弹出分享框并进行分享
        NSArray *items = [NSArray arrayWithObjects:[NSURL fileURLWithPath:imagePath], nil];
        UIActivityViewController *activityViewController =
        [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
        activityViewController.excludedActivityTypes = @[UIActivityTypeAirDrop];
        NSLog(@"lalala");
        // 分享之后的回调
        activityViewController.completionWithItemsHandler = ^(UIActivityType  _Nullable activityType, BOOL completed, NSArray * _Nullable returnedItems, NSError * _Nullable activityError) {
            if (completed) {
                NSLog(@"completed");
            } else if (activityError) {
                NSLog(@"activityError == %@", activityError.localizedDescription);
            }
        };
        if (self.isIPad) {
            activityViewController.popoverPresentationController.sourceView = self.view;
            activityViewController.popoverPresentationController.sourceRect = CGRectMake(self.view.frame.size.width/2.0, self.view.frame.size.height/2.0, 1, 1);
            activityViewController.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionUp;
        }
        [self presentViewController:activityViewController animated:YES completion:^{
            [SVProgressHUD dismiss];
        }];
    };
    
    [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).with.offset(-(kBottomSafeHeight+36));
        make.centerX.equalTo(self.view.mas_centerX).with.offset(-65);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
    
    [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_bottom).with.offset(-(kBottomSafeHeight+36));
        make.centerX.equalTo(self.view.mas_centerX).with.offset(65);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    if(error != NULL) {
        [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"PictureSaveFailedTips", nil)];
    } else {
        [SVProgressHUD showSuccessWithStatus:NSLocalizedString(@"PictureSaveSucceedTips", nil)];
    }
}


@end
