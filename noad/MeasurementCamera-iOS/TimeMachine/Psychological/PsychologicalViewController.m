//
//  PsychologicalViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "PsychologicalViewController.h"
#import "PsychologicalResultVC.h"
#import "DatabaseManager.h"
#import "PalmsViewController.h"
#import "PopView.h"
@interface PsychologicalViewController ()<MyAdManagerDelegate>

@property (nonatomic, strong) UIImageView *bgImageBox;
@property (nonatomic, strong) UIButton *nextButton;
@property (nonatomic, strong) UILabel *subjectNumLabel;
@property (nonatomic, strong) UILabel *subjectTitleLabel;
@property (nonatomic, strong) UIView *bgSubjectAnswerButton1;
@property (nonatomic, strong) UILabel *subjectAnswerLabel1;
@property (nonatomic, strong) UIView *bgSubjectAnswerButton2;
@property (nonatomic, strong) UILabel *subjectAnswerLabel2;
@property (nonatomic, strong) UIView *bgSubjectAnswerButton3;
@property (nonatomic, strong) UILabel *subjectAnswerLabel3;
@property (nonatomic, strong) UIView *bgSubjectAnswerButton4;
@property (nonatomic, strong) UILabel *subjectAnswerLabel4;

@property (nonatomic, strong) NSArray *psychologicalSubjectList;

@property (nonatomic) NSInteger currentSubjectIndex;
@property (nonatomic) NSInteger currentSubjectAnswer;

@end

#define UnsSubjectAnswer 88

@implementation PsychologicalViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.currentSubjectIndex = 0;
    self.currentSubjectAnswer = UnsSubjectAnswer;
    
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    self.psychologicalSubjectList = [databaseManager obtainPsychologicalSubjectTableWithPsychological_id:_pDict[@"psychological_id"]];
    
    //NSLog(@"psychologicalSubject == %@", self.psychologicalSubjectList);
    if(self.isHandPrint)
    {
        
        
        
        
        NSString *filePath = [[NSBundle mainBundle] pathForResource:@"手相" ofType:@"json"];
        NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
        if (![currentLanguageRegion containsString:@"zh"])
        {
            filePath = [[NSBundle mainBundle] pathForResource:@"手相_en" ofType:@"json"];
        }
        
        // 读取文件内容
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        
        
        
        // 解析 JSON 数据
        NSError *error;
        NSArray *jsonDict = [NSJSONSerialization JSONObjectWithData:data options:kNilOptions error:&error];
        self.psychologicalSubjectList = jsonDict;
        if(!self.handPrintResultArray)
        {
            
            NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
            
            
            NSArray* arr = @[@{@"感情":[NSMutableArray array]},
                             @{@"生命":[NSMutableArray array]},
                             @{@"事业":[NSMutableArray array]},
                             @{@"性格":[NSMutableArray array]}];
            
            if (![currentLanguageRegion containsString:@"zh"])
            {
                arr = @[@{@"Emotion":[NSMutableArray array]},
                                 @{@"Life":[NSMutableArray array]},
                                 @{@"Career":[NSMutableArray array]},
                                 @{@"Character":[NSMutableArray array]}];
            }
            
            
            self.handPrintResultArray = [NSMutableArray arrayWithArray:arr];
            
            
        }
    }
    [self initBgImgView];
    [self initBackButton];
    //NSLog(@"psychologicalSubject == %@", self.psychologicalSubjectList);
    if(self.isHandPrint)
    {
        
        
        
        UIButton *leftButton = [UIButton new];
        [leftButton setImage:[UIImage imageNamed:@"提示"] forState:0];
        NSString *filePath = [[NSBundle mainBundle] pathForResource:@"手相" ofType:@"json"];
        NSString *currentLanguageRegion = [[[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"] firstObject];
        if (![currentLanguageRegion containsString:@"zh"])
        {
            [leftButton setImage:[UIImage imageNamed:@"提示_en"] forState:0];
        }
        // Set the button's frame or constraints as needed
        leftButton.frame = CGRectMake(SCREEN_WIDTH-50-5, kStatusBarHeight,35, 35);  // Example frame
        
        [self.view addSubview:leftButton];
        [leftButton bk_whenTapped:^{
            UIImageView* im = [UIImageView createSizeFitImageviewName:@"手相"];
            im.backgroundColor = [UIColor whiteColor];
            im.frame = CGRectMake(0, 0, 305*scaleX, 396*scaleX);
            im.layer.cornerRadius = 10*scaleX;
            im.layer.masksToBounds = YES;
            PopView* pop = [PopView popSideContentView:im direct:PopViewDirection_SlideInCenter];
            
        }];
    }
    
    // 标题
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = _pDict[@"title"];
    titleLabel.textColor = [UIColor whiteColor];
    titleLabel.font = [UIFont systemFontOfSize:16.0];
    titleLabel.numberOfLines = 0;
    [self.view addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_top).with.offset(kStatusBarHeight + 50 + 10);
        make.leading.equalTo(self.view.mas_leading).with.offset(40);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-40);
    }];
    if(self.isHandPrint)
    {
        titleLabel.text =local( @"在手相里左“先天”、右“后天”，右手的影响比重较高，左手影响力较小，所以判断手相时，主要用右手来判断，再依据左手做吉凶上加减分。并不是男左女右。");
    }
    
    UILabel *descLabel = [[UILabel alloc] init];
    descLabel.text = _pDict[@"desc"];
    descLabel.textColor = [UIColor whiteColor];
    descLabel.font = [UIFont systemFontOfSize:13.0];
    descLabel.numberOfLines = 0;
    [self.view addSubview:descLabel];
    [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(titleLabel.mas_bottom).with.offset(10);
        make.leading.equalTo(self.view.mas_leading).with.offset(40);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-40);
    }];
    
    if (self.bgImageBox == nil) {
        self.bgImageBox = [[UIImageView alloc] init];
        [self.bgImageBox setImage:[UIImage imageNamed:@"无内容背景框"]];
        [self.bgImageBox setContentMode:UIViewContentModeScaleAspectFit];
        [self.bgImageBox setUserInteractionEnabled:YES];
        [self.view addSubview:self.bgImageBox];
        if (self.isIPad == YES) {
            [_bgImageBox mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(descLabel.mas_bottom).with.offset(30);
                make.centerX.equalTo(self.view.mas_centerX).with.offset(0);
                make.width.equalTo(@(kScreenWidth*0.6));
                make.height.equalTo(@((kScreenWidth*0.6)*1.66));
            }];
        } else {
            CGFloat edge = [iPhoneXTool setCommonValue:45 orStrangeValue:40];
            [_bgImageBox mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(descLabel.mas_bottom).with.offset(30);
                make.leading.equalTo(self.view.mas_leading).with.offset(edge);
                make.trailing.equalTo(self.view.mas_trailing).with.offset(-edge);
                make.height.equalTo(@((kScreenWidth-edge*2)*1.66));
            }];
        }
        [self showBannerView:CGRectMake(0, self.view.bounds.size.height-90, SCREEN_WIDTH, 90) size:CGSizeMake(SCREEN_WIDTH, 90)];
        
    }
    
    if (self.psychologicalSubjectList.count > 0) {
        if (self.nextButton == nil) {
            self.nextButton = [[UIButton alloc] init];
            [self.nextButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
            [self.nextButton.titleLabel setFont:[UIFont systemFontOfSize:14.0]];
            self.nextButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
            self.nextButton.layer.cornerRadius = 18;
            [self.bgImageBox addSubview:self.nextButton];
            [self.nextButton addTarget:self action:@selector(nextButtonClick:) forControlEvents:UIControlEventTouchUpInside];
            if (self.isIPad == YES) {
                [_nextButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.bottom.equalTo(_bgImageBox.mas_bottom).with.offset(-((kScreenWidth*0.6)*1.66)*0.12);
                    make.centerX.equalTo(_bgImageBox.mas_centerX).with.offset(0);
                    make.width.equalTo(@150);
                    make.height.equalTo(@36);
                }];
            } else {
                [_nextButton mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.bottom.equalTo(_bgImageBox.mas_bottom).with.offset(-66);
                    make.centerX.equalTo(_bgImageBox.mas_centerX).with.offset(0);
                    make.width.equalTo(@150);
                    make.height.equalTo(@36);
                }];
            }
            
            //NSLog(@"self.psychologicalSubjectList == %@", self.psychologicalSubjectList);
            if (self.psychologicalSubjectList.count == 1) {
                [self.nextButton setTitle:NSLocalizedString(@"ViewResults", nil) forState:UIControlStateNormal];
            } else if (self.psychologicalSubjectList.count > 1) {
                [self.nextButton setTitle:NSLocalizedString(@"NextSubject", nil) forState:UIControlStateNormal];
            }
        }
        
        /*
        if (self.subjectNumLabel == nil) {
            self.subjectNumLabel = [[UILabel alloc] init];
            self.subjectNumLabel.text = @"8/18";
            self.subjectNumLabel.textColor = [UIColor whiteColor];
            self.subjectNumLabel.font = [UIFont systemFontOfSize:11.0];
            [self.bgImageBox addSubview:self.subjectNumLabel];
            [_subjectNumLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(_bgImageBox.mas_top).with.offset(72);
                make.leading.equalTo(_bgImageBox.mas_leading).with.offset(60);
                make.trailing.equalTo(_bgImageBox.mas_trailing).with.offset(-60);
            }];
        }
         */
        
        if (self.subjectTitleLabel == nil) {
            self.subjectTitleLabel = [[UILabel alloc] init];
            self.subjectTitleLabel.text = _psychologicalSubjectList[self.currentSubjectIndex][@"subject_title"];
            self.subjectTitleLabel.textColor = [UIColor whiteColor];
            self.subjectTitleLabel.font = [UIFont systemFontOfSize:14.0];
            self.subjectTitleLabel.numberOfLines = 0;
            [self.bgImageBox addSubview:self.subjectTitleLabel];
            if (self.isIPad == YES) {
                [_subjectTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_bgImageBox.mas_top).with.offset(kScreenWidth*0.6*1.66*0.15);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(kScreenWidth*0.6*0.18);
                    make.trailing.equalTo(_bgImageBox.mas_trailing).with.offset(-kScreenWidth*0.6*0.18);
                }];
            } else {
                [_subjectTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_bgImageBox.mas_top).with.offset(80);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(60);
                    make.trailing.equalTo(_bgImageBox.mas_trailing).with.offset(-60);
                }];
            }
        }
        
        if (self.bgSubjectAnswerButton1 == nil) {
            self.bgSubjectAnswerButton1 = [[UIView alloc] init];
            //self.bgSubjectAnswerButton1.backgroundColor = [MyColor colorWithHexString:@"#333333" alpha:1.0];
            self.bgSubjectAnswerButton1.layer.cornerRadius = 5.0;
            [self.bgImageBox addSubview:self.bgSubjectAnswerButton1];
            self.bgSubjectAnswerButton1.userInteractionEnabled = YES;
            
            UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(subjectAnswerButton1:)];
            [self.bgSubjectAnswerButton1 addGestureRecognizer:tapGestureRecognizer];
        }
        
        if (self.subjectAnswerLabel1 == nil) {
            self.subjectAnswerLabel1 = [[UILabel alloc] init];
            self.subjectAnswerLabel1.textColor = [UIColor whiteColor];
            self.subjectAnswerLabel1.font = [UIFont systemFontOfSize:14.0];
            self.subjectAnswerLabel1.numberOfLines = 0;
            [self.bgImageBox addSubview:self.subjectAnswerLabel1];
            if (self.isIPad == YES) {
                [_subjectAnswerLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_subjectTitleLabel.mas_bottom).with.offset(30);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(kScreenWidth*0.6*0.18);
                    make.width.lessThanOrEqualTo(@(kScreenWidth*0.6*0.64));
                }];
            } else {
                [_subjectAnswerLabel1 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_subjectTitleLabel.mas_bottom).with.offset(30);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(65);
                    make.width.lessThanOrEqualTo(@(kScreenWidth-80-130));
                }];
            }
            NSString *answer = [NSString stringWithFormat:@"A. %@", _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer1"]];
            self.subjectAnswerLabel1.text = answer;
        }
        
        [_bgSubjectAnswerButton1 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(_subjectAnswerLabel1.mas_leading).with.offset(-5);
            make.trailing.equalTo(_subjectAnswerLabel1.mas_trailing).with.offset(5);
            make.centerY.equalTo(_subjectAnswerLabel1.mas_centerY).with.offset(0);
            make.top.equalTo(_subjectAnswerLabel1.mas_top).with.offset(-5);
            make.bottom.equalTo(_subjectAnswerLabel1.mas_bottom).offset(5);
        }];
        
        if (self.bgSubjectAnswerButton2 == nil) {
            self.bgSubjectAnswerButton2 = [[UIView alloc] init];
            self.bgSubjectAnswerButton2.layer.cornerRadius = 5.0;
            [self.bgImageBox addSubview:self.bgSubjectAnswerButton2];
            self.bgSubjectAnswerButton2.userInteractionEnabled = YES;
            
            UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(subjectAnswerButton2:)];
            [self.bgSubjectAnswerButton2 addGestureRecognizer:tapGestureRecognizer];
        }
        
        if (self.subjectAnswerLabel2 == nil) {
            self.subjectAnswerLabel2 = [[UILabel alloc] init];
            self.subjectAnswerLabel2.textColor = [UIColor whiteColor];
            self.subjectAnswerLabel2.font = [UIFont systemFontOfSize:14.0];
            self.subjectAnswerLabel2.numberOfLines = 0;
            [self.bgImageBox addSubview:self.subjectAnswerLabel2];
            if (self.isIPad == YES) {
                [_subjectAnswerLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_subjectAnswerLabel1.mas_bottom).with.offset(26);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(kScreenWidth*0.6*0.18);
                    make.width.lessThanOrEqualTo(@(kScreenWidth*0.6*0.64));
                }];
            } else {
                [_subjectAnswerLabel2 mas_makeConstraints:^(MASConstraintMaker *make) {
                    make.top.equalTo(_subjectAnswerLabel1.mas_bottom).with.offset(26);
                    make.leading.equalTo(_bgImageBox.mas_leading).with.offset(65);
                    make.width.lessThanOrEqualTo(@(kScreenWidth-80-130));
                }];
            }
            NSString *answer = [NSString stringWithFormat:@"B. %@", _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer2"]];
            self.subjectAnswerLabel2.text = answer;
        }
        
        [_bgSubjectAnswerButton2 mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(_subjectAnswerLabel2.mas_leading).with.offset(-5);
            make.trailing.equalTo(_subjectAnswerLabel2.mas_trailing).with.offset(5);
            make.centerY.equalTo(_subjectAnswerLabel2.mas_centerY).with.offset(0);
            make.top.equalTo(_subjectAnswerLabel2.mas_top).with.offset(-5);
            make.bottom.equalTo(_subjectAnswerLabel2.mas_bottom).offset(5);
        }];
        
        NSString *subject_answer3 = _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer3"];
        if (![subject_answer3 isEqualToString:@""]  &&  subject_answer3.length > 0) {
            if (self.bgSubjectAnswerButton3 == nil) {
                self.bgSubjectAnswerButton3 = [[UIView alloc] init];
                self.bgSubjectAnswerButton3.layer.cornerRadius = 5.0;
                [self.bgImageBox addSubview:self.bgSubjectAnswerButton3];
                self.bgSubjectAnswerButton3.userInteractionEnabled = YES;
                
                UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(subjectAnswerButton3:)];
                [self.bgSubjectAnswerButton3 addGestureRecognizer:tapGestureRecognizer];
            }
            
            if (self.subjectAnswerLabel3 == nil) {
                self.subjectAnswerLabel3 = [[UILabel alloc] init];
                self.subjectAnswerLabel3.textColor = [UIColor whiteColor];
                self.subjectAnswerLabel3.font = [UIFont systemFontOfSize:14.0];
                self.subjectAnswerLabel3.numberOfLines = 0;
                [self.bgImageBox addSubview:self.subjectAnswerLabel3];
                if (self.isIPad == YES) {
                    [_subjectAnswerLabel3 mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.top.equalTo(_subjectAnswerLabel2.mas_bottom).with.offset(26);
                        make.leading.equalTo(_bgImageBox.mas_leading).with.offset(kScreenWidth*0.6*0.18);
                        make.width.lessThanOrEqualTo(@(kScreenWidth*0.6*0.64));
                    }];
                } else {
                    [_subjectAnswerLabel3 mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.top.equalTo(_subjectAnswerLabel2.mas_bottom).with.offset(26);
                        make.leading.equalTo(_bgImageBox.mas_leading).with.offset(65);
                        make.width.lessThanOrEqualTo(@(kScreenWidth-80-130));
                    }];
                }
                NSString *answer = [NSString stringWithFormat:@"C. %@", _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer3"]];
                self.subjectAnswerLabel3.text = answer;
            }
            
            [_bgSubjectAnswerButton3 mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(_subjectAnswerLabel3.mas_leading).with.offset(-5);
                make.trailing.equalTo(_subjectAnswerLabel3.mas_trailing).with.offset(5);
                make.centerY.equalTo(_subjectAnswerLabel3.mas_centerY).with.offset(0);
                make.top.equalTo(_subjectAnswerLabel3.mas_top).with.offset(-5);
                make.bottom.equalTo(_subjectAnswerLabel3.mas_bottom).offset(5);
            }];
        }
        
        NSString *subject_answer4 = _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer4"];
        if (![subject_answer4 isEqualToString:@""]  &&  subject_answer3.length > 0) {
            if (self.bgSubjectAnswerButton4 == nil) {
                self.bgSubjectAnswerButton4 = [[UIView alloc] init];
                self.bgSubjectAnswerButton4.layer.cornerRadius = 5.0;
                [self.bgImageBox addSubview:self.bgSubjectAnswerButton4];
                self.bgSubjectAnswerButton4.userInteractionEnabled = YES;
                
                UITapGestureRecognizer *tapGestureRecognizer = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(subjectAnswerButton4:)];
                [self.bgSubjectAnswerButton4 addGestureRecognizer:tapGestureRecognizer];
            }
            
            if (self.subjectAnswerLabel4 == nil) {
                self.subjectAnswerLabel4 = [[UILabel alloc] init];
                self.subjectAnswerLabel4.textColor = [UIColor whiteColor];
                self.subjectAnswerLabel4.font = [UIFont systemFontOfSize:14.0];
                self.subjectAnswerLabel4.numberOfLines = 0;
                [self.bgImageBox addSubview:self.subjectAnswerLabel4];
                if (self.isIPad == YES) {
                    [_subjectAnswerLabel4 mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.top.equalTo(_subjectAnswerLabel3.mas_bottom).with.offset(26);
                        make.leading.equalTo(_bgImageBox.mas_leading).with.offset(kScreenWidth*0.6*0.18);
                        make.width.lessThanOrEqualTo(@(kScreenWidth*0.6*0.64));
                    }];
                } else {
                    [_subjectAnswerLabel4 mas_makeConstraints:^(MASConstraintMaker *make) {
                        make.top.equalTo(_subjectAnswerLabel3.mas_bottom).with.offset(26);
                        make.leading.equalTo(_bgImageBox.mas_leading).with.offset(65);
                        make.width.lessThanOrEqualTo(@(kScreenWidth-80-130));
                    }];
                }
                NSString *answer = [NSString stringWithFormat:@"D. %@", _psychologicalSubjectList[self.currentSubjectIndex][@"subject_answer4"]];
                self.subjectAnswerLabel4.text = answer;
            }
            
            [_bgSubjectAnswerButton4 mas_makeConstraints:^(MASConstraintMaker *make) {
                make.leading.equalTo(_subjectAnswerLabel4.mas_leading).with.offset(-5);
                make.trailing.equalTo(_subjectAnswerLabel4.mas_trailing).with.offset(5);
                make.centerY.equalTo(_subjectAnswerLabel4.mas_centerY).with.offset(0);
                make.top.equalTo(_subjectAnswerLabel4.mas_top).with.offset(-5);
                make.bottom.equalTo(_subjectAnswerLabel4.mas_bottom).offset(5);
            }];
        }
    }
}

- (void)subjectAnswerButton1:(UIButton *)sender
{
    _currentSubjectAnswer = 1;
    
    self.bgSubjectAnswerButton1.backgroundColor = [MyColor colorWithHexString:@"#333333" alpha:1.0];
    self.bgSubjectAnswerButton2.backgroundColor = [UIColor clearColor];
    
    if (self.bgSubjectAnswerButton3) {
        self.bgSubjectAnswerButton3.backgroundColor = [UIColor clearColor];
    }
    if (self.bgSubjectAnswerButton4) {
        self.bgSubjectAnswerButton4.backgroundColor = [UIColor clearColor];
    }
}

- (void)subjectAnswerButton2:(UIButton *)sender
{
    _currentSubjectAnswer = 2;
    
    self.bgSubjectAnswerButton1.backgroundColor = [UIColor clearColor];
    self.bgSubjectAnswerButton2.backgroundColor = [MyColor colorWithHexString:@"#333333" alpha:1.0];
    
    if (self.bgSubjectAnswerButton3) {
        self.bgSubjectAnswerButton3.backgroundColor = [UIColor clearColor];
    }
    if (self.bgSubjectAnswerButton4) {
        self.bgSubjectAnswerButton4.backgroundColor = [UIColor clearColor];
    }
}

- (void)subjectAnswerButton3:(UIButton *)sender
{
    _currentSubjectAnswer = 3;
    
    self.bgSubjectAnswerButton1.backgroundColor = [UIColor clearColor];
    self.bgSubjectAnswerButton2.backgroundColor = [UIColor clearColor];
    
    if (self.bgSubjectAnswerButton3) {
        self.bgSubjectAnswerButton3.backgroundColor = [MyColor colorWithHexString:@"#333333" alpha:1.0];
    }
    if (self.bgSubjectAnswerButton4) {
        self.bgSubjectAnswerButton4.backgroundColor = [UIColor clearColor];
    }
}

- (void)subjectAnswerButton4:(UIButton *)sender
{
    _currentSubjectAnswer = 4;
    
    self.bgSubjectAnswerButton1.backgroundColor = [UIColor clearColor];
    self.bgSubjectAnswerButton2.backgroundColor = [UIColor clearColor];
    
    if (self.bgSubjectAnswerButton3) {
        self.bgSubjectAnswerButton3.backgroundColor = [UIColor clearColor];
    }
    if (self.bgSubjectAnswerButton4) {
        self.bgSubjectAnswerButton4.backgroundColor = [MyColor colorWithHexString:@"#333333" alpha:1.0];
    }
}

- (void)nextButtonClick:(UIButton *)sender
{
    if (_currentSubjectAnswer == UnsSubjectAnswer) {
        [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PsychologicalTestTips1", nil)];
        return;
    }
    if (_psychologicalSubjectList.count == 1) {
        NSDictionary *sDict = _psychologicalSubjectList[_currentSubjectIndex];
        NSString *result_type = sDict[@"result_type"];
        if (![result_type isEqualToString:@""]  &&  result_type.length > 0) {
            NSInteger resultType = 0;
            if ([result_type containsString:@"/"]) {
                NSArray *rArray = [result_type componentsSeparatedByString:@"/"];
                resultType = [rArray[_currentSubjectAnswer-1] integerValue];
            }
            
            NSString *resultStr = _pDict[@"result"];
            NSString *result = @"";
            if ([resultStr containsString:@"/"]) {
                NSArray *rArray = [resultStr componentsSeparatedByString:@"/"];
                if (resultType <= rArray.count) {
                    result = rArray[resultType-1];
                }
            }
            PsychologicalResultVC *vc = [[PsychologicalResultVC alloc] init];
            vc.pTitle = _pDict[@"title"];
            vc.result = result;
            [self.navigationController pushViewController:vc animated:YES];
        }
    } else {
        NSDictionary *sDict = _psychologicalSubjectList[_currentSubjectIndex];
        NSString *result_type = sDict[@"result_type"];
        if (![result_type isEqualToString:@""]  &&  result_type.length > 0) {
            
            NSInteger toIndex = 0;
            if (_currentSubjectAnswer == 1) {
                NSString *toIndexStr = sDict[@"answer1_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 2) {
                NSString *toIndexStr = sDict[@"answer2_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 3) {
                NSString *toIndexStr = sDict[@"answer3_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 4) {
                NSString *toIndexStr = sDict[@"answer4_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            }
            if (toIndex > 0) {
                if (toIndex > 1000) {
                    NSInteger resultType = toIndex - 1000;
                    //NSLog(@"resultType == %ld", resultType);
                    NSString *resultStr = _pDict[@"result"];
                    NSString *result = @"";
                    if ([resultStr containsString:@"/"]) {
                        NSArray *rArray = [resultStr componentsSeparatedByString:@"/"];
                        result = rArray[resultType-1];
                    }
                    PsychologicalResultVC *vc = [[PsychologicalResultVC alloc] init];
                    vc.pTitle = _pDict[@"title"];
                    vc.result = result;
                    [self.navigationController pushViewController:vc animated:YES];
                } else {
                    _currentSubjectIndex = toIndex - 1;
                    
                    _subjectTitleLabel.text = _psychologicalSubjectList[_currentSubjectIndex][@"subject_title"];
                    
                    NSString *answer1 = [NSString stringWithFormat:@"A. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer1"]];
                    _subjectAnswerLabel1.text = answer1;
                    NSString *answer2 = [NSString stringWithFormat:@"B. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer2"]];
                    _subjectAnswerLabel2.text = answer2;
                    if (_subjectAnswerLabel3) {
                        NSString *answer3 = [NSString stringWithFormat:@"C. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer3"]];
                        _subjectAnswerLabel3.text = answer3;
                    }
                    if (_subjectAnswerLabel4) {
                        NSString *answer4 = [NSString stringWithFormat:@"D. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer4"]];
                        _subjectAnswerLabel4.text = answer4;
                    }
                    
                    
                    NSString *result_type =  _psychologicalSubjectList[_currentSubjectIndex][@"result_type"];
                    if (![result_type isEqualToString:@""]  &&  result_type.length > 0) {
                        [_nextButton setTitle:NSLocalizedString(@"ViewResults", nil) forState:UIControlStateNormal];
                    }
                    
                    self.bgSubjectAnswerButton1.backgroundColor = [UIColor clearColor];
                    self.bgSubjectAnswerButton2.backgroundColor = [UIColor clearColor];
                    
                    if (self.bgSubjectAnswerButton3) {
                        self.bgSubjectAnswerButton3.backgroundColor = [UIColor clearColor];
                    }
                    if (self.bgSubjectAnswerButton4) {
                        self.bgSubjectAnswerButton4.backgroundColor = [UIColor clearColor];
                    }
                    
                    _currentSubjectAnswer = UnsSubjectAnswer;
                }
            } else {
                NSInteger resultType = 0;
                if ([result_type containsString:@"/"]) {
                    NSArray *rArray = [result_type componentsSeparatedByString:@"/"];
                    resultType = [rArray[_currentSubjectAnswer-1] integerValue];
                }
                //NSLog(@"resultType == %ld", resultType);
                NSString *resultStr = _pDict[@"result"];
                NSString *result = @"";
                if ([resultStr containsString:@"/"]) {
                    NSArray *rArray = [resultStr componentsSeparatedByString:@"/"];
                    result = rArray[resultType-1];
                }
                PsychologicalResultVC *vc = [[PsychologicalResultVC alloc] init];
                vc.pTitle = _pDict[@"title"];
                vc.result = result;
                [self.navigationController pushViewController:vc animated:YES];
            }
        } else {
            NSInteger toIndex = 0;
            if (_currentSubjectAnswer == 1) {
                NSString *toIndexStr = sDict[@"answer1_toindex"];
                if(![toIndexStr isKindOfClass:[NSString class]])
                {
                    toIndexStr = [NSString stringWithFormat:@"%@",sDict[@"answer1_toindex"]];
                }
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 2) {
                NSString *toIndexStr = sDict[@"answer2_toindex"];
                if(![toIndexStr isKindOfClass:[NSString class]])
                {
                    toIndexStr = [NSString stringWithFormat:@"%@",sDict[@"answer2_toindex"]];
                }
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 3) {
                NSString *toIndexStr = sDict[@"answer3_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            } else if (_currentSubjectAnswer == 4) {
                NSString *toIndexStr = sDict[@"answer4_toindex"];
                if (![toIndexStr isEqualToString:@""]  &&  toIndexStr.length > 0) {
                    toIndex = [toIndexStr integerValue];
                }
            }
            if(_currentSubjectAnswer == 1)
            {
                // 获取 type 和 analyze 数据
                NSString *type = sDict[@"type"];
                NSArray *analyze = sDict[@"analyze"];

                // 遍历 handPrintResultArray，找到 key 为 type 的字典，并将 analyze 数据添加到对应的数组中
                for (NSMutableDictionary *dict in self.handPrintResultArray) {
                    if (dict[type]) {
                        NSMutableArray *array = dict[type];
                        for (NSString *item in analyze) {
                            if (![array containsObject:item]) {
                                [array addObject:item];
                            }
                        }
                        break; // 找到后可以退出循环，假设 type 是唯一的
                    }
                }

            }
            if(_currentSubjectIndex>=_psychologicalSubjectList.count-1)
            {
                PalmsViewController *vc = [[PalmsViewController alloc] init];
                vc.handprintResult = self.handPrintResultArray;
                [self.navigationController pushViewController:vc animated:YES];
                
                return;
            }
            
            _currentSubjectIndex = toIndex - 1;
            
            
            
            _subjectTitleLabel.text = _psychologicalSubjectList[_currentSubjectIndex][@"subject_title"];
            
            NSString *answer1 = [NSString stringWithFormat:@"A. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer1"]];
            _subjectAnswerLabel1.text = answer1;
            NSString *answer2 = [NSString stringWithFormat:@"B. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer2"]];
            _subjectAnswerLabel2.text = answer2;
            if (_subjectAnswerLabel3) {
                NSString *answer3 = [NSString stringWithFormat:@"C. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer3"]];
                _subjectAnswerLabel3.text = answer3;
            }
            if (_subjectAnswerLabel4) {
                NSString *answer4 = [NSString stringWithFormat:@"D. %@", _psychologicalSubjectList[_currentSubjectIndex][@"subject_answer4"]];
                _subjectAnswerLabel4.text = answer4;
            }
            
            
            NSString *result_type =  _psychologicalSubjectList[_currentSubjectIndex][@"result_type"];
            if (![result_type isEqualToString:@""]  &&  result_type.length > 0) {
                [_nextButton setTitle:NSLocalizedString(@"ViewResults", nil) forState:UIControlStateNormal];
            }
            
            self.bgSubjectAnswerButton1.backgroundColor = [UIColor clearColor];
            self.bgSubjectAnswerButton2.backgroundColor = [UIColor clearColor];
            
            if (self.bgSubjectAnswerButton3) {
                self.bgSubjectAnswerButton3.backgroundColor = [UIColor clearColor];
            }
            if (self.bgSubjectAnswerButton4) {
                self.bgSubjectAnswerButton4.backgroundColor = [UIColor clearColor];
            }
            
            _currentSubjectAnswer = UnsSubjectAnswer;
        }
    }
}


@end
