//
//  PrefixHeader.pch
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#ifndef PrefixHeader_pch
#define PrefixHeader_pch

#import <UIKit/UIKit.h>
#import <AVFoundation/AVFoundation.h>
#import <AssetsLibrary/AssetsLibrary.h>
#import <Photos/Photos.h>

/**公用宏*/
#define kScreenWidth [UIScreen mainScreen].bounds.size.width
#define kScreenHeight [UIScreen mainScreen].bounds.size.height
//#define kStatusBarHeight [[UIApplication sharedApplication] statusBarFrame].size.height

/** 弱引用 */
#define WEAKSELF __weak typeof(self) weakSelf = self

#define kIs_iphone (UI_USER_INTERFACE_IDIOM() == UIUserInterfaceIdiomPhone)
#define kIs_iPhoneX kScreenWidth >=375.0f && kScreenHeight >=812.0f && kIs_iphone
/*状态栏高度*/
#define kStatusBarHeight (CGFloat)(kIs_iPhoneX?(44.0):(20.0))
/*导航栏高度*/
#define kNavBarHeight (44)
/*状态栏和导航栏总高度*/
#define kNavBarAndStatusBarHeight (CGFloat)(kIs_iPhoneX?(88.0):(64.0))
/*TabBar高度*/
#define kTabBarHeight (CGFloat)(kIs_iPhoneX?(49.0 + 34.0):(49.0))
/*顶部安全区域远离高度*/
#define kTopBarSafeHeight (CGFloat)(kIs_iPhoneX?(44.0):(0))
/*底部安全区域远离高度*/
#define kBottomSafeHeight (CGFloat)(kIs_iPhoneX?(34.0):(0))
/*iPhoneX的状态栏高度差值*/
#define kTopBarDifHeight (CGFloat)(kIs_iPhoneX?(24.0):(0))
/*导航条和Tabbar总高度*/
#define kNavAndTabHeight (kNavBarAndStatusBarHeight + kTabBarHeight)

// 分享图片前保存图片的文件夹名称
#define ShareImage @"ShareImage"
// 魔法变脸 模版图片文件夹
#define MagicTemplate @"MagicTemplate"

#define DATABASE @"DATABASE"
#define DATABASEMagic @"DATABASEMagic"

#define AudioEditExpiresDate @"AudioEditExpiresDate"

/**layout*/
#import "Masonry.h"
#import "SVProgressHUD.h"
#import "AFNetworking.h"
#import "SDWebImage.h"

#import "MyColor.h"
#import "iPhoneXTool.h"

#import <BUAdSDK/BUAdSDK.h>
#import "UpdateManager.h"
#import "CTDConfig.h"
#import "UIButton+Create.h"
#import "UIImageView+Create.h"
#import "UIButton+Create.h"
#import "UIColor+Hex.h"
#import "UILabel+createLabels.h"
#import "UIView+Extension.h"
#import "MyAdManager.h"
#import "BaseViewController.h"
#import "AppDelegate.h"
#import "APPMakeStoreIAPManager.h"

#import "UIView+BSAnimation.h"
#import "BSUserDefaultManager.h"
#import <BlocksKit/UIView+BlocksKit.h>
#import <UMCommon/UMCommon.h>
#import <UMAPM/UMAPMConfig.h>
#import <UMAPM/UMCrashConfigure.h>
#define VipExpiresDate @"VipExpiresDate"
#define BKgetColorFrom(r,g,b,a) [UIColor colorWithRed:r/255.0 green:g/255.0 blue:b/255.0 alpha:a]
#define kNavgationBarHeight 44.f
#define local(x)   NSLocalizedString(x, @"description for this key.")

#define tabbarSelectColor [MyColor colorWithHexString:@"#F9DB92" alpha:1.0]
#define tabbarUnSelectColor [MyColor colorWithHexString:@"#FFFFFF" alpha:1.0]

#endif /* PrefixHeader_pch */
