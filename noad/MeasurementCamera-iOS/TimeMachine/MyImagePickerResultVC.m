//
//  MyImagePickerResultVC.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "MyImagePickerResultVC.h"
#import "MyImagePickerMaskView.h"
#import "ImageManager.h"

@interface MyImagePickerResultVC ()

@property (nonatomic, strong) UIImage *cutImage;

@end

@implementation MyImagePickerResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.view.backgroundColor = [UIColor blackColor];
    
    UIView *topView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kStatusBarHeight+60)];
    topView.backgroundColor = [UIColor blackColor];
    [self.view addSubview:topView];
    
    UIButton *backButton = [[UIButton alloc] initWithFrame:CGRectMake(5, kStatusBarHeight+5, 50, 50)];
    [backButton setImage:[UIImage imageNamed:@"返回"] forState:UIControlStateNormal];
    [backButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [topView addSubview:backButton];
    
    UIImageView *photoImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, kStatusBarHeight+60, kScreenWidth, kScreenHeight-kStatusBarHeight-60-kBottomSafeHeight-120)];
    photoImageView.backgroundColor = [UIColor blackColor];
    photoImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.view addSubview:photoImageView];
    photoImageView.image = _photoImage;
    
    MyImagePickerMaskView *maskView = [[MyImagePickerMaskView alloc] initWithFrame:photoImageView.bounds];
    [photoImageView addSubview:maskView];
    
    UIView *bottomView = [[UIView alloc] initWithFrame:CGRectMake(0, kScreenHeight-kBottomSafeHeight-120, kScreenWidth, kBottomSafeHeight+120)];
    bottomView.backgroundColor = [UIColor blackColor];
    [self.view addSubview:bottomView];
    
    UIButton *remakeButton = [[UIButton alloc] init];
    [remakeButton setTitle:NSLocalizedString(@"Remake", nil) forState:UIControlStateNormal];
    [remakeButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [remakeButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
    remakeButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    remakeButton.layer.cornerRadius = 20;
    [bottomView addSubview:remakeButton];
    [remakeButton addTarget:self action:@selector(backButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [remakeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bottomView.mas_top).with.offset(30);
        make.leading.equalTo(bottomView.mas_leading).with.offset(60);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    
    UIButton *okButton = [[UIButton alloc] init];
    [okButton setTitle:NSLocalizedString(@"OK", nil) forState:UIControlStateNormal];
    [okButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [okButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
    okButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    okButton.layer.cornerRadius = 20;
    [bottomView addSubview:okButton];
    [okButton addTarget:self action:@selector(okButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [okButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(bottomView.mas_top).with.offset(30);
        make.trailing.equalTo(bottomView.mas_trailing).with.offset(-60);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    
    // 截取圆圈内的图像
    _cutImage = [ImageManager cutImageFromImageView:photoImageView];
}

- (void)okButtonClick:(UIButton *)sender
{
    if (self.dismissVC) {
        self.dismissVC(_cutImage);
    }
}

- (void)backButtonClick:(UIButton *)sender
{
    [self dismissViewControllerAnimated:YES completion:nil];
}


@end
