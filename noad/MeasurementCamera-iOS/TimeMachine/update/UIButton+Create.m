//
//  UIButton+Create.m
//  CPATestDatabase
//
//  Created by fs0011 on 2022/6/28.
//

#import "UIButton+Create.h"
#import "ZYEButton.h"
@implementation UIButton (Create)
+ (id)createButtonWithImageName:(NSString*)name
{
    UIButton* btn;
    if ([name isEqualToString:@""]) {
        btn = [UIButton createButtonWithNormalImageName:nil normalTitle:nil normalColor:nil seletedName:nil seletedTitle:nil seletColor:nil font:nil];
    }
    else
    {
        btn = [UIButton createButtonWithNormalImageName:name normalTitle:nil normalColor:nil seletedName:nil seletedTitle:nil seletColor:nil font:nil];
    }
    
    [btn sizeToFit];
    return  btn;
}

+ (id)createButtonWithNormalImageName:(NSString*)normalName seletedName:(NSString*)name
{
    UIButton* btn = [UIButton createButtonWithNormalImageName:normalName normalTitle:nil normalColor:nil seletedName:name seletedTitle:nil seletColor:nil font:nil];
    [btn sizeToFit];
    return  btn;
}

+ (id)createButtonWithTitle:(NSString*)title color:(UIColor*)color font:(UIFont*)font
{
    UIButton* btn = [UIButton createButtonWithNormalImageName:nil normalTitle:title normalColor:color seletedName:nil seletedTitle:nil seletColor:nil font:font];
    return  btn;
}

+ (id)createButtonWithNormalTitle:(NSString*)title normalColor:(UIColor*)normalColor seletedTitle:(NSString*)seletedTitle seletColor:(UIColor*)seletColor font:(UIFont*)font
{
    UIButton* btn = [UIButton createButtonWithNormalImageName:nil normalTitle:title normalColor:normalColor seletedName:nil seletedTitle:seletedTitle seletColor:seletColor font:font];;
    return  btn;
}


+ (id)createButtonWithTitle:(NSString*)title color:(UIColor*)color font:(UIFont*)font ImageName:(NSString*)normalName
{
    UIButton* btn = [UIButton createButtonWithNormalImageName:normalName normalTitle:title normalColor:color seletedName:nil seletedTitle:nil seletColor:nil font:font];
    return  btn;
}

+ (id)createButtonWithNormalImageName:(NSString*)normalName normalTitle:(NSString*)title normalColor:(UIColor*)normalColor seletedName:(NSString*)name seletedTitle:(NSString*)seletedTitle seletColor:(UIColor*)seletColor font:(UIFont*)font
{
    ZYEButton* btn = [ZYEButton new];
    if (normalName) {
        [btn setImage:[UIImage imageNamed:normalName] forState:0];
    }
    if (title) {
        [btn setTitle:title forState:0];
    }
    if (normalColor) {
        [btn setTitleColor:normalColor forState:0];
    }
    if (seletColor) {
        [btn setTitleColor:seletColor forState:UIControlStateSelected];
    }
    if (name) {
        [btn setImage:[UIImage imageNamed:name] forState:UIControlStateSelected];
    }
    if (seletedTitle) {
        [btn setTitle:seletedTitle forState:UIControlStateSelected];
    }
    if (font) {
        btn.titleLabel.font = font;
    }
    [btn sizeToFit];
    return btn;
}

+ (id)createButtonWithBGImageName:(NSString*)name
{
    ZYEButton* btn = [ZYEButton new];
    [btn setBackgroundImage:[UIImage imageNamed:name] forState:0];
    return btn;
}
@end
