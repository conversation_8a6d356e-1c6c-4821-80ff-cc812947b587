//
//  ZYEUpdateView.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/20.
//

#import "ZYEUpdateView.h"
#import "PopView.h"
#import "UIButton+Create.h"
#import "UIImageView+Create.h"
#import "UIButton+Create.h"
@implementation ZYEUpdateView
{
    CompletionBlock _actionBlock;
}
- (instancetype)initWithdetail:(NSString*)detail ationCallback:(CompletionBlock)action isForce:(BOOL)force;
{
    if(self==[super init])
    {
        
        self.layer.cornerRadius = 16;
        self.layer.masksToBounds = YES;
        self.backgroundColor = [UIColor whiteColor];
        [self layoutWitTitle:@"更新" detail:(NSString*)detail isForce:force];
        _actionBlock = action;
        [self mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(275);
            
        }];
        
    }
    return self;
}

- (void)layoutWitTitle:(NSString*)title detail:(NSString*)detail isForce:(BOOL)force
{
    UILabel* titleLabel = [UILabel createLabelWithTitle:title textColor:darkTextColor textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:16 weight:UIFontWeightMedium]];
    [self addSubview:titleLabel];
    titleLabel.numberOfLines = 0;
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(24);
        make.centerX.mas_equalTo(0);
    }];
    
    UILabel* detailLabel = [UILabel createLabelWithTitle:detail textColor:[UIColor colorWithHexString:@"#434343" alpha:1] textAlignment:NSTextAlignmentCenter font:[UIFont systemFontOfSize:16]];
    detailLabel.numberOfLines = 0;
    [self addSubview:detailLabel];
    [detailLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(titleLabel.mas_bottom).offset(34);
        make.width.mas_equalTo(240);
    }];
    UIView* bottom = detailLabel;
    if(!force)
    {
        UIButton* btnleft = [UIButton createButtonWithTitle:@"忽略此版本" color:darkTextColor font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
        [self addSubview:btnleft];
        [btnleft mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.mas_equalTo(0);
            make.top.mas_equalTo(detailLabel.mas_bottom).offset(24);
        }];
        [btnleft addTarget:self action:@selector(cancel) forControlEvents:UIControlEventTouchUpInside];
        bottom =  btnleft;
    }
    
    UIButton* sure = [UIButton createButtonWithTitle:@"立即更新" color:[UIColor whiteColor] font:[UIFont systemFontOfSize:12]];
    sure.layer.cornerRadius = 22;
    sure.backgroundColor = [UIColor redColor];
    sure.layer.masksToBounds = YES;
    [self addSubview:sure];
    [sure mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(bottom.mas_bottom).offset(24);
        make.centerX.mas_equalTo(0);
        make.bottom.mas_equalTo(-24);
        make.width.mas_equalTo(227);
        make.height.mas_equalTo(44);
    }];
    [sure addTarget:self action:@selector(sureAction:) forControlEvents:UIControlEventTouchUpInside];
}

- (void)cancel
{
    [PopView hidenPopView];
}

- (void)sureAction:(UIButton*)sender
{
    _actionBlock(sender.titleLabel.text);
}

@end
