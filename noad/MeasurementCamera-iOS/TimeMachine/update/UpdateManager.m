//
//  UpdateManager.m
//  photoTimeMachine
//
//  Created by fs0011 on 2023/6/20.
//

#import "UpdateManager.h"
#import "ZYEUpdateView.h"
#import "PopView.h"
@implementation UpdateManager
{
    AFHTTPSessionManager *_sessionManager;
}

+ (instancetype)shareManagerWith:(NSString*)url
{
    static UpdateManager* manager;
    static dispatch_once_t onceToken;
       
        dispatch_once(&onceToken, ^{

            
            manager = [[UpdateManager alloc] initWithUpdateUrl:url];
            manager.AD_count = 3;
        });
    
        return manager;
 
}
- (instancetype)initWithUpdateUrl:(NSString*)url
{
    if(self =  [super init])
    {
        _sessionManager = [AFHTTPSessionManager manager];
        _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
        [_sessionManager.requestSerializer setValue:@"application/json" forHTTPHeaderField:@"Accept"];
        [self postUrl:url];
    }
    return self;
}

- (void)postUrl:(NSString*)url
{
     NSString* str =  W_PERSISTENT_GET_OBJECT(@"version");
    NSString *version = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    
    NSString* urlString = [NSString stringWithFormat:@"%@", url];
    if(str&&[self compairVersion:version newVerSion:str]==NSOrderedSame)
    {
        
    }
    
    [_sessionManager GET:url parameters:nil headers:nil progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSDictionary* dic = responseObject;
        if([dic isKindOfClass:[NSDictionary class]])
        {
            BOOL displayUpdateContent = [dic[@"displayUpdateContent"] boolValue];
            BOOL forceUpdate = [dic[@"forceUpdate"] boolValue];
            BOOL show = displayUpdateContent;
            
            NSString* lastVersion = dic[@"version"];
            W_PERSISTENT_SET_OBJECT(lastVersion, @"version");
            if(displayUpdateContent)
            {
                if(version&&([self compairVersion:lastVersion newVerSion:str]==NSOrderedSame||[self compairVersion:lastVersion newVerSion:str]==NSOrderedDescending))
                {
                    show = NO;
                }
            }
            if(forceUpdate)
            {
                show = YES;
            }
            if(show)
            {
                ZYEUpdateView* update = [[ZYEUpdateView alloc]initWithdetail:forceUpdate?@"当前版本低于最低支持版本":dic[@"updateContent"][@"zh"] ationCallback:^(NSString * _Nonnull result) {
                    NSString* URLString = [@"https://apps.apple.com/cn/app/id1581095417" stringByAddingPercentEscapesUsingEncoding:NSUTF8StringEncoding];;
                    NSURL * urlStr = [NSURL URLWithString:URLString];//后面为参数
                    
                    if ([[UIApplication sharedApplication] canOpenURL:urlStr]) {
                        NSLog(@"can go to test");
                        [[UIApplication sharedApplication] openURL:urlStr options:@{} completionHandler:nil];
                    }else{
                        NSLog(@"can not go to test！！！！！");
                    }
                } isForce:forceUpdate];
                [update layoutIfNeeded];
                PopView* pop = [PopView popSideContentView:update direct:PopViewDirection_SlideInCenter];
                pop.clickOutHidden = NO;
                pop.backgroundColor = [[UIColor blackColor]colorWithAlphaComponent:0.5];
            }
            
            if(self.complete)
            {
                NSInteger count = [responseObject[@"AD_count"] intValue]==0?3:[responseObject[@"AD_count"] intValue];
                self.complete(@(count));
                [UpdateManager shareManagerWith:updateJsonUrl].AD_count = count;
                W_PERSISTENT_SET_OBJECT(@(count), @"ad_count");
            }
            
            
        }
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        if(self.complete)
        {
            self.complete(@(3));
            [UpdateManager shareManagerWith:updateJsonUrl].AD_count = 3;
        }
    }];
    
    
    
    
}


- (NSComparisonResult)compairVersion:(NSString*)versionCurrent newVerSion:(NSString*)newVersion
{
    NSString *currentVersion = versionCurrent;
    NSString *targetVersion = newVersion; // 目标版本号
    
    // 获取版本号的组成部分（以"."分割）
    NSArray *currentComponents = [currentVersion componentsSeparatedByString:@"."];
    NSArray *targetComponents = [targetVersion componentsSeparatedByString:@"."];
    
    // 补充0，使两个版本号的组成部分个数相同
    NSUInteger maxComponents = MAX(currentComponents.count, targetComponents.count);
    NSMutableArray *paddedCurrentComponents = [NSMutableArray arrayWithArray:currentComponents];
    NSMutableArray *paddedTargetComponents = [NSMutableArray arrayWithArray:targetComponents];
    
    while (paddedCurrentComponents.count < maxComponents) {
        [paddedCurrentComponents addObject:@"0"];
    }
    
    while (paddedTargetComponents.count < maxComponents) {
        [paddedTargetComponents addObject:@"0"];
    }
    
    // 比较每个组成部分
    NSComparisonResult result = NSOrderedSame;
    for (NSInteger i = 0; i < maxComponents; i++) {
        NSInteger currentComponent = [paddedCurrentComponents[i] integerValue];
        NSInteger targetComponent = [paddedTargetComponents[i] integerValue];
        
        if (currentComponent < targetComponent) {
            result = NSOrderedAscending;
            break;
        } else if (currentComponent > targetComponent) {
            result = NSOrderedDescending;
            break;
        }
    }
    
    if (result == NSOrderedSame) {
        NSLog(@"当前版本号和目标版本号相同");
    } else if (result == NSOrderedAscending) {
        NSLog(@"当前版本号低于目标版本号");
    } else if (result == NSOrderedDescending) {
        NSLog(@"当前版本号高于目标版本号");
    }
    return result;
}

@end
