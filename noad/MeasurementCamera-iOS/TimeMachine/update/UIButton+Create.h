//
//  UIButton+Create.h
//  CPATestDatabase
//
//  Created by fs0011 on 2022/6/28.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface UIButton (Create)
+ (id)createButtonWithImageName:(NSString*)name;
+ (id)createButtonWithNormalImageName:(NSString*)normalName seletedName:(NSString*)name;
+ (id)createButtonWithTitle:(NSString*)title color:(UIColor*)color font:(UIFont*)font;
+ (id)createButtonWithTitle:(NSString*)title color:(UIColor*)color font:(UIFont*)font ImageName:(NSString*)normalName;
+ (id)createButtonWithNormalTitle:(NSString*)title normalColor:(UIColor*)normalColor seletedTitle:(NSString*)seletedTitle seletColor:(UIColor*)seletColor font:(UIFont*)font;
+ (id)createButtonWithNormalImageName:(NSString*)normalName normalTitle:(NSString*)title normalColor:(UIColor*)normalColor seletedName:(NSString*)name seletedTitle:(NSString*)seletedTitle seletColor:(UIColor*)seletColor font:(UIFont*)font;
+ (id)createButtonWithBGImageName:(NSString*)name;
@end

NS_ASSUME_NONNULL_END
