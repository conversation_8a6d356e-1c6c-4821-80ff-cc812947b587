//
//  UIImageView+Create.m
//  CPATestDatabase
//
//  Created by fs0011 on 2022/6/28.
//

#import "UIImageView+Create.h"

@implementation UIImageView (Create)
+ (id)createRoundImageviewName:(NSString*)name withWidth:(CGFloat)width;
{
    UIImageView* im  = [[UIImageView alloc]init];
    [im setImage:[UIImage imageNamed:name]];
    im.width = im.height = width;
    im.layer.cornerRadius = width*1.0/2;
    im.layer.masksToBounds = YES;
    return im;
}
+ (id)createSizeFitImageviewName:(NSString*)name
{
    UIImageView* im  = [[UIImageView alloc]init];
    if(![name isEqualToString:@""])
    {
        [im setImage:[UIImage imageNamed:name]];
        
        [im sizeToFit];
    }
    return im;
}
@end
