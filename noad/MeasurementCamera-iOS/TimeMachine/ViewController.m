//
//  ViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import "ViewController.h"
#import "HomeCell.h"
#import "YoungViewController.h"
#import "AgednessViewController.h"
#import "BabyViewController.h"
#import "PalmsViewController.h"
#import "CartoonViewController.h"
#import "MagicViewController.h"
#import "PsychologicalViewController.h"
#import "FileManager.h"
#import "DatabaseManager.h"
#import "PalmsDatabaseManager.h"
#import "PsychologicalDatabaseManager.h"
#import "FacePointsTool.h"
#import "TingSimpleScrollView.h"
#import "PopView.h"
#import "IPChangeIconView.h"
#import "UpgradeVIPViewController.h"
#import "FacePalmController.h"
#import "MyImagePickerController.h"
#import "MyImagePickerResultVC.h"
#import "UIImage+FixOrientation.h"
#import "TMYearViewController.h"
#import "Masonry/Masonry.h"
#import "CardSliderView.h"
#import "AllFuncShowView.h"
#import "FaceAnalysisVC.h"
@interface ViewController () <UITableViewDelegate, UITableViewDataSource, TingSimpleScrollViewDelegate, TingSimpleScrollViewDatasource,MyAdManagerDelegate,CardSliderViewDelegate,AllFuncShowViewDelegate>

@property (nonatomic, strong) NSArray *itemList;

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *psychologicalList;

@property (nonatomic, strong) TingSimpleScrollView *simpleScrollView;

@property (nonatomic) BOOL isIPad;

@property (nonatomic) BOOL isLanguageZH;

@end

@implementation ViewController

- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    
    self.navigationController.navigationBarHidden = YES;
    self.navigationController.navigationBar.translucent = YES;
    
    }

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [FileManager removeDocumentsFileName:ShareImage];
    [FileManager removeDocumentsFileName:[DATABASE stringByAppendingPathComponent:@""]];
    //[FileManager removeDocumentsFileName:MagicTemplate];
    
    self.isIPad = [self getIsIPad];
    self.isLanguageZH = [self getCurrentLanguageIsZH];
    
    self.itemList = [NSArray arrayWithObjects:@{@"title":NSLocalizedString(@"MagicFaceChange", nil), @"image":@"首页变脸魔法"}, @{@"title":NSLocalizedString(@"CartoonEffects", nil), @"image":@"首页卡通特效"}, @{@"title":NSLocalizedString(@"PalmReading", nil), @"image":@"首页手相解读"}, @{@"title":NSLocalizedString(@"GetYoung", nil), @"image":@"首页一键变年轻"}, @{@"title":NSLocalizedString(@"GetAgedness", nil), @"image":@"首页时光变老机"}, @{@"title":NSLocalizedString(@"GetBaby", nil), @"image":@"首页宝宝预测"}, nil];
    
    //[self initViews];
    [self initNewView];
    
    PalmsDatabaseManager *palmsDatabaseManager = [[PalmsDatabaseManager alloc] init];
    [palmsDatabaseManager loadData];
    
    PsychologicalDatabaseManager *psychologicalDatabaseManager = [[PsychologicalDatabaseManager alloc] init];
    [psychologicalDatabaseManager loadData];
    
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    self.psychologicalList = [databaseManager obtainPsychologicalListTable];
    
    [self.tableView reloadData];
    
    NSString *isFirstMagicFace = [[NSUserDefaults standardUserDefaults] objectForKey:@"isFirstMagicFace"];
    if ([isFirstMagicFace isEqualToString:@"1"]) {
        BOOL isCreateMagicTemplate = [databaseManager createMagicTemplateListTable];
        if (isCreateMagicTemplate == YES) {
            [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:@"isFirstMagicFace"];
            NSArray *templateList = [NSArray arrayWithObjects:@"template1", @"template2", @"template3", nil];
            for (int i = 0; i < templateList.count; i++) {
                [databaseManager insertMagicTemplateTableWithTemplate_name:templateList[i]];
                
                UIImage *image = [UIImage imageNamed:[NSString stringWithFormat:@"%@.jpg", templateList[i]]];
                NSData *imageData = UIImageJPEGRepresentation(image, 1.0);
                NSString *imagePath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg", templateList[i]]];
                [imageData writeToFile:imagePath atomically:YES];
                
                NSString *fromPath = [[NSBundle mainBundle] pathForResource:templateList[i] ofType:@"txt"];
                NSString *toPath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.txt", templateList[i]]];
                [[NSFileManager defaultManager] copyItemAtPath:fromPath toPath:toPath error:nil];
            }
        }
    }
    
    NSLog(@"DocPath == %@", [FileManager pathForDocuments]);
    
    UIButton* btn = [UIButton createButtonWithTitle:local(@"更换logo") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:14]];
    [self.view addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-15);
        make.top.mas_equalTo(self.view.mas_topMargin).offset(15);
    }];
    [btn addTarget:self action:@selector(changeLogo) forControlEvents:UIControlEventTouchUpInside];
    
    UIButton* btn2 = [UIButton createButtonWithImageName:@"VIP"];
    [self.view addSubview:btn2];
    [btn2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(15);
        make.centerY.mas_equalTo(btn);
        make.width.height.mas_equalTo(50);
    }];
    [btn2 bk_whenTapped:^{
        UpgradeVIPViewController *vc = [[UpgradeVIPViewController alloc] init];
        vc.from = @"首页";
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
    }];
   
    
    UIButton *itemButton = [[UIButton alloc] init];
    itemButton.tag = 10000+6;
    [itemButton addTarget:self action:@selector(photoFilterButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view  addSubview:itemButton];
    
}


- (void)initViews
{
    self.view.backgroundColor = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
    
    if (self.tableView == nil) {
        self.tableView = [[UITableView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight-kBottomSafeHeight) style:UITableViewStylePlain];
        self.tableView.delegate = self;
        self.tableView.dataSource = self;
        [self.view addSubview:self.tableView];
        [self.tableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
        self.tableView.showsVerticalScrollIndicator = NO;
        self.tableView.showsHorizontalScrollIndicator = NO;
        self.tableView.bounces = NO;
        
        if (@available(iOS 11.0, *)) {
            self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        } else {
            self.automaticallyAdjustsScrollViewInsets = NO;
        }
        
        [self setupTableHeaderView];
        [self showBannerView:CGRectMake(0, self.view.size.height-90, SCREEN_WIDTH, 90) size:CGSizeMake(SCREEN_WIDTH, 90)];
    }
}

- (void)myAdManagerBannerViewCloseRefresh
{
    self.tableView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight-kBottomSafeHeight);
}

- (void)myAdManagerBannerAdViewRenderSuccess
{
    self.tableView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight-kBottomSafeHeight-90);
}

- (void)showAdPageViewController
{
    self.simpleScrollView = [[TingSimpleScrollView alloc] initWithFrame:self.view.bounds];
    self.simpleScrollView.delegate = self;
    self.simpleScrollView.datasource = self;
    [self.view addSubview:self.simpleScrollView];
}

- (void)initNewView {
    self.view.backgroundColor = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
    
    UIImageView *topImgView = [[UIImageView alloc] initWithFrame:CGRectZero];
    topImgView.image = [UIImage imageNamed:@"新首页背景"];
    [self.view addSubview:topImgView];
    [topImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    UIImageView *kqqhzlmageView = [[UIImageView alloc] initWithFrame:CGRectZero];
    kqqhzlmageView.contentMode = UIViewContentModeScaleAspectFit;
    kqqhzlmageView.image = [UIImage imageNamed:@"新横幅"];
    [self.view addSubview:kqqhzlmageView];
    [kqqhzlmageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(@(285));
        make.width.equalTo(@(274*scaleX));
        make.height.equalTo(@(38*scaleX));
    }];
    kqqhzlmageView.userInteractionEnabled = YES;
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                   action:@selector(aiTouch)];
    [kqqhzlmageView addGestureRecognizer:tap];
    
    UILabel *ai = [[UILabel alloc] initWithFrame:CGRectZero];
    ai.text = local(@"生成你的AI年度运势报告");
    ai.font = [UIFont systemFontOfSize:20*scaleX weight:UIFontWeightSemibold];
    ai.textColor = [MyColor colorWithHexString:@"#000000" alpha:0.87];
    [kqqhzlmageView addSubview:ai];
    [ai mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(kqqhzlmageView);
        make.centerY.equalTo(kqqhzlmageView);
    }];
    
    UIImageView *allFunc = [[UIImageView alloc] initWithFrame:CGRectZero];
    allFunc.image = [UIImage imageNamed:@"新全部功能"];
    allFunc.contentMode = UIViewContentModeScaleAspectFit;
    [self.view addSubview:allFunc];
    [allFunc mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(95*scaleX));
        make.height.equalTo(@(25*scaleX));
        make.right.equalTo(self.view);
        if(self.isIPad == YES) {
            make.centerY.equalTo(self.view).offset(-10*scaleX);
        } else {
            make.top.equalTo(kqqhzlmageView.mas_bottom).offset(55*scaleX);
        }
        
    }];
    allFunc.userInteractionEnabled = YES;
    UITapGestureRecognizer *functap = [[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                   action:@selector(allFuncTouch)];
    [allFunc addGestureRecognizer:functap];
     
    UILabel *allLabel = [[UILabel alloc] initWithFrame:CGRectZero];
    allLabel.text = local(@"全部功能");
    allLabel.font = [UIFont systemFontOfSize:14*scaleX weight:UIFontWeightMedium];
    allLabel.textColor = [UIColor whiteColor];
    [allFunc addSubview:allLabel];
    [allLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(16*scaleX));
        make.top.equalTo(@(3*scaleX));
    }];
    
    CardSliderView  *sliderView = [[CardSliderView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, 304)];
    [self.view addSubview:sliderView];
    [sliderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.view);
        make.top.equalTo(allFunc.mas_bottom).offset(22);
        make.height.equalTo(@(240));
    }];
    sliderView.delegate = self;
     
    
}


- (void)setupTableHeaderView
{
    UIView *headerView = [[UIView alloc] init];
    headerView.backgroundColor = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
    // 设置 view 的 frame(将设置 frame 提到设置 tableHeaderView 之前)
    headerView.frame = CGRectMake(0, 0, kScreenWidth, 900);
    // 设置 tableHeaderView
    self.tableView.tableHeaderView = headerView;
    
    UIImageView *bgImgView = [[UIImageView alloc] init];
    bgImgView.image = [UIImage imageNamed:@"首页背景"];
    [headerView addSubview:bgImgView];
    
    UIImageView *topImgView = [[UIImageView alloc] initWithFrame:CGRectMake(30, 0, kScreenWidth-60, (kScreenWidth-60)*0.87)];
    topImgView.image = [UIImage imageNamed:@"首页位图"];
    [headerView addSubview:topImgView];
    
    UIImageView *kqqhzlmageView = [[UIImageView alloc] initWithFrame:CGRectMake(66, topImgView.bounds.size.height-26-(topImgView.bounds.size.width-132)/5, topImgView.bounds.size.width-132, (topImgView.bounds.size.width-132)/5)];
    kqqhzlmageView.contentMode = UIViewContentModeScaleAspectFit;
    kqqhzlmageView.image = [UIImage imageNamed:@"开启奇幻之旅"];
    [topImgView addSubview:kqqhzlmageView];
    
    
    
    
    UILabel *kqqhzlLabel = [[UILabel alloc] initWithFrame:kqqhzlmageView.bounds];
    kqqhzlLabel.text = local(@"测测你的面相");
    kqqhzlLabel.textAlignment = NSTextAlignmentCenter;
    kqqhzlLabel.textColor = [UIColor blackColor];
    [kqqhzlmageView addSubview:kqqhzlLabel];
    if (self.isIPad == YES) {
        kqqhzlLabel.font = [UIFont boldSystemFontOfSize:60.0];
    } else {
        if (self.isLanguageZH == YES) {
            kqqhzlLabel.font = [UIFont boldSystemFontOfSize:20.0];
        } else {
            kqqhzlLabel.font = [UIFont boldSystemFontOfSize:16.0];
        }
    }
    kqqhzlmageView.superview.userInteractionEnabled = YES;
    kqqhzlmageView.userInteractionEnabled = YES;
    [kqqhzlmageView bk_whenTapped:^{
        UIButton *itemButton = [[UIButton alloc] init];
        itemButton.tag = 10000+6;
        [self photoFilterButtonClick:itemButton];
    }];
    

    
    
    for (int i = 0; i < 3; i++) {
        NSDictionary *item = _itemList[i];
        
        NSInteger row = (int)i / 3;
        NSInteger col = (int)i % 3;
        
        UIImageView *itemImgView = [[UIImageView alloc] initWithFrame:CGRectMake(12+((kScreenWidth-48)/3+12)*col, (kScreenWidth-60)*0.87+8+((kScreenWidth-48)/3*1.752+50)*row, (kScreenWidth-48)/3, (kScreenWidth-48)/3*1.752)];
        itemImgView.image = [UIImage imageNamed:item[@"image"]];
        itemImgView.userInteractionEnabled = YES;
        [headerView addSubview:itemImgView];
        
        UIButton *itemButton = [[UIButton alloc] initWithFrame:itemImgView.bounds];
        itemButton.tag = 10000+i;
        [itemButton addTarget:self action:@selector(photoFilterButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [itemImgView addSubview:itemButton];
        
        UILabel *labelTitle = [[UILabel alloc] initWithFrame:CGRectMake(12+((kScreenWidth-48)/3+12)*col, itemImgView.frame.origin.y+itemImgView.bounds.size.height+2, (kScreenWidth-48)/3, 32)];
        [labelTitle setText:item[@"title"]];
        [labelTitle setTextColor:[UIColor whiteColor]];
        [labelTitle setTextAlignment:NSTextAlignmentCenter];
        labelTitle.tag = 20000+i;
        labelTitle.numberOfLines = 0;
        [headerView addSubview:labelTitle];
        if (self.isIPad == YES) {
            if (self.isLanguageZH == YES) {
                [labelTitle setFont:[UIFont systemFontOfSize:18.0]];
            } else {
                [labelTitle setFont:[UIFont systemFontOfSize:16.0]];
            }
        } else {
            if (self.isLanguageZH == YES) {
                [labelTitle setFont:[UIFont systemFontOfSize:14.0]];
            } else {
                [labelTitle setFont:[UIFont systemFontOfSize:12.0]];
            }
        }
        
        if (i > 2) {
            itemButton.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
            
            UIImageView *lockImageView = [[UIImageView alloc] initWithFrame:CGRectMake(itemImgView.bounds.size.width/2-16, itemImgView.bounds.size.height/2-16, 32, 32)];
            lockImageView.image = [UIImage imageNamed:@"lock.png"];
            lockImageView.contentMode = UIViewContentModeScaleAspectFit;
            [itemImgView addSubview:lockImageView];
        } else {
            itemButton.backgroundColor = [UIColor clearColor];
        }
        if (i == 2)
        {
            UIImageView *lockImageView = [[UIImageView alloc] initWithFrame:CGRectMake(itemImgView.bounds.size.width-32-8, 8, 32, 32)];
            lockImageView.image = [UIImage imageNamed:@"AITip"];
            lockImageView.contentMode = UIViewContentModeScaleAspectFit;
            [itemImgView addSubview:lockImageView];
        }
    }
    
    UILabel *labelItem = (UILabel *)[headerView viewWithTag:20002];
    
    [bgImgView setFrame:CGRectMake(0, 0, kScreenWidth, labelItem.frame.origin.y + labelItem.bounds.size.height + 22)];
    
    
    CAGradientLayer *gl = [CAGradientLayer layer];
    gl.frame = CGRectMake(0,0,SCREEN_WIDTH-12*2*scaleX,48*scaleX);
    gl.startPoint = CGPointMake(0.2, 0.11);
    gl.endPoint = CGPointMake(-0.21, 0.51);
    gl.colors = @[(__bridge id)[UIColor colorWithRed:181/255.0 green:127/255.0 blue:111/255.0 alpha:1].CGColor, (__bridge id)[UIColor colorWithRed:254/255.0 green:236/255.0 blue:191/255.0 alpha:1].CGColor];
    gl.locations = @[@(0), @(1.0f)];

    
    UIButton *btn = [UIButton createButtonWithTitle:local(@"生成您的AI年度运势报告") color:[UIColor whiteColor] font:[UIFont systemFontOfSize:14 weight:UIFontWeightMedium]];
    btn.frame = CGRectMake(12*scaleX,bgImgView.bottom+12*scaleX,SCREEN_WIDTH-12*2*scaleX,48*scaleX);
    [btn.layer insertSublayer:gl atIndex:0];
    btn.layer.cornerRadius = 22;
    btn.layer.masksToBounds = YES;
    [headerView addSubview:btn];
    
    btn.layer.cornerRadius = 8;
    
    [headerView setFrame:CGRectMake(0, 0, kScreenWidth, bgImgView.bounds.size.height+48*scaleX+12*2*scaleX + 60)];
    [btn bk_whenTapped:^{
        if([APPMakeStoreIAPManager featureVip]!=YES)
        {
            UpgradeVIPViewController *vc = [[UpgradeVIPViewController alloc] init];
            vc.from = @"年度运势";
            [self.navigationController pushViewController:vc animated:YES];
            return;
        }
        TMYearViewController* tmvc = [TMYearViewController new];
        [self.navigationController pushViewController:tmvc animated:YES];
    }];
    
    // 心理测试
    UILabel *labelMentalTest = [[UILabel alloc] initWithFrame:CGRectMake(12, bgImgView.bounds.size.height+ 48*scaleX+12*2*scaleX + 26, 200, 20)];
    [labelMentalTest setText:NSLocalizedString(@"PsychologicalTest", nil)];
    [labelMentalTest setTextColor:[UIColor whiteColor]];
    [labelMentalTest setFont:[UIFont systemFontOfSize:17.0]];
    [headerView addSubview:labelMentalTest];
    if (self.isLanguageZH == YES) {
        [labelMentalTest setFont:[UIFont systemFontOfSize:17.0]];
    } else {
        [labelMentalTest setFont:[UIFont systemFontOfSize:16.0]];
    }
}

- (void)photoFilterButtonClick:(UIButton *)sender
{
    NSInteger tagIndex = sender.tag - 10000;
    NSLog(@"tag == %ld", (long)tagIndex);
    
    switch (tagIndex) {
        case 0:
        {
            [MobClick event:@"Home" attributes:@{@"source":@"魔法变脸"}];
            MagicViewController *vc = [[MagicViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 1:
        {
            [self showFullscreenVideoAd];
            [MobClick event:@"Home" attributes:@{@"source":@"卡通特效"}];
            CartoonViewController *vc = [[CartoonViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 2:
        {
            
            [self showFullscreenVideoAd];
            PalmsViewController *vc = [[PalmsViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
            [MobClick event:@"Home" attributes:@{@"source":@"手相"}];
            
//            PsychologicalViewController *vc = [[PsychologicalViewController alloc] init];
            
//            vc.isHandPrint = YES;
//            [self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 3:
        {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"HomeTips1", nil)];
            
            //[SVProgressHUD showInfoWithStatus:NSLocalizedString(@"HomeTips1", nil)];
            //YoungViewController *vc = [[YoungViewController alloc] init];
            //[self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 4:
        {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"HomeTips1", nil)];
            //AgednessViewController *vc = [[AgednessViewController alloc] init];
            //[self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 5:
        {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"HomeTips1", nil)];
            //BabyViewController *vc = [[BabyViewController alloc] init];
            //[self.navigationController pushViewController:vc animated:YES];
        }
            break;
        case 6:
        {
            [MobClick event:@"Home" attributes:@{@"source":@"脸相"}];
//            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"HomeTips1", nil)];
            //BabyViewController *vc = [[BabyViewController alloc] init];
            if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
                MyImagePickerController *vc = [[MyImagePickerController alloc] init];
                vc.delegate = self; //delegate遵循了两个代理
                vc.allowsEditing = NO;
                vc.sourceType = UIImagePickerControllerSourceTypeCamera;
                vc.cameraDevice = UIImagePickerControllerCameraDeviceFront;
                vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
                vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
                [self presentViewController:vc animated:YES completion:^{}];
                __weak typeof(vc) weakVC = vc;
                vc.openAlbum = ^(UIImage * _Nonnull image) {
                    [weakVC dismissViewControllerAnimated:YES completion:^{
                        FacePalmController *vc = [[FacePalmController alloc] init];
                        vc.image = image;
                        [self.navigationController pushViewController:vc animated:YES];
                    }];
                };
            }
            
        }
            break;
            
        default:
            break;
    }
}

#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    UIImage *photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
    photoImage = [photoImage fixOrientation];
    //改变该图片的方向
    photoImage = [UIImage imageWithCGImage:photoImage.CGImage scale:photoImage.scale orientation:UIImageOrientationUpMirrored];
    NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
    //  截取图像
    
    MyImagePickerResultVC *vc = [[MyImagePickerResultVC alloc] init];
    vc.photoImage = photoImage;
    vc.modalPresentationStyle = UIModalPresentationFullScreen;
    [picker presentViewController:vc animated:YES completion:^{}];
    __weak typeof(vc) weakVC = vc;
    vc.dismissVC = ^(UIImage * _Nonnull image) {
        [weakVC dismissViewControllerAnimated:YES completion:^{
            [picker dismissViewControllerAnimated:NO completion:^{
            }];
            //FacePalmController *vc = [[FacePalmController alloc] init];
            FaceAnalysisVC *vc = [[FaceAnalysisVC alloc] init];
            vc.image = image;
            vc.hidesBottomBarWhenPushed = YES;
            [self.navigationController pushViewController:vc animated:YES];
            
        }];
    };
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:^{
            
    }];
}

#pragma mark -CardSliderDelegate

- (void)faceMagicTouch:(CardView *)view {
    [MobClick event:@"Home" attributes:@{@"source":@"魔法变脸"}];
    MagicViewController *vc = [[MagicViewController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)handTouch:(CardView *)view {
    [self showFullscreenVideoAd];
    PalmsViewController *vc = [[PalmsViewController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
    [MobClick event:@"Home" attributes:@{@"source":@"手相"}];
}

- (void)catoonTouch:(CardView *)view {
    [self showFullscreenVideoAd];
    [MobClick event:@"Home" attributes:@{@"source":@"卡通特效"}];
    CartoonViewController *vc = [[CartoonViewController alloc] init];
    vc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)timeOldTouch:(CardView *)view {
    [MobClick event:@"Home" attributes:@{@"source":@"脸相"}];
    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        MyImagePickerController *vc = [[MyImagePickerController alloc] init];
        vc.delegate = self; //delegate遵循了两个代理
        vc.allowsEditing = NO;
        vc.sourceType = UIImagePickerControllerSourceTypeCamera;
        vc.cameraDevice = UIImagePickerControllerCameraDeviceFront;
        vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
        vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
        [self presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.openAlbum = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                //FacePalmController *vc = [[FacePalmController alloc] init];
                FaceAnalysisVC *vc = [[FaceAnalysisVC alloc] init];
                vc.image = image;
                vc.hidesBottomBarWhenPushed = YES;
                [self.navigationController pushViewController:vc animated:YES];
            }];
        };
    }
}

- (void)aiTouch {
    if([APPMakeStoreIAPManager featureVip]!=YES)
    {
        UpgradeVIPViewController *vc = [[UpgradeVIPViewController alloc] init];
        vc.from = @"年度运势";
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    TMYearViewController* tmvc = [TMYearViewController new];
    tmvc.hidesBottomBarWhenPushed = YES;
    [self.navigationController pushViewController:tmvc animated:YES];
}

- (void)allFuncTouch {
    AllFuncShowView *vc = [[AllFuncShowView alloc] initWithFrame:self.view.bounds];
    vc.delegate = self;
    [self.view addSubview:vc];
}


#pragma mark - UITableView DataSource
- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView
{
    if (tableView == _tableView) {
        return 1;
    }
    return 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    if (tableView == _tableView) {
        return _psychologicalList.count;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == _tableView) {
        static NSString *CellIdentifier = @"Cell";
        HomeCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
        if (cell == nil) {
            cell = [[HomeCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
        }
        NSDictionary *pDict = _psychologicalList[indexPath.row];
        cell.labelTitle.text = [NSString stringWithFormat:@"%ld.%@", indexPath.row+1, pDict[@"desc"]];
        //cell.labelTitle.text = pDict[@"title"];
        cell.labelDesc.text = pDict[@"desc"];
        
        return cell;
    }
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == _tableView) {
        UITableViewCell *cell = [self tableView:tableView cellForRowAtIndexPath:indexPath];
        return cell.bounds.size.height;
    }
    return 0;
}

#pragma mark - Table view delegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (tableView == _tableView) {
        PsychologicalViewController *vc = [[PsychologicalViewController alloc] init];
        NSDictionary *pDict = _psychologicalList[indexPath.row];
        vc.pDict = pDict;
        [self.navigationController pushViewController:vc animated:YES];
    }
}


#pragma mark -TingSimpleScrollViewDelegate
- (void)tingSimpleScrollView:(TingSimpleScrollView *)simpleScrollView clickAtIndex:(NSInteger)index
{
    if (simpleScrollView == _simpleScrollView) {
        NSLog(@"index ==%ld", (long)index);
    }
}

#pragma mark -TingSimpleScrollViewDatasource
- (NSInteger)numberOfPagesInTingSimpleScrollView:(TingSimpleScrollView *)simpleScrollView
{
    if (simpleScrollView == _simpleScrollView) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:simpleScrollView.bounds];
        imageView.image = [UIImage imageNamed:[NSString stringWithFormat:@"template%ld.jpg", (long)index+1]];
        
        return 0;
    }
    return 0;
}

- (UIView *)tingSimpleScrollView:(TingSimpleScrollView *)simpleScrollView pageAtIndex:(NSInteger)index
{
    if (simpleScrollView == _simpleScrollView) {
        UIImageView *imageView = [[UIImageView alloc] initWithFrame:simpleScrollView.bounds];
        
        return imageView;
    }
    
    return [UIView new];
}


//如果想要判断设备是ipad，要用如下方法
- (BOOL)getIsIPad
{
    NSString *deviceType = [UIDevice currentDevice].model;
    
    if([deviceType isEqualToString:@"iPhone"]) {
        //iPhone
        return NO;
    }
    else if([deviceType isEqualToString:@"iPod touch"]) {
        //iPod Touch
        return NO;
    }
    else if([deviceType isEqualToString:@"iPad"]) {
        //iPad
        return YES;
    }
    return NO;
}

// 判断是中文还是其他语言
- (BOOL)getCurrentLanguageIsZH
{
    BOOL isZH = NO;
    
    NSString *currentLanguage1 = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"][0];
    NSString *currentLanguage2 = [[NSBundle mainBundle] preferredLocalizations][0];
    NSLog(@"language1 == %@   language2 == %@", currentLanguage1, currentLanguage2);
    
    if ([currentLanguage1 containsString:@"zh-Ha"]  ||  [currentLanguage2 containsString:@"zh-Ha"]) {
        isZH = YES;
        NSLog(@"这个是中文");
    }
    
    return isZH;
}


- (void)changeLogo
{
    IPChangeIconView* change = [[IPChangeIconView alloc]initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH-2*tableEdge, 120)];
    [change layoutIfNeeded];
    PopView* pop = [PopView popSideContentView:change direct:PopViewDirection_SlideInCenter];
    [change layout];
    [change layoutIfNeeded];
}
@end
