//
//  PsychologicalDatabaseManager.m
//  TimeMachine
//
//  Created by 叶庭文 on 2021/7/25.
//

#import "PsychologicalDatabaseManager.h"
#import "DatabaseManager.h"

@implementation PsychologicalDatabaseManager

- (void)loadData
{
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    [databaseManager  createPsychologicalListTable];
    [databaseManager  createPsychologicalSubjectTable];
    
    // 爱情观测试题
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"LoveOutlook", nil) desc:NSLocalizedString(@"LoveOutlookDesc", nil) result:NSLocalizedString(@"LoveOutlookResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"1" subject_title:NSLocalizedString(@"BelieveInFate", nil) subject_answer1:NSLocalizedString(@"Believe", nil) answer1_toindex:@"2" subject_answer2:NSLocalizedString(@"Disbelieve", nil) answer2_toindex:@"3" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"2" subject_title:NSLocalizedString(@"MoviesType", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"3" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"4" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"3" subject_title:NSLocalizedString(@"Pursues", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"4" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"5" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"4" subject_title:NSLocalizedString(@"Trousers", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"5" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"6" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"5" subject_title:NSLocalizedString(@"FallInLove", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"6" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"7" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"6" subject_title:NSLocalizedString(@"BigStar", nil) subject_answer1:NSLocalizedString(@"Have", nil) answer1_toindex:@"8" subject_answer2:NSLocalizedString(@"NoHave", nil) answer2_toindex:@"9" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"7" subject_title:NSLocalizedString(@"Clothes", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"10" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"11" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"8" subject_title:NSLocalizedString(@"FirstKiss", nil) subject_answer1:NSLocalizedString(@"Exist", nil) answer1_toindex:@"12" subject_answer2:NSLocalizedString(@"NoExist", nil) answer2_toindex:@"13" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"9" subject_title:NSLocalizedString(@"LoveLetter", nil) subject_answer1:NSLocalizedString(@"Have", nil) answer1_toindex:@"14" subject_answer2:NSLocalizedString(@"NoHave", nil) answer2_toindex:@"15" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"10" subject_title:NSLocalizedString(@"BirthdayWithPartner", nil) subject_answer1:NSLocalizedString(@"Can", nil) answer1_toindex:@"16" subject_answer2:NSLocalizedString(@"NoCan", nil) answer2_toindex:@"17" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"11" subject_title:NSLocalizedString(@"Confess", nil) subject_answer1:NSLocalizedString(@"Can", nil) answer1_toindex:@"18" subject_answer2:NSLocalizedString(@"NoCan", nil) answer2_toindex:@"14" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"12" subject_title:NSLocalizedString(@"ActColder", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"2/5"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"13" subject_title:NSLocalizedString(@"Associate", nil) subject_answer1:NSLocalizedString(@"Can", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"NoCan", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"6/3"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"14" subject_title:NSLocalizedString(@"PerfectHalf", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"4/1"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"15" subject_title:NSLocalizedString(@"AnotherHalf", nil) subject_answer1:NSLocalizedString(@"Can", nil) answer1_toindex:@"18" subject_answer2:NSLocalizedString(@"NoCan", nil) answer2_toindex:@"13" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"16" subject_title:NSLocalizedString(@"FavoriteColor", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"3/4"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"17" subject_title:NSLocalizedString(@"FirstSight", nil) subject_answer1:NSLocalizedString(@"Believe", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Disbelieve", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"5/2"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"1" subject_index:@"18" subject_title:NSLocalizedString(@"VigorousLove", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"1/2"];
    
    // 看看你是否会被情所困
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"BeTrappedByLove", nil) desc:NSLocalizedString(@"BeautifulLove", nil) result:NSLocalizedString(@"BeautifulLoveResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"2" subject_index:@"1" subject_title:NSLocalizedString(@"FirstAppointment", nil) subject_answer1:NSLocalizedString(@"Cafe", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Cinema", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Zoo", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"DepartmentStore", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 你在什么时候最容易紧张
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"Nervous", nil) desc:NSLocalizedString(@"NervousDesc", nil) result:NSLocalizedString(@"NervousResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"1" subject_title:NSLocalizedString(@"Angry", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"2" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"3" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"2" subject_title:NSLocalizedString(@"SayNothing", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"3" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"4" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"3" subject_title:NSLocalizedString(@"GreatPatience", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"4" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"5" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"4" subject_title:NSLocalizedString(@"BoringWork", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"5" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"6" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"5" subject_title:NSLocalizedString(@"FeelTired", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"6" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"7" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"6" subject_title:NSLocalizedString(@"WorryThing", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"7" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"8" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"7" subject_title:NSLocalizedString(@"NumberGame", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"8" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"9" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"8" subject_title:NSLocalizedString(@"ThinkTwice", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"9" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"10" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"9" subject_title:NSLocalizedString(@"DressedWell", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"1/2"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"3" subject_index:@"10" subject_title:NSLocalizedString(@"ObseringOther", nil) subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"3/4"];
    
    // 测测什么坏习惯会让你没朋友
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"BadHabits", nil) desc:NSLocalizedString(@"BadHabitsDesc", nil) result:NSLocalizedString(@"BadHabitsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"4" subject_index:@"1" subject_title:NSLocalizedString(@"Activities", nil) subject_answer1:NSLocalizedString(@"BeParticularlyInterested", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"BeMoreInterested", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"NotVeryInterested", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NotAtAllInterested", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你未来三个月的交友运如何
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"MakeFriendsLuck", nil) desc:NSLocalizedString(@"MakeFriendsDesc", nil) result:NSLocalizedString(@"MakeFriendsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"5" subject_index:@"1" subject_title:NSLocalizedString(@"AttendParty", nil) subject_answer1:NSLocalizedString(@"ASpecialLiking", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"PreferTo", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"DontLike", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"QuiteAnnoying", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你身边有需要靠金钱维持的朋友吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"MoneyFriends", nil) desc:NSLocalizedString(@"MoneyFriendsDesc", nil) result:NSLocalizedString(@"MoneyFriendsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"6" subject_index:@"1" subject_title:NSLocalizedString(@"CriminalRecords", nil) subject_answer1:NSLocalizedString(@"Willing", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyWilling", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"CompletelyUnwilling", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测兄弟会不会为你两肋插刀
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"BrotherHelp", nil) desc:NSLocalizedString(@"BrotherHelpDesc", nil) result:NSLocalizedString(@"BrotherHelpResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"7" subject_index:@"1" subject_title:NSLocalizedString(@"ConfrontParent", nil) subject_answer1:NSLocalizedString(@"Work", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Love", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Learning", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NotAgainst", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的好闺蜜会不会在背后插你一刀
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TestGoodFriends", nil) desc:NSLocalizedString(@"TestGoodFriendsDesc", nil) result:NSLocalizedString(@"TestGoodFriendsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"8" subject_index:@"1" subject_title:NSLocalizedString(@"Complain", nil) subject_answer1:NSLocalizedString(@"Work", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Affection", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"JustAsMuch", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NoneOfThem", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];

    // 测试你认识的新朋友会带你玩还是带你飞
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"NewFriends", nil) desc:NSLocalizedString(@"NewFriendsDesc", nil) result:NSLocalizedString(@"NewFriendsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"9" subject_index:@"1" subject_title:NSLocalizedString(@"Colleagues", nil) subject_answer1:NSLocalizedString(@"Often", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Occasionally", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"NoHave", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的社交盲点是什么
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"SocialBlindSpots", nil) desc:NSLocalizedString(@"SocialBlindSpotsDesc", nil) result:NSLocalizedString(@"SocialBlindSpotsResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"10" subject_index:@"1" subject_title:NSLocalizedString(@"MidnightAwoke", nil) subject_answer1:NSLocalizedString(@"WC", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Disturbed", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Dream", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你朋友会不会遵守和你的约定
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"Abideagreement", nil) desc:NSLocalizedString(@"AbideagreementDesc", nil) result:NSLocalizedString(@"AbideagreementResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"11" subject_index:@"1" subject_title:NSLocalizedString(@"RevengeInevitable", nil) subject_answer1:NSLocalizedString(@"OfCourseIs", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"VeryLikelyIs", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Occasionally", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NotAtAll", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的脾气爆炸点是什么
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TemperamentExplodePoint", nil) desc:NSLocalizedString(@"TemperamentExplodePointDesc", nil) result:NSLocalizedString(@"TemperamentExplodePointResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"12" subject_index:@"1" subject_title:NSLocalizedString(@"HelpTramp", nil) subject_answer1:NSLocalizedString(@"SureThing", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Basically", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"BasicallyNot", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"AbsolutelyNot", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你情绪易怒性有多少
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"EmotionAngry", nil) desc:NSLocalizedString(@"EmotionAngryDesc", nil) result:NSLocalizedString(@"EmotionAngryResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"13" subject_index:@"1" subject_title:NSLocalizedString(@"SecretiveNoSay", nil) subject_answer1:NSLocalizedString(@"MustSay", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"HoldWhile", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"ToFriendSay", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"AbsoluteNotSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你是个遇强则强遇弱则弱的人吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"StrongWeak", nil) desc:NSLocalizedString(@"StrongWeakDesc", nil) result:NSLocalizedString(@"StrongWeakResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"14" subject_index:@"1" subject_title:NSLocalizedString(@"FoodSpoilsWithoutEating", nil) subject_answer1:NSLocalizedString(@"Never", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyDo", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"OftenDo", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Always", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你是温柔腹黑女吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"GentleBlackWoman", nil) desc:NSLocalizedString(@"GentleBlackWomanDesc", nil) result:NSLocalizedString(@"GentleBlackWomanResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"15" subject_index:@"1" subject_title:NSLocalizedString(@"AnImpassableBarrier", nil) subject_answer1:NSLocalizedString(@"Always", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OftenDo", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"OccasionallyDo", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Never", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测试你是一个心计高手吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TestSchemer", nil) desc:NSLocalizedString(@"TestSchemerDesc", nil) result:NSLocalizedString(@"TestSchemerResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"16" subject_index:@"1" subject_title:NSLocalizedString(@"WhyGrumpy", nil) subject_answer1:NSLocalizedString(@"BeWronged", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Betrayed", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"BeQuestioned", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的窝囊指数
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"WimpIndex", nil) desc:NSLocalizedString(@"WimpIndexDesc", nil) result:NSLocalizedString(@"WimpIndexResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"17" subject_index:@"1" subject_title:NSLocalizedString(@"BeSlander", nil) subject_answer1:NSLocalizedString(@"Colleague", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"ExLover", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Classmate", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Stranger", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你在别人眼中是不是乖巧小绵羊
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"CleverHoggerel", nil) desc:NSLocalizedString(@"CleverHoggerelDesc", nil) result:NSLocalizedString(@"CleverHoggerelResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"18" subject_index:@"1" subject_title:NSLocalizedString(@"WorryExpect", nil) subject_answer1:NSLocalizedString(@"Worry", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"Expect", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Both", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"None", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你性格中的豪气有几分
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TestHeroicSpirit", nil) desc:NSLocalizedString(@"TestHeroicSpiritDesc", nil) result:NSLocalizedString(@"TestHeroicSpiritResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"19" subject_index:@"1" subject_title:NSLocalizedString(@"WhyQuiet", nil) subject_answer1:NSLocalizedString(@"BecauseThinking", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"BecauseSad", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"BecauseWeary", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测真实的自己
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TrueSelf", nil) desc:NSLocalizedString(@"TrueSelfDesc", nil) result:NSLocalizedString(@"TrueSelfResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"20" subject_index:@"1" subject_title:NSLocalizedString(@"HurryGrow", nil) subject_answer1:NSLocalizedString(@"BecauseCurious", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"BecauseParent", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"BecauseOthers", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NoGrow", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你是傻傻的还是精明的
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"SillyShrewd", nil) desc:NSLocalizedString(@"SillyShrewdDesc", nil) result:NSLocalizedString(@"SillyShrewdResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"21" subject_index:@"1" subject_title:NSLocalizedString(@"BecausePastSmarting", nil) subject_answer1:NSLocalizedString(@"Always2", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyDo2", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"BasicallyNot2", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"BecauseMood", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的厚黑指数是多少
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"SchemeIndex", nil) desc:NSLocalizedString(@"SchemeIndexDesc", nil) result:NSLocalizedString(@"SchemeIndexResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"22" subject_index:@"1" subject_title:NSLocalizedString(@"ScoldOthers", nil) subject_answer1:NSLocalizedString(@"BasicallyNot3", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OftenDo2", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"OccasionallyDo3", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Never2", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你的心狠程度是多少
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"CrueltyDegree", nil) desc:NSLocalizedString(@"CrueltyDegreeDsc", nil) result:NSLocalizedString(@"CrueltyDegreeResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"23" subject_index:@"1" subject_title:NSLocalizedString(@"TVPlayTears", nil) subject_answer1:NSLocalizedString(@"Never", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyDo", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"OftenDo", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Always", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你是多情人还是薄情人
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"SusceptibleUngrateful", nil) desc:NSLocalizedString(@"SusceptibleUngratefulDesc", nil) result:NSLocalizedString(@"SusceptibleUngratefulResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"24" subject_index:@"1" subject_title:NSLocalizedString(@"CareOthers", nil) subject_answer1:NSLocalizedString(@"SureThing", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"BigProbably", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"SmallProbably", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"AbsolutelyNot", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测试你是否有一颗钢铁心
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TestIronHeart", nil) desc:NSLocalizedString(@"TestIronHeartDesc", nil) result:NSLocalizedString(@"TestIronHeartResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"25" subject_index:@"1" subject_title:NSLocalizedString(@"BearEntangle", nil) subject_answer1:NSLocalizedString(@"MustCan", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyNot", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"OftenNot", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"AbsolutelyNot2", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测试你是别人眼里的马后炮吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"BelatedEffort", nil) desc:NSLocalizedString(@"BelatedEffortDesc", nil) result:NSLocalizedString(@"BelatedEffortResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"26" subject_index:@"1" subject_title:NSLocalizedString(@"ExpectMeetSomeone", nil) subject_answer1:NSLocalizedString(@"Classmate2", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"FirstLover", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"NobleMan", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"NoOne", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测生活中你是那种说一不二的人吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"AuthorityFigures", nil) desc:NSLocalizedString(@"AuthorityFiguresDesc", nil) result:NSLocalizedString(@"AuthorityFiguresResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"27" subject_index:@"1" subject_title:NSLocalizedString(@"WhereCourage", nil) subject_answer1:NSLocalizedString(@"Ability", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"EconomicStrength", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"AroundPerson", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你是个容易被迷惑的人吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"EasyBeConfused", nil) desc:NSLocalizedString(@"EasyBeConfusedDesc", nil) result:NSLocalizedString(@"EasyBeConfusedResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"1" subject_title:NSLocalizedString(@"LifeTrouble", nil) subject_answer1:NSLocalizedString(@"OfCourseNot", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"OccasionallyCan", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"Indifferent", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测什么人能镇得住你
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"HoldStill", nil) desc:NSLocalizedString(@"HoldStillDesc", nil) result:NSLocalizedString(@"HoldStillResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"29" subject_index:@"1" subject_title:NSLocalizedString(@"SoulInfidelity", nil) subject_answer1:NSLocalizedString(@"MustNotAccept", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"PhysicalInfidelityNot", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"TogetherSoulInfidelity", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"HardToSay", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测在别人眼里你是伪君子吗
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"TestHypocrite", nil) desc:NSLocalizedString(@"TestHypocriteDesc", nil) result:NSLocalizedString(@"TestHypocriteResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"30" subject_index:@"1" subject_title:NSLocalizedString(@"SendPicturesDynamic", nil) subject_answer1:NSLocalizedString(@"SendTravelPhotos", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"SendBuyThings", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"SendCookedDishes", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"SendNoMakeupPhotos", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你为什么总是被否定
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"WhyBeDeny", nil) desc:NSLocalizedString(@"WhyBeDenyDesc", nil) result:NSLocalizedString(@"WhyBeDenyResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"31" subject_index:@"1" subject_title:NSLocalizedString(@"LifeNeedEncourage", nil) subject_answer1:NSLocalizedString(@"OfCourse", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"ShouldNeed", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"CanNot", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"CompleteNotNeed", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    // 测测你会因为什么而性格大变
    [databaseManager insertPsychologicalListTableWithTitle:NSLocalizedString(@"CharacterChange", nil) desc:NSLocalizedString(@"CharacterChangeDesc", nil) result:NSLocalizedString(@"CharacterChangeResult", nil)];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"32" subject_index:@"1" subject_title:NSLocalizedString(@"EncounterDangerPhone", nil) subject_answer1:NSLocalizedString(@"ToParent", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"ToPolice", nil) answer2_toindex:@"" subject_answer3:NSLocalizedString(@"ToFriend", nil) answer3_toindex:@"" subject_answer4:NSLocalizedString(@"Bewildered", nil) answer4_toindex:@"" result_type:@"1/2/3/4"];
    
    
    
    // 测测你内心的正气有几分
    /*
    [databaseManager insertPsychologicalListTableWithTitle:@"测测你内心的正气有几分" desc:@"一个内心充满正气的人，他们在遇到跟自己不相关的事情的时候，也是能够挺身而出的。因为那样能够帮助到别人，他们没有办法做到坐视不理的。" result:@"你内心正气有100分，你是一个正气百分百的人，你说话做事都是光明磊落的，从来不会担心别人抓你的小辫子或是在背后说你什么，因为你很有自信，觉得自己身正不怕影子歪。而且你并不会因为亲疏关系就有所偏袒，在你这里，对的就是对的，错的就是错的，没有什么好说的，大家按规矩行事。/你内心正气有80分，大多数时候，你还是挺正气的一个人。马路上看到有什么不文明的行为，你也是能够做到挺身而出的。你能够积极的去帮助那些柔弱的人，并且不要求任何的回报。但是在关乎家人的时候，你就会变得有点自私了，这时候的你是没有任何的正气的，一心就只为家人着想。/你内心正气50分，你内心正气50分。因为你的悟性是一阵一阵的。有时候你能够挺身而出去帮助别人，能够表现出自己比较善良的一面出来。但是有时候你又是特别自私自利的一个人，会经常为了自己的利益去损害他人的利益。你经常都是那种表面一套背后一套的，让人难以看清真面目。/你内心正气10分，你是一个内心毫无正气的人，你天天就想着怎么去占别人的便宜，怎么让自己的利益最大化。为了所谓的好处，你什么样恶劣的事情都做得出来，你会自我安慰说没有钱是万万不能的。因此你才不在乎别人怎么看待自己的，反正你觉得人不为己天诛地灭，你会随人家说去。"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"1" subject_title:@"你能够接受别人对你的批评吗？" subject_answer1:@"肯定可以的" answer1_toindex:@"2" subject_answer2:@"不可以" answer2_toindex:@"4" subject_answer3:@"看情况吧" answer3_toindex:@"3" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"2" subject_title:@"你每一次聚会都是提前走人还是最后一个走人？" subject_answer1:@"提前走人" answer1_toindex:@"4" subject_answer2:@"大家一起走" answer2_toindex:@"3" subject_answer3:@"最后一个走人" answer3_toindex:@"6" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"3" subject_title:@"你会经常上网去刷时事新闻吗？" subject_answer1:@"会的，每天都要刷新" answer1_toindex:@"4" subject_answer2:@"偶尔有空刷新" answer2_toindex:@"7" subject_answer3:@"基本没有时间" answer3_toindex:@"5" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"4" subject_title:@"你会时常都在觉得别人多管闲事吗？" subject_answer1:@"是的，经常这样认为" answer1_toindex:@"6" subject_answer2:@"很少这样认为" answer2_toindex:@"7" subject_answer3:@"偶尔会这样认为" answer3_toindex:@"8" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"5" subject_title:@"你觉得自己算是朋友很多的那种人吗？" subject_answer1:@"是的，朋友很多" answer1_toindex:@"7" subject_answer2:@"不多，但彼此很好" answer2_toindex:@"8" subject_answer3:@"朋友很少" answer3_toindex:@"6" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"6" subject_title:@"你每天晚上都会早早的就回家吗？" subject_answer1:@"当然了" answer1_toindex:@"9" subject_answer2:@"看情况" answer2_toindex:@"7" subject_answer3:@"基本都很晚回家" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"7" subject_title:@"你认为下面最愚蠢的事情是什么呢？" subject_answer1:@"跟老板吵架" answer1_toindex:@"8" subject_answer2:@"手无缚鸡之力去抓小偷" answer2_toindex:@"10" subject_answer3:@"当众说弱智的谎言" answer3_toindex:@"9" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"8" subject_title:@"你会永远都站在家里人这一边吗？" subject_answer1:NSLocalizedString(@"Can", nil) answer1_toindex:@"1002" subject_answer2:NSLocalizedString(@"NoCan", nil) answer2_toindex:@"9" subject_answer3:@"不好说" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@"2"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"9" subject_title:@"你会非常在意公司的人员晋升情况吗？" subject_answer1:@"会的，非常在意" answer1_toindex:@"1003" subject_answer2:@"不会很在意" answer2_toindex:@"1004" subject_answer3:@"不好说" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@"3/4"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"28" subject_index:@"10" subject_title:@"你是一个做事有规划的人吗？" subject_answer1:NSLocalizedString(@"Yes", nil) answer1_toindex:@"" subject_answer2:NSLocalizedString(@"No", nil) answer2_toindex:@"" subject_answer3:@"看情况" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"1/4/3"];
    */
    
    
    // 测测你为什么总是被否定
    /*
    [databaseManager insertPsychologicalListTableWithTitle:@"测你的个性是刚还是怂" desc:@"生活中，不同的人有不一样的个性，有的人个性很刚，有的人则是个性很怂，个性刚的人有自己的主见和原则，个性怂的人经常会被人拿捏在手里了。" result:@"你的个性是刚，你是那种很有原则的人，你坚持着自己的原则，没有人可以改变你，除了你自己。你的个性是刚的，大家都知道你是不好惹的，因此不敢随意的去招惹你。你是有勇气的人，你觉得不对的事情你一定会指出来的，其实你在生活中是得到别人尊重的。/你的个性是怂，你是那种很软弱的人，你的个性是怂的，你在生活中可没少让人欺负，即使是这样你也不敢反抗，很多时候身边的人都看不下去了。你应该好好的改变一下自己的个性，不能什么都让别人说了算的，生活是你自己的，你应该为自己做主才是，而不是听命于人。/你的个性有刚有怂，你是遇事会变通的人，有些时候你是刚的，有些时候你是怂的，大家对你的了解并没有办法全面，因为谁也不知道你的刚和怂会在什么时候出现了。有的人觉得你很狡猾，随时都在改变你的个性，确实你会因为自己的改变而让自己的生活减少了很多的麻烦。/你的个性居中，其实你是个性很好的，让人觉得比较好相处，你不会过于刚，也不至于怂，你把握好了分寸反而能够把自己的生活经营得很好。其实很多人喜欢和你来往，因为你有一个好的个性，大家觉得和你相处是很愉快的，并没有什么压力，你的好个性给你带来了好运。"];

    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"1" subject_title:@"你觉得个性是能够被改变的吗？" subject_answer1:@"当然能" answer1_toindex:@"2" subject_answer2:@"并不能" answer2_toindex:@"4" subject_answer3:@"不好说" answer3_toindex:@"3" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"2" subject_title:@"你觉得你的个性会被谁所改变呢？" subject_answer1:@"自己" answer1_toindex:@"4" subject_answer2:@"父母" answer2_toindex:@"3" subject_answer3:@"爱人" answer3_toindex:@"6" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"3" subject_title:@"你在生活中是那种容易受影响的人吗？" subject_answer1:@"非常容易" answer1_toindex:@"4" subject_answer2:@"还好" answer2_toindex:@"7" subject_answer3:@"并不受影响" answer3_toindex:@"5" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"4" subject_title:@"你的个性会给你带来麻烦吗？" subject_answer1:@"当然会" answer1_toindex:@"6" subject_answer2:@"并不会" answer2_toindex:@"7" subject_answer3:@"不好说" answer3_toindex:@"8" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"5" subject_title:@"你经常会对着身边的人发脾气吗？" subject_answer1:@"经常" answer1_toindex:@"7" subject_answer2:@"偶尔" answer2_toindex:@"8" subject_answer3:@"没有过" answer3_toindex:@"6" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"6" subject_title:@"你在什么时候会认输呢？" subject_answer1:@"任何时候" answer1_toindex:@"9" subject_answer2:@"技不如人的时候" answer2_toindex:@"7" subject_answer3:@"心情好的时候" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"7" subject_title:@"你被人打倒了还能够站起来吗？" subject_answer1:@"当然能" answer1_toindex:@"8" subject_answer2:@"应该能" answer2_toindex:@"10" subject_answer3:@"并不能" answer3_toindex:@"9" subject_answer4:@"" answer4_toindex:@"" result_type:@""];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"8" subject_title:@"你觉得个性的养成和什么有关系呢？" subject_answer1:@"遗传" answer1_toindex:@"9" subject_answer2:@"朋友" answer2_toindex:@"1002" subject_answer3:@"不好说" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@"2"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"9" subject_title:@"你会为了什么改变个性呢？" subject_answer1:@"金钱" answer1_toindex:@"1003" subject_answer2:@"爱情" answer2_toindex:@"1001" subject_answer3:@"亲情" answer3_toindex:@"10" subject_answer4:@"" answer4_toindex:@"" result_type:@"3/1"];
    
    [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"35" subject_index:@"10" subject_title:@"你不开心的时候会怎么做呢？" subject_answer1:@"睡一觉" answer1_toindex:@"" subject_answer2:@"大发脾气" answer2_toindex:@"" subject_answer3:@"使命的花钱" answer3_toindex:@"" subject_answer4:@"" answer4_toindex:@"" result_type:@"3/4/2"];
     
     
     // 测测你面对流言蜚语能够淡定如常吗
     [databaseManager insertPsychologicalListTableWithTitle:@"测测你面对流言蜚语能够淡定如常吗" desc:@"流言蜚语往往能够让一个人受到重创，有的人很坚强的去面对，有的人就逃避，有的人因为流言蜚语而寝食难安的，流言蜚语的杀伤力可不小。" result:@"你面对流言蜚语能够淡定如常，你是那种不容易受到影响的人，面对流言蜚语你并没有在害怕或者是担心，你觉得谣言止于智者。你是行的端坐得正的人，你并不担心别人说你什么，你觉得时间会让事实说话的。流言蜚语基本是不会给你带来伤害和影响的。/你面对流言蜚语会不知所措，面对流言蜚语的时候你并不知道自己该怎么做才好，你内心是很矛盾的，反抗或者是接受，你往往不知道该如何去做选择了。你的不知所措也代表了你的恐惧和慌张，越是这种时候你越要沉住气，否则是很容易被人所利用的。/你面对流言蜚语会寝食难安，听见流言蜚语的时候你会睡不着也吃不下，你会想很多，这样的话是严重的影响了你的生活的。你开始变得悲观起来，你在质疑自己、质疑别人、质疑生活。这样的状态下你的生活只会变得越来越不顺利而已，你需要调整情绪。/你面对流言蜚语暴跳如雷，你是脾气很暴躁的人，面对流言蜚语的时候你会变得更加的暴躁的，你听不得这些话，流言蜚语进了你的耳朵的时候，你开始失去了理智，冲动的想法和行为都会产生的。这种时候的你往往会做出更多错误的事情来，无法控制你自己。"];
     
     [databaseManager insertPsychologicalSubjectTableWithPsychological_id:@"32" subject_index:@"1" subject_title:@"你身边八卦的朋友有很多吗？" subject_answer1:@"很多" answer1_toindex:@"" subject_answer2:@"很少" answer2_toindex:@"" subject_answer3:@"一般" answer3_toindex:@"" subject_answer4:@"一个都没有" answer4_toindex:@"" result_type:@"1/2/3/4"];
    */
    
}


@end
