//
//  PalmsResultCircleMaskView.m
//  TimeMachine
//
//  Created by 叶庭文 on 2021/7/23.
//

#import "PalmsResultCircleMaskView.h"
#import "DrawManager.h"
#import "LanguageManager.h"

@interface PalmsResultCircleMaskView ()

@property (nonatomic, strong) UILabel *textLabel1;
@property (nonatomic, strong) UILabel *textLabel2;
@property (nonatomic, strong) UILabel *textLabel3;
@property (nonatomic, strong) UILabel *textLabel4;

@end

@implementation PalmsResultCircleMaskView


- (void)drawRect:(CGRect)rect
{
    // Drawing code
    
    DrawManager *drawManager = [[DrawManager alloc] init];
    
    CGContextRef contextRef = UIGraphicsGetCurrentContext();
    
    [drawManager drawline:contextRef startPoint:CGPointMake(self.bounds.size.width/2-10, 30) stopPoint:CGPointMake(self.bounds.size.width/2+10, 100) color:[MyColor colorWithHexString:@"#FEECBF" alpha:1.0] lineWidth:1.0 dash:NO];
    
    UIView *circlePoint1 = [[UIView alloc] initWithFrame:CGRectMake(self.bounds.size.width/2+10-2, 100-2, 4, 4)];
    circlePoint1.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
    circlePoint1.layer.cornerRadius = 2.0;
    [self addSubview:circlePoint1];
    
    [drawManager drawline:contextRef startPoint:CGPointMake(50, self.bounds.size.height/2+20) stopPoint:CGPointMake(98, self.bounds.size.height/2+10) color:[MyColor colorWithHexString:@"#FEECBF" alpha:1.0] lineWidth:1.0 dash:NO];
    
    UIView *circlePoint2 = [[UIView alloc] initWithFrame:CGRectMake(98-2, self.bounds.size.height/2+10-2, 4, 4)];
    circlePoint2.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
    circlePoint2.layer.cornerRadius = 2.0;
    [self addSubview:circlePoint2];
    
    [drawManager drawline:contextRef startPoint:CGPointMake(self.bounds.size.width-50, self.bounds.size.height/2+30) stopPoint:CGPointMake(self.bounds.size.width-110, self.bounds.size.height/2+20) color:[MyColor colorWithHexString:@"#FEECBF" alpha:1.0] lineWidth:1.0 dash:NO];
    
    UIView *circlePoint3 = [[UIView alloc] initWithFrame:CGRectMake(self.bounds.size.width-110-2, self.bounds.size.height/2+20-2, 4, 4)];
    circlePoint3.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
    circlePoint3.layer.cornerRadius = 2.0;
    [self addSubview:circlePoint3];
    
    [drawManager drawline:contextRef startPoint:CGPointMake(120, self.bounds.size.height-44) stopPoint:CGPointMake(150, self.bounds.size.height-90) color:[MyColor colorWithHexString:@"#FEECBF" alpha:1.0] lineWidth:1.0 dash:NO];
    
    UIView *circlePoint4 = [[UIView alloc] initWithFrame:CGRectMake(150-2, self.bounds.size.height-90-2, 4, 4)];
    circlePoint4.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
    circlePoint4.layer.cornerRadius = 2.0;
    [self addSubview:circlePoint4];
}

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
        
        BOOL isLanguageZH = [LanguageManager getCurrentLanguageIsZH];
        if (self.textLabel1 == nil) {
            self.textLabel1 = [[UILabel alloc] init];
            self.textLabel1.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
            self.textLabel1.textColor = [MyColor colorWithHexString:@"#683F24" alpha:1.0];
            self.textLabel1.textAlignment = NSTextAlignmentCenter;
            self.textLabel1.font = [UIFont systemFontOfSize:12.0];
            self.textLabel1.layer.cornerRadius = 3.0;
            self.textLabel1.layer.masksToBounds = YES;
            [self addSubview:self.textLabel1];
            self.textLabel1.numberOfLines = 0;
            if (isLanguageZH == YES) {
                [self.textLabel1 setFrame:CGRectMake(self.bounds.size.width/2-30, 20, 68, 22)];
            } else {
                [self.textLabel1 setFrame:CGRectMake(self.bounds.size.width/2-30, 20, 68, 30)];
            }
        }
        
        if (self.textLabel2 == nil) {
            self.textLabel2 = [[UILabel alloc] init];
            self.textLabel2.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
            self.textLabel2.textColor = [MyColor colorWithHexString:@"#683F24" alpha:1.0];
            self.textLabel2.textAlignment = NSTextAlignmentCenter;
            self.textLabel2.font = [UIFont systemFontOfSize:12.0];
            self.textLabel2.layer.cornerRadius = 3.0;
            self.textLabel2.layer.masksToBounds = YES;
            [self addSubview:self.textLabel2];
            self.textLabel2.numberOfLines = 0;
            if (isLanguageZH == YES) {
                [self.textLabel2 setFrame:CGRectMake(0, self.bounds.size.height/2+20, 68, 22)];
            } else {
                [self.textLabel2 setFrame:CGRectMake(0, self.bounds.size.height/2+20, 68, 30)];
            }
        }
        
        if (self.textLabel3 == nil) {
            self.textLabel3 = [[UILabel alloc] init];
            self.textLabel3.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
            self.textLabel3.textColor = [MyColor colorWithHexString:@"#683F24" alpha:1.0];
            self.textLabel3.textAlignment = NSTextAlignmentCenter;
            self.textLabel3.font = [UIFont systemFontOfSize:12.0];
            self.textLabel3.layer.cornerRadius = 3.0;
            self.textLabel3.layer.masksToBounds = YES;
            [self addSubview:self.textLabel3];
            self.textLabel3.numberOfLines = 0;
            if (isLanguageZH == YES) {
                [self.textLabel3 setFrame:CGRectMake(self.bounds.size.width-70, self.bounds.size.height/2+30, 68, 22)];
            } else {
                [self.textLabel3 setFrame:CGRectMake(self.bounds.size.width-70, self.bounds.size.height/2+30, 68, 30)];
            }
        }
        
        if (self.textLabel4 == nil) {
            self.textLabel4 = [[UILabel alloc] init];
            self.textLabel4.backgroundColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0];
            self.textLabel4.textColor = [MyColor colorWithHexString:@"#683F24" alpha:1.0];
            self.textLabel4.textAlignment = NSTextAlignmentCenter;
            self.textLabel4.font = [UIFont systemFontOfSize:12.0];
            self.textLabel4.layer.cornerRadius = 3.0;
            self.textLabel4.layer.masksToBounds = YES;
            [self addSubview:self.textLabel4];
            self.textLabel4.numberOfLines = 0;
            if (isLanguageZH == YES) {
                [self.textLabel4 setFrame:CGRectMake(90, self.bounds.size.height-44, 68, 22)];
            } else {
                [self.textLabel4 setFrame:CGRectMake(90, self.bounds.size.height-44, 68, 30)];
            }
        }
    }
    return self;
}

- (void)setLabel1:(NSString *)label1
{
    self.textLabel1.text = label1;
}

- (void)setLabel2:(NSString *)label2
{
    self.textLabel2.text = label2;
}

- (void)setLabel3:(NSString *)label3
{
    self.textLabel3.text = label3;
}

- (void)setLabel4:(NSString *)label4
{
    self.textLabel4.text = label4;
}



@end
