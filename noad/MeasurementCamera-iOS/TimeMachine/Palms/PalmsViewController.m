//
//  PalmsViewController.m
//  TimeMachine
//
//  Created by 叶庭文 on 2021/7/16.
//

#import "PalmsViewController.h"
#import "PalmsUnscrambleVC.h"
#import "PalmsImagePickerVC.h"
#import "UIImage+FixOrientation.h"
#import "MyImagePickerResultVC.h"
#import "ImageManager.h"
#import "UpgradeVIPViewController.h"
#import "TMZhanbuController.h"
@interface PalmsViewController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate>

@property (nonatomic, strong) UILabel *labelTips;
@property (nonatomic, strong) UIImageView *palmsImageView;
@property (nonatomic, strong) UIImage *photoImage;
@property (nonatomic, strong) UIButton *unscrambleButton;

@end

@implementation PalmsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isTopButton = NO;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    self.imageBox.image = [UIImage imageNamed:@"手相背景"];
    if (self.isIPad == YES) {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 120, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        } else {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 100, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        }
    } else {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
    
    if (self.addButton == nil) {
        self.addButton = [[UIButton alloc] init];
        [self.addButton setImage:[UIImage imageNamed:@"添加"] forState:UIControlStateNormal];
        [self.addButton addTarget:self action:@selector(addPhotoButtonClick:) forControlEvents:UIControlEventTouchUpInside];
        [self.imageBox addSubview:self.addButton];
        [self.addButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(10);
            make.centerY.equalTo(self.imageBox.mas_centerY).with.offset(40);
            make.width.equalTo(@100);
            make.height.equalTo(@100);
        }];
    }
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"PalmReadingTips1", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-66);
                make.width.equalTo(@220);
            }];
        }
    }
    
    if (self.palmsImageView == nil) {
        self.palmsImageView = [[UIImageView alloc] init];
        self.palmsImageView.layer.cornerRadius = 8.0;
        self.palmsImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.palmsImageView.clipsToBounds = true;
        self.palmsImageView.alpha = 0;
        [self.imageBox addSubview:self.palmsImageView];
        [_palmsImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.imageBox.mas_top).with.offset(70);
            make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-130);
            make.leading.equalTo(self.imageBox.mas_leading).with.offset(40);
            make.trailing.equalTo(self.imageBox.mas_trailing).with.offset(-40);
        }];
    }
    
    if (self.unscrambleButton == nil) {
        self.unscrambleButton = [[UIButton alloc] init];
        [self.unscrambleButton setTitle:NSLocalizedString(@"StartReading", nil) forState:UIControlStateNormal];
        [self.unscrambleButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        [self.unscrambleButton.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
        self.unscrambleButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
        self.unscrambleButton.layer.cornerRadius = 20;
        self.unscrambleButton.userInteractionEnabled = NO;
        [self.view addSubview:self.unscrambleButton];
        [self.unscrambleButton setFrame:CGRectMake((self.view.bounds.size.width-160)/2, self.imageBox.frame.origin.y+self.imageBox.bounds.size.height+40, 160, 40)];
        [self.unscrambleButton addTarget:self action:@selector(unscrambleButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    }
}

- (void)unscrambleButtonClick:(UIButton *)sender
{
    if([APPMakeStoreIAPManager featureVip]!=YES)
    {
        UpgradeVIPViewController *vc = [[UpgradeVIPViewController alloc] init];
        vc.from = @"手相";
        [self.navigationController pushViewController:vc animated:YES];
        return;
    }
    TMZhanbuController *vc = [[TMZhanbuController alloc] init];
    vc.seletImage = _photoImage;
    vc.type =  1;
    [self.navigationController pushViewController:vc animated:YES];
}


- (void)addPhotoButtonClick:(UIButton *)sender
{
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    [self presentImagePickerController];
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"CameraPowerTips1", nil) message:NSLocalizedString(@"CameraPowerTips2", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        [self presentImagePickerController];
    }
}

- (void)presentImagePickerController
{
    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        PalmsImagePickerVC *vc = [[PalmsImagePickerVC alloc] init];
        vc.delegate = self; //delegate遵循了两个代理
        vc.allowsEditing = NO;
        vc.sourceType = UIImagePickerControllerSourceTypeCamera;
        vc.cameraDevice = UIImagePickerControllerCameraDeviceRear;
        vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
        vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
        [self presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.openAlbum = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                UIImage *photoImage = [ImageManager compressImage:image maxSize:1800];
                
                self.imageBox.image = [UIImage imageNamed:@"无内容背景框"];
                self.addButton.alpha = 0;
                
                self.palmsImageView.alpha = 1;
                self.palmsImageView.image = photoImage;
                
                self.photoImage = photoImage;
                
                self.unscrambleButton.userInteractionEnabled = YES;
                self.unscrambleButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
            }];
        };
    }
}


#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    [picker dismissViewControllerAnimated:YES completion:^{
        UIImage *photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
        if (photoImage) {
            photoImage = [photoImage fixOrientation];
            //NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
            //photoImage = [ImageManager compressImage:photoImage maxSize:1800];
            
            self.imageBox.image = [UIImage imageNamed:@"无内容背景框"];
            self.addButton.alpha = 0;
            
            self.palmsImageView.alpha = 1;
            self.palmsImageView.image = photoImage;
            
            self.unscrambleButton.userInteractionEnabled = YES;
            self.unscrambleButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
            
            self->_photoImage = photoImage;
        }
    }];
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:^{
            
    }];
}
@end
