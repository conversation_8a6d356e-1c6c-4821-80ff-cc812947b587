//
//  PalmsResultVC.m
//  TimeMachine
//
//  Created by 叶庭文 on 2021/7/16.
//

#import "PalmsResultVC.h"
#import "AWPolygonView.h"
#import "StarsView.h"
#import "DrawManager.h"
#import "PalmsResultCircleMaskView.h"
#import "ImageTextButton.h"
#import "ImageManager.h"
#import "FileManager.h"
#import "TimeTool.h"

@interface PalmsResultVC ()

@property (nonatomic, strong) UIScrollView *scrollView;

@end

@implementation PalmsResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [self initBgImgView];
    
    if (self.scrollView == nil) {
        self.scrollView = [[UIScrollView alloc] init];
        self.scrollView.showsVerticalScrollIndicator = NO;
        self.scrollView.showsHorizontalScrollIndicator = NO;
        self.scrollView.bounces = NO;
        self.scrollView.backgroundColor = [UIColor clearColor];
        [self.view addSubview:self.scrollView];
        if (self.isIPad) {
            [self.scrollView setFrame:CGRectMake(0, kStatusBarHeight, kScreenWidth, kScreenHeight-kStatusBarHeight-kBottomSafeHeight-110)];
        } else {
            [self.scrollView setFrame:CGRectMake(0, kStatusBarHeight, kScreenWidth, kScreenHeight-kStatusBarHeight-kBottomSafeHeight-80)];
        }
    }
    
    [self initBackButton];
    
    [self initViews];
}

- (void)initViews
{
    if (self.scrollView) {
        UIView *circleView = [[UIView alloc] initWithFrame:CGRectMake((kScreenWidth-240)/2, 80, 240, 240)];
        circleView.backgroundColor = [UIColor clearColor];
        circleView.layer.cornerRadius = 120;
        circleView.layer.masksToBounds = YES;
        circleView.layer.borderColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0].CGColor;
        circleView.layer.borderWidth = 2.0;
        [_scrollView addSubview:circleView];
        
        UIImageView *palmsImageView = [[UIImageView alloc] init];
        palmsImageView.layer.cornerRadius = 8.0;
        palmsImageView.contentMode = UIViewContentModeScaleAspectFit;
        //palmsImageView.clipsToBounds = true;
        palmsImageView.image = _palmsImage;
        [circleView addSubview:palmsImageView];
        [palmsImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(circleView.mas_centerX).with.offset(0);
            make.centerY.equalTo(circleView.mas_centerY).with.offset(0);
            make.width.equalTo(@220);
            make.height.equalTo(@240);
        }];
        
        PalmsResultCircleMaskView *circleMaskView = [[PalmsResultCircleMaskView alloc] initWithFrame:CGRectMake((kScreenWidth-300)/2, 50, 300, 300)];
        [_scrollView addSubview:circleMaskView];
        circleMaskView.label1 = _label1;
        circleMaskView.label2 = _label2;
        circleMaskView.label3 = _label3;
        circleMaskView.label4 = _label4;
        
        UIView *bgBoxView = [[UIView alloc] initWithFrame:CGRectMake(25, circleView.frame.origin.y+circleView.bounds.size.height+44, _scrollView.bounds.size.width-50, 500)];
        bgBoxView.backgroundColor = [MyColor colorWithHexString:@"#FEF7D9" alpha:1.0];
        bgBoxView.layer.cornerRadius = 10.0;
        bgBoxView.layer.masksToBounds = YES;
        bgBoxView.layer.borderWidth = 4.0;
        bgBoxView.layer.borderColor = [MyColor colorWithHexString:@"#FAEABF" alpha:1.0].CGColor;
        [_scrollView addSubview:bgBoxView];
        
        // 手掌分析
        UILabel *palmsUnscrambLabel = [[UILabel alloc] init];
        palmsUnscrambLabel.text = NSLocalizedString(@"PalmAnalysis", nil);
        palmsUnscrambLabel.textColor = [MyColor colorWithHexString:@"#DE842E" alpha:1.0];
        palmsUnscrambLabel.font = [UIFont systemFontOfSize:18.0];
        palmsUnscrambLabel.textAlignment = NSTextAlignmentCenter;
        [bgBoxView addSubview:palmsUnscrambLabel];
        [palmsUnscrambLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            //make.centerX.equalTo(bgBoxView.mas_centerX).with.offset(0);
            make.top.equalTo(bgBoxView.mas_top).with.offset(20);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.trailing.equalTo(bgBoxView.mas_trailing).with.offset(-20);
            make.height.equalTo(@20);
        }];
        
        AWPolygonView *polygonView = [[AWPolygonView alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2-64, 100, 128, 131)];
        polygonView.values = @[@(_wealth * 0.2), @(_cause * 0.2), @(_healthy * 0.2), @(_interpersonal * 0.2), @(_family * 0.2), @(_love * 0.2)];
        [bgBoxView addSubview:polygonView];
        
        UIImageView *chartImageView = [[UIImageView alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2-64, 100, 128, 131)];
        //UIImageView *chartImageView = [[UIImageView alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2-65, 100, 130, 130)];
        chartImageView.image = [UIImage imageNamed:@"手相六边形底图"];
        chartImageView.contentMode = UIViewContentModeScaleAspectFit;
        [bgBoxView addSubview:chartImageView];
        
        NSString *wealthString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Wealth", nil), _wealth];
        UILabel *wealthLabel = [[UILabel alloc] init];
        [wealthLabel setText:wealthString];
        [wealthLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [wealthLabel setTextAlignment:NSTextAlignmentCenter];
        [wealthLabel setFont:[UIFont systemFontOfSize:13.0]];
        [wealthLabel setNumberOfLines:2];
        [bgBoxView addSubview:wealthLabel];
        [wealthLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(chartImageView.mas_top).with.offset(0);
            make.centerX.equalTo(chartImageView.mas_centerX).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSString *causeString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Cause", nil), _cause];
        UILabel *causeLabel = [[UILabel alloc] init];
        [causeLabel setText:causeString];
        [causeLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [causeLabel setTextAlignment:NSTextAlignmentCenter];
        [causeLabel setFont:[UIFont systemFontOfSize:13.0]];
        [causeLabel setNumberOfLines:2];
        [bgBoxView addSubview:causeLabel];
        [causeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(chartImageView.mas_top).with.offset(8);
            make.trailing.equalTo(chartImageView.mas_leading).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSString *healthyString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Healthy", nil), _healthy];
        UILabel *healthyLabel = [[UILabel alloc] init];
        [healthyLabel setText:healthyString];
        [healthyLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [healthyLabel setTextAlignment:NSTextAlignmentCenter];
        [healthyLabel setFont:[UIFont systemFontOfSize:13.0]];
        [healthyLabel setNumberOfLines:2];
        [bgBoxView addSubview:healthyLabel];
        [healthyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(chartImageView.mas_top).with.offset(88);
            make.trailing.equalTo(chartImageView.mas_leading).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSString *loveString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Love", nil), _love];
        UILabel *loveLabel = [[UILabel alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2+53, 110, 60, 38)];
        [loveLabel setText:loveString];
        [loveLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [loveLabel setTextAlignment:NSTextAlignmentCenter];
        [loveLabel setFont:[UIFont systemFontOfSize:13.0]];
        [loveLabel setNumberOfLines:2];
        [bgBoxView addSubview:loveLabel];
        [loveLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(chartImageView.mas_top).with.offset(8);
            make.leading.equalTo(chartImageView.mas_trailing).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSString *familyString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Family", nil), _family];
        UILabel *familyLabel = [[UILabel alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2+53, 180, 60, 38)];
        [familyLabel setText:familyString];
        [familyLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [familyLabel setTextAlignment:NSTextAlignmentCenter];
        [familyLabel setFont:[UIFont systemFontOfSize:13.0]];
        [familyLabel setNumberOfLines:2];
        [bgBoxView addSubview:familyLabel];
        [familyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(chartImageView.mas_top).with.offset(88);
            make.leading.equalTo(chartImageView.mas_trailing).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSString *interpersonalString = [NSString stringWithFormat:@"%@\n%.1f", NSLocalizedString(@"Interpersonal", nil), _interpersonal];
        UILabel *interpersonalLabel = [[UILabel alloc] initWithFrame:CGRectMake(bgBoxView.bounds.size.width/2-20, 230, 60, 38)];
        [interpersonalLabel setText:interpersonalString];
        [interpersonalLabel setTextColor:[MyColor colorWithHexString:@"#67573C" alpha:1.0]];
        [interpersonalLabel setTextAlignment:NSTextAlignmentCenter];
        [interpersonalLabel setFont:[UIFont systemFontOfSize:13.0]];
        [interpersonalLabel setNumberOfLines:2];
        [bgBoxView addSubview:interpersonalLabel];
        [interpersonalLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(chartImageView.mas_bottom).with.offset(0);
            make.centerX.equalTo(chartImageView.mas_centerX).with.offset(0);
            make.height.equalTo(@38);
        }];
        
        NSDictionary* dic0 = self.handPrintResult[0];
        NSArray* ar0 = [dic0 allValues][0];
        _feeling_content = [ar0 componentsJoinedByString:@" "];
        
        NSDictionary* dic1 = self.handPrintResult[1];
        NSArray* ar1 = [dic1 allValues][0];
        NSString* _life_content = [ar1 componentsJoinedByString:@" "];
        
        NSDictionary* dic2 = self.handPrintResult[2];
        NSArray* ar2 = [dic2 allValues][0];
        _work_content = [ar2 componentsJoinedByString:@" "];
        
        NSDictionary* dic3 = self.handPrintResult[3];
        NSArray* ar3 = [dic3 allValues][0];
        _personality_content = [ar3 componentsJoinedByString:@" "];
        
        
        // 性格
        UILabel *personalityLabel = [[UILabel alloc] init];
        [personalityLabel setText:NSLocalizedString(@"性格", nil)];
        [personalityLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [personalityLabel setTextAlignment:NSTextAlignmentLeft];
        [personalityLabel setFont:[UIFont systemFontOfSize:17.0]];
        [bgBoxView addSubview:personalityLabel];
        [personalityLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(interpersonalLabel.mas_bottom).with.offset(30);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@100);
            make.height.equalTo(@20);
        }];
        
        StarsView *personalityStartView = [[StarsView alloc] initWithStarSize:CGSizeMake(20, 20) space:5 numberOfStar:5];
        personalityStartView.score = _personality_score;
        //personalityStartView.frame = CGRectMake(60, 290, personalityStartView.frame.size.width, personalityStartView.frame.size.height);
        [bgBoxView addSubview:personalityStartView];
        [personalityStartView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(interpersonalLabel.mas_bottom).with.offset(30);
            make.leading.equalTo(personalityLabel.mas_trailing).with.offset(10);
            make.width.equalTo(@(bgBoxView.bounds.size.width-80));
            make.height.equalTo(@20);
        }];
        
        UILabel *personalityContentLabel = [[UILabel alloc] init];
        [personalityContentLabel setText:_personality_content];
        [personalityContentLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [personalityContentLabel setTextAlignment:NSTextAlignmentLeft];
        [personalityContentLabel setFont:[UIFont systemFontOfSize:13.0]];
        [personalityContentLabel setNumberOfLines:0];
        [bgBoxView addSubview:personalityContentLabel];
        [personalityContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(personalityLabel.mas_bottom).with.offset(12);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@(bgBoxView.bounds.size.width-40));
        }];
        
        // 感情
        UILabel *feelingLabel = [[UILabel alloc] init];
        [feelingLabel setText:NSLocalizedString(@"Feeling", nil)];
        [feelingLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [feelingLabel setTextAlignment:NSTextAlignmentLeft];
        [feelingLabel setFont:[UIFont systemFontOfSize:17.0]];
        [bgBoxView addSubview:feelingLabel];
        [feelingLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(personalityContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@100);
            make.height.equalTo(@20);
        }];
        
        StarsView *feelingStartView = [[StarsView alloc] initWithStarSize:CGSizeMake(20, 20) space:5 numberOfStar:5];
        feelingStartView.score = _feeling_score;
        //personalityStartView.frame = CGRectMake(60, 290, personalityStartView.frame.size.width, personalityStartView.frame.size.height);
        [bgBoxView addSubview:feelingStartView];
        [feelingStartView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(personalityContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(feelingLabel.mas_trailing).with.offset(10);
            make.width.equalTo(@(bgBoxView.bounds.size.width-80));
            make.height.equalTo(@20);
        }];
        
        UILabel *feelingContentLabel = [[UILabel alloc] init];
        [feelingContentLabel setText:_feeling_content];
        [feelingContentLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [feelingContentLabel setTextAlignment:NSTextAlignmentLeft];
        [feelingContentLabel setFont:[UIFont systemFontOfSize:13.0]];
        [feelingContentLabel setNumberOfLines:0];
        [bgBoxView addSubview:feelingContentLabel];
        [feelingContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(feelingLabel.mas_bottom).with.offset(12);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@(bgBoxView.bounds.size.width-40));
        }];
        
        // 工作
        UILabel *workLabel = [[UILabel alloc] init];
        [workLabel setText:NSLocalizedString(@"事业", nil)];
        [workLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [workLabel setTextAlignment:NSTextAlignmentLeft];
        [workLabel setFont:[UIFont systemFontOfSize:17.0]];
        [bgBoxView addSubview:workLabel];
        [workLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(feelingContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@100);
            make.height.equalTo(@20);
        }];
        
        StarsView *workStartView = [[StarsView alloc] initWithStarSize:CGSizeMake(20, 20) space:5 numberOfStar:5];
        workStartView.score = _work_score;
        //personalityStartView.frame = CGRectMake(60, 290, personalityStartView.frame.size.width, personalityStartView.frame.size.height);
        [bgBoxView addSubview:workStartView];
        [workStartView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(feelingContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(workLabel.mas_trailing).with.offset(10);
            make.width.equalTo(@(bgBoxView.bounds.size.width-80));
            make.height.equalTo(@20);
        }];
        
        UILabel *workContentLabel = [[UILabel alloc] init];
        [workContentLabel setText:_work_content];
        [workContentLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [workContentLabel setTextAlignment:NSTextAlignmentLeft];
        [workContentLabel setFont:[UIFont systemFontOfSize:13.0]];
        [workContentLabel setNumberOfLines:0];
        [bgBoxView addSubview:workContentLabel];
        [workContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(workLabel.mas_bottom).with.offset(12);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@(bgBoxView.bounds.size.width-40));
        }];
        
        // 生命
        UILabel *lifeLabel = [[UILabel alloc] init];
        [lifeLabel setText:NSLocalizedString(@"生命", nil)];
        [lifeLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [lifeLabel setTextAlignment:NSTextAlignmentLeft];
        [lifeLabel setFont:[UIFont systemFontOfSize:17.0]];
        [bgBoxView addSubview:lifeLabel];
        [lifeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(workContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@100);
            make.height.equalTo(@20);
        }];
        
        StarsView *lifeStartView = [[StarsView alloc] initWithStarSize:CGSizeMake(20, 20) space:5 numberOfStar:5];
        int iMinValue = 1;
        int iMaxValue = 20;
        CGFloat lifeScore = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
        
        lifeStartView.score = lifeScore;
        //personalityStartView.frame = CGRectMake(60, 290, personalityStartView.frame.size.width, personalityStartView.frame.size.height);
        [bgBoxView addSubview:lifeStartView];
        [lifeStartView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(workContentLabel.mas_bottom).with.offset(20);
            make.leading.equalTo(lifeLabel.mas_trailing).with.offset(10);
            make.width.equalTo(@(bgBoxView.bounds.size.width-80));
            make.height.equalTo(@20);
        }];
        
        UILabel *lifeContentLabel = [[UILabel alloc] init];
        [lifeContentLabel setText:_life_content];
        [lifeContentLabel setTextColor:[MyColor colorWithHexString:@"#683F24" alpha:1.0]];
        [lifeContentLabel setTextAlignment:NSTextAlignmentLeft];
        [lifeContentLabel setFont:[UIFont systemFontOfSize:13.0]];
        [lifeContentLabel setNumberOfLines:0];
        [bgBoxView addSubview:lifeContentLabel];
        [lifeContentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(lifeLabel.mas_bottom).with.offset(12);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@(bgBoxView.bounds.size.width-40));
        }];
        
        
        
        
        // ...
        UILabel *referResultLabel = [[UILabel alloc] init];
        [referResultLabel setText:NSLocalizedString(@"ResultReference", nil)];
        [referResultLabel setTextColor:[MyColor colorWithHexString:@"#D8CAA3" alpha:1.0]];
        [referResultLabel setTextAlignment:NSTextAlignmentLeft];
        [referResultLabel setFont:[UIFont systemFontOfSize:13.0]];
        [bgBoxView addSubview:referResultLabel];
        [referResultLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(lifeContentLabel.mas_bottom).with.offset(30);
            make.leading.equalTo(bgBoxView.mas_leading).with.offset(20);
            make.width.equalTo(@(bgBoxView.bounds.size.width-40));
        }];
        
        [_scrollView layoutIfNeeded];
        
        [bgBoxView setFrame:CGRectMake(25, circleView.frame.origin.y+circleView.bounds.size.height+44, _scrollView.bounds.size.width-50, referResultLabel.frame.origin.y+referResultLabel.bounds.size.height+20)];
        
        ImageTextButton *saveButton = [[ImageTextButton alloc] init];
        [saveButton setImage:[UIImage imageNamed:@"保存"] title:NSLocalizedString(@"Save", nil)];
        [self.view addSubview:saveButton];
        saveButton.buttonClick = ^(BOOL isClick) {
            NSLog(@"saveButton");
            
            UIImage *cutImage = [ImageManager cutImageFromView:bgBoxView];
            UIImageWriteToSavedPhotosAlbum(cutImage, self, @selector(image:didFinishSavingWithError:contextInfo:), NULL);
        };
        
        ImageTextButton *shareButton = [[ImageTextButton alloc] init];
        [shareButton setImage:[UIImage imageNamed:@"分享"] title:NSLocalizedString(@"Share", nil)];
        [self.view addSubview:shareButton];
        shareButton.buttonClick = ^(BOOL isClick) {
            NSLog(@"shareButton");
            [SVProgressHUD show];
            
            UIImage *cutImage = [ImageManager cutImageFromView:bgBoxView];
            NSData *imageData = UIImageJPEGRepresentation(cutImage, 0.5);
            NSString *imagePath = [[FileManager pathForDocumentsFileName:ShareImage] stringByAppendingPathComponent:[NSString stringWithFormat:@"photo%@.jpg", [TimeTool getCurrentTime]]];
            [imageData writeToFile:imagePath atomically:YES];
            //NSLog(@"imagePath == %@", imagePath);
            
            // 弹出分享框并进行分享
            NSArray *items = [NSArray arrayWithObjects:[NSURL fileURLWithPath:imagePath], nil];
            UIActivityViewController *activityViewController =
            [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
            activityViewController.excludedActivityTypes = @[UIActivityTypeAirDrop];
            //NSLog(@"lalala");
            // 分享之后的回调
            activityViewController.completionWithItemsHandler = ^(UIActivityType  _Nullable activityType, BOOL completed, NSArray * _Nullable returnedItems, NSError * _Nullable activityError) {
                if (completed) {
                    //NSLog(@"completed");
                } else if (activityError) {
                    //NSLog(@"activityError == %@", activityError.localizedDescription);
                }
            };
            if (self.isIPad) {
                activityViewController.popoverPresentationController.sourceView = self.view;
                activityViewController.popoverPresentationController.sourceRect = CGRectMake(self.view.frame.size.width/2.0, self.view.frame.size.height/2.0, 1, 1);
                activityViewController.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionUp;
            }
            [self presentViewController:activityViewController animated:YES completion:^{
                [SVProgressHUD dismiss];
            }];
        };
        if (self.isIPad == YES) {
            [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom).with.offset(-(36));
                make.centerX.equalTo(self.view.mas_centerX).with.offset(-65);
                make.width.equalTo(@50);
                make.height.equalTo(@66);
            }];
            
            [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom).with.offset(-(36));
                make.centerX.equalTo(self.view.mas_centerX).with.offset(65);
                make.width.equalTo(@50);
                make.height.equalTo(@66);
            }];
        } else {
            [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom).with.offset(-(kBottomSafeHeight+8));
                make.centerX.equalTo(self.view.mas_centerX).with.offset(-65);
                make.width.equalTo(@50);
                make.height.equalTo(@66);
            }];
            
            [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(self.view.mas_bottom).with.offset(-(kBottomSafeHeight+8));
                make.centerX.equalTo(self.view.mas_centerX).with.offset(65);
                make.width.equalTo(@50);
                make.height.equalTo(@66);
            }];
        }
        
        [_scrollView setContentSize:CGSizeMake(kScreenWidth, bgBoxView.frame.origin.y+bgBoxView.bounds.size.height+50)];
    }
}

- (void)backButtonClick:(UIButton *)sender
{
    [self.navigationController popToRootViewControllerAnimated:YES];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    if(error != NULL) {
        [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"PictureSaveFailedTips", nil)];
    } else {
        [SVProgressHUD showSuccessWithStatus:NSLocalizedString(@"PictureSaveSucceedTips", nil)];
    }
}


@end
