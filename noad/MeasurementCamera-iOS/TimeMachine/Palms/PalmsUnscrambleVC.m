//
//  PalmsUnscrambleVC.m
//  TimeMachine
//
//  Created by 叶庭文 on 2021/7/16.
//

#import "PalmsUnscrambleVC.h"
#import "PalmsResultVC.h"
#import "DatabaseManager.h"

@interface PalmsUnscrambleVC ()

@property (nonatomic, strong) UILabel *labelTips;

@property (nonatomic, strong) UIImageView *palmsImageView;

@property (nonatomic, strong) NSString *label1;
@property (nonatomic, strong) NSString *label2;
@property (nonatomic, strong) NSString *label3;
@property (nonatomic, strong) NSString *label4;
@property (nonatomic) CGFloat wealth;  // 财富
@property (nonatomic) CGFloat cause;  // 事业
@property (nonatomic) CGFloat healthy;  // 健康
@property (nonatomic) CGFloat interpersonal;  // 交际
@property (nonatomic) CGFloat family;  // 家庭
@property (nonatomic) CGFloat love;  // 爱情
@property (nonatomic) CGFloat personality_score;  //个性评分
@property (nonatomic, strong) NSString *personality_content;  //个性内容
@property (nonatomic) CGFloat feeling_score;  //感情评分
@property (nonatomic, strong) NSString *feeling_content;  //感情内容
@property (nonatomic) CGFloat work_score;  //工作评分
@property (nonatomic, strong) NSString *work_content;  //工作内容

@end

@implementation PalmsUnscrambleVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isTopButton = NO;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    self.imageBox.image = [UIImage imageNamed:@"无内容背景框"];
    if (self.isIPad == YES) {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 120, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        } else {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 100, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        }
    } else {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"PalmReadingTips2", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-66);
                make.width.equalTo(@220);
            }];
        }
    }
    
    if (self.palmsImageView == nil) {
        self.palmsImageView = [[UIImageView alloc] init];
        self.palmsImageView.layer.cornerRadius = 8.0;
        self.palmsImageView.contentMode = UIViewContentModeScaleAspectFill;
        self.palmsImageView.clipsToBounds = true;
        self.palmsImageView.image = _palmsImage;
        [self.imageBox addSubview:self.palmsImageView];
        [_palmsImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.imageBox.mas_top).with.offset(70);
            make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-130);
            make.leading.equalTo(self.imageBox.mas_leading).with.offset(40);
            make.trailing.equalTo(self.imageBox.mas_trailing).with.offset(-40);
        }];
    }
    
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    NSArray *palmsArray = [databaseManager obtainPalmsTable];
    
    int iMaxArrayCount = (int)palmsArray.count-1;
    //NSLog(@"iMaxArrayCount == %d", iMaxArrayCount);
    
    _label1 = palmsArray[(arc4random() % iMaxArrayCount)][@"label1"];
    _label2 = palmsArray[(arc4random() % iMaxArrayCount)][@"label2"];
    _label3 = palmsArray[(arc4random() % iMaxArrayCount)][@"label3"];
    _label4 = palmsArray[(arc4random() % iMaxArrayCount)][@"label4"];
    //NSLog(@"label1 == %@", _label1);
    
    int iMinValue = 1;
    int iMaxValue = 20;
    _wealth = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _cause = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _healthy = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _interpersonal = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _family = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _love = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    
    _personality_score = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _feeling_score = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    _work_score = 3 + (iMinValue + (arc4random() % iMaxValue)) * 0.1;
    
    _personality_content = palmsArray[(arc4random() % iMaxArrayCount)][@"personality_content"];
    _feeling_content = palmsArray[(arc4random() % iMaxArrayCount)][@"feeling_content"];
    _work_content = palmsArray[(arc4random() % iMaxArrayCount)][@"work_content"];
    

    [self performSelector:@selector(pushPalmsResultVC) withObject:nil afterDelay:2.0];
}

- (void)pushPalmsResultVC
{
    PalmsResultVC *vc = [[PalmsResultVC alloc] init];
    vc.label1 = _label1;
    vc.label2 = _label2;
    vc.label3 = _label3;
    vc.label4 = _label4;
    vc.wealth = _wealth;
    vc.cause = _cause;
    vc.healthy = _healthy;
    vc.interpersonal = _interpersonal;
    vc.family = _family;
    vc.love = _love;
    vc.personality_score = _personality_score;
    vc.personality_content = _personality_content;
    vc.feeling_score = _feeling_score;
    vc.feeling_content = _feeling_content;
    vc.work_score = _work_score;
    vc.work_content = _work_content;
    vc.palmsImage = _palmsImage;
    vc.handPrintResult = self.handprintResult;
    [self.navigationController pushViewController:vc animated:YES];
}


@end
