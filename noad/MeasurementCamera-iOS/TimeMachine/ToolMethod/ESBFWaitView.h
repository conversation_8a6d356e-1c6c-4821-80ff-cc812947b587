//
//  BFWaitView.h
//  Eraser
//
//  Created by biggerlens on 2021/1/23.
//  Copyright © 2021 BiggerLensStore. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface ESBFWaitView : NSObject
/// 共享单例对象
+ (instancetype)share;
/// 销毁[等待动画控件]单例
+ (void)destroySingleton;
/// 封锁init方法,不允许外界通过init进行初始化
- (instancetype)init NS_UNAVAILABLE;
/// 加载等待动画的类型枚举
typedef NS_ENUM(NSInteger, BKWaitViewType) {
    BKWhiteType,///<黑色背景[白色]图标动画
    BKGrayType,///<白色背景[灰色]图标动画
};
/// 加载等待动画
/// @param targetView 目标视图
- (void)addDarkCurtainView:(UIView*)targetView;
/// 加载等待动画
/// @param targetView 目标视图
/// @param type 动画类型
- (void)addDarkCurtainView:(UIView*)targetView type:(BKWaitViewType)type;
/// 移除等待动画
- (void)removeDarkCurtainView;
@end

NS_ASSUME_NONNULL_END
