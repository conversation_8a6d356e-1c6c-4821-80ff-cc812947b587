//
//  BFWaitView.m
//  Eraser
//
//  Created by biggerlens on 2021/1/23.
//  Copyright © 2021 BiggerLensStore. All rights reserved.
//

#import "ESBFWaitView.h"
@interface ESBFWaitView ()
/// 目标视图
@property (nonatomic, strong) UIView *targetView;
/// 当前控制器交互的黑暗幕布
@property (nonatomic, strong) UIView *darkCurtainView;
/// 等待动画视图
@property (nonatomic, strong) UIActivityIndicatorView *waitIndicatorView;
@end
@implementation ESBFWaitView
static ESBFWaitView *singleton = nil;
static dispatch_once_t onceToken;
/// 共享单例对象
+ (instancetype)share{
    dispatch_once(&onceToken, ^{
        singleton = [[ESBFWaitView alloc] init];
    });
    return singleton;
}
/// 销毁[等待动画控件]单例
+ (void)destroySingleton{
    singleton = nil;
    onceToken = 0L;
}

/// 加载等待动画
/// @param targetView 目标视图
- (void)addDarkCurtainView:(UIView*)targetView{
    if(self.targetView != nil){
        [self removeDarkCurtainView];
    }
    
    self.targetView = targetView;
    self.targetView.userInteractionEnabled = NO;
    [self.darkCurtainView addSubview:self.waitIndicatorView];
    [self.targetView addSubview:self.darkCurtainView];
    [self.waitIndicatorView startAnimating];
}

/// 加载等待动画
/// @param targetView 目标视图
/// @param type 动画类型
- (void)addDarkCurtainView:(UIView*)targetView type:(BKWaitViewType)type{
    if(self.targetView != nil){
        [self removeDarkCurtainView];
    }
    
    if (type == BKGrayType) {
        self.waitIndicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleGray];
        self.waitIndicatorView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
        self.waitIndicatorView.backgroundColor = [UIColor clearColor];
        self.darkCurtainView.backgroundColor = UIColor.whiteColor;
    }
    
    self.targetView = targetView;
    self.targetView.userInteractionEnabled = NO;
    [self.targetView addSubview:self.darkCurtainView];
    [self.waitIndicatorView startAnimating];
}

/// 移除等待动画
- (void)removeDarkCurtainView{
    if(self.waitIndicatorView.animating){
        [self.waitIndicatorView stopAnimating];
    }
    
    if(self.darkCurtainView.superview != nil){
        [self.waitIndicatorView removeFromSuperview];
        [self.darkCurtainView removeFromSuperview];
        self.waitIndicatorView = nil;
    }
    
    if (self.targetView != nil) {
        self.targetView.userInteractionEnabled = YES;
        self.targetView = nil;
    }
}

#pragma mark - ================== Get和Set方法 start ================
-(UIView *)darkCurtainView{
    if (!_darkCurtainView) {
        _darkCurtainView = [[UIView alloc] init];
        _darkCurtainView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
        _darkCurtainView.backgroundColor = UIColor.blackColor;
        _darkCurtainView.alpha = 0.5;
    }
    return _darkCurtainView;
}
    
-(UIActivityIndicatorView *)waitIndicatorView{
    if (!_waitIndicatorView) {
        _waitIndicatorView = [[UIActivityIndicatorView alloc] initWithActivityIndicatorStyle:UIActivityIndicatorViewStyleWhiteLarge];
        _waitIndicatorView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
        _waitIndicatorView.backgroundColor = [UIColor clearColor];
    }
    return _waitIndicatorView;
}
#pragma mark ================== Get和Set方法 end ==================
@end
