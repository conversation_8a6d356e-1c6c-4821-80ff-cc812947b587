//
//  BaseViewController.h
//  TimeMachine
//
//  Created by FS003 on 2021/7/6.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface BaseViewController : UIViewController

// 背景框框上面是否还有按钮
@property (nonatomic) BOOL isTopButton;
// 背景框框
@property (nonatomic, strong) UIImageView *imageBox;
// 添加按钮
@property (nonatomic, strong) UIButton *addButton;
// 人物头像
@property (nonatomic, strong) UIImageView *avatarImageView;

@property (nonatomic) BOOL isIPad;   // 判断是不是ipad

@property (nonatomic) BOOL isLanguageZH;   // 判断当前语言是不是中文
@property UILabel* tiltleLabel;

@property UIButton* backButton;
// 初始化背景图片
- (void)initBgImgView;

// 初始化返回按钮
- (void)initBackButton;

// 初始化背景框框图片
- (void)initImageBox;

// 初始化添加按钮
- (void)initAddButtonAndAvatar;

- (void)creatTitleLabel:(NSString*)label;

- (void)backButtonClick:(UIButton *)sender;

- (void)addPhotoButtonClick:(UIButton *)sender;

- (BOOL)getIsIPad;
- (BOOL)getCurrentLanguageIsZH;
- (void)showFullscreenVideoAd;
- (void)showBannerView:(CGRect)frame size:(CGSize)size;
@end

NS_ASSUME_NONNULL_END
