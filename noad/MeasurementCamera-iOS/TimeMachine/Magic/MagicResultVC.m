//
//  MagicResultVC.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "MagicResultVC.h"
#import "ImageTextButton.h"
#import "FileManager.h"
#import "TimeTool.h"
#import "ImageManager.h"
#import "VisionImageView.h"
#import "MagicChangeFaceView.h"
#import "FacePointsTool.h"

@interface MagicResultVC ()

@property (nonatomic, strong) UILabel *labelTips;
@property (nonatomic, strong) MagicChangeFaceView *magicChangeFaceView;

@end

@implementation MagicResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    [SVProgressHUD showWithStatus:NSLocalizedString(@"MagicFaceChangeTips6", nil)];
    
    self.isTopButton = YES;
    
    self.magicChangeFaceView = [[MagicChangeFaceView alloc] initWithFrame:CGRectMake(0, 100, kScreenWidth, kScreenHeight-160)];
    [self.view addSubview:self.magicChangeFaceView];
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    if (self.isIPad == YES) {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 120, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        } else {
            [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 100, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
        }
    } else {
        if (self.isTopButton == YES) {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+70 orStrangeValue:kStatusBarHeight+50+80], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        } else {
            [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+40 orStrangeValue:kStatusBarHeight+50+50], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
        }
    }
    
    [self initAddButtonAndAvatar];
    self.addButton.alpha = 0;
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"MagicFaceChangeTips6", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-66);
                make.width.equalTo(@220);
            }];
        }
    }
    
    // 重新选择照片按钮
    UIButton *reselectImageButton = [[UIButton alloc] init];
    reselectImageButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
    [reselectImageButton setTitle:NSLocalizedString(@"ReselectPhoto", nil) forState:UIControlStateNormal];
    [reselectImageButton.titleLabel setFont:[UIFont systemFontOfSize:14.0]];
    [reselectImageButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [reselectImageButton addTarget:self action:@selector(reselectImageButtonClick:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:reselectImageButton];
    [reselectImageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.view.mas_trailing).with.offset(0);
        make.bottom.equalTo(self.imageBox.mas_top).with.offset(-[iPhoneXTool setCommonValue:20 orStrangeValue:25]);
        make.width.equalTo(@120);
        make.height.equalTo(@40);
    }];
    
    [self.view layoutIfNeeded];
    
    if (reselectImageButton) {
        UIBezierPath *maskPath = [UIBezierPath bezierPathWithRoundedRect:reselectImageButton.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerBottomLeft cornerRadii:CGSizeMake(20, 20)];
        CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
        maskLayer.path = maskPath.CGPath;
        reselectImageButton.layer.masksToBounds = YES;
        reselectImageButton.layer.mask = maskLayer;
    }
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    
    [self startMagicChangeFace];
}

- (void)initViews
{
    ImageTextButton *saveButton = [[ImageTextButton alloc] init];
    [saveButton setImage:[UIImage imageNamed:@"保存"] title:NSLocalizedString(@"Save", nil)];
    [self.view addSubview:saveButton];
    saveButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"saveButton");
        [self showFullscreenVideoAd];
        UIImageWriteToSavedPhotosAlbum(self.magicChangeFaceView.resultImage, self, @selector(image:didFinishSavingWithError:contextInfo:), NULL);
    };
    
    ImageTextButton *shareButton = [[ImageTextButton alloc] init];
    [shareButton setImage:[UIImage imageNamed:@"分享"] title:NSLocalizedString(@"Share", nil)];
    [self.view addSubview:shareButton];
    shareButton.buttonClick = ^(BOOL isClick) {
        NSLog(@"shareButton");
        [SVProgressHUD show];
        
        NSData *imageData = UIImageJPEGRepresentation(self.magicChangeFaceView.resultImage, 1.0);
        NSString *imagePath = [[FileManager pathForDocumentsFileName:ShareImage] stringByAppendingPathComponent:[NSString stringWithFormat:@"photo%@.jpg", [TimeTool getCurrentTime]]];
        [imageData writeToFile:imagePath atomically:YES];
        NSLog(@"imagePath == %@", imagePath);
        
        // 弹出分享框并进行分享
        NSArray *items = [NSArray arrayWithObjects:[NSURL fileURLWithPath:imagePath], nil];
        UIActivityViewController *activityViewController =
        [[UIActivityViewController alloc] initWithActivityItems:items applicationActivities:nil];
        activityViewController.excludedActivityTypes = @[UIActivityTypeAirDrop];
        
        // 分享之后的回调
        activityViewController.completionWithItemsHandler = ^(UIActivityType  _Nullable activityType, BOOL completed, NSArray * _Nullable returnedItems, NSError * _Nullable activityError) {
            if (completed) {
                
            } else if (activityError) {
                
            }
        };
        if (self.isIPad) {
            activityViewController.popoverPresentationController.sourceView = self.view;
            activityViewController.popoverPresentationController.sourceRect = CGRectMake(self.view.frame.size.width/2.0, self.view.frame.size.height/2.0, 1, 1);
            activityViewController.popoverPresentationController.permittedArrowDirections = UIPopoverArrowDirectionUp;
        }
        [self presentViewController:activityViewController animated:YES completion:^{
            [SVProgressHUD dismiss];
        }];
    };
    
    [saveButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(-[iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
    
    [shareButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset([iPhoneXTool setCommonValue:22 orStrangeValue:18]);
        make.centerX.equalTo(self.imageBox.mas_centerX).with.offset([iPhoneXTool setCommonValue:70 orStrangeValue:60]);
        make.width.equalTo(@50);
        make.height.equalTo(@66);
    }];
}

- (void)reselectImageButtonClick:(UIButton *)sender
{
    if ([self.delegate respondsToSelector:@selector(reselectPhotoImage)]) {
        [self.delegate reselectPhotoImage];
    }
    
    [self.navigationController popViewControllerAnimated:YES];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo
{
    if (error != NULL) {
        [SVProgressHUD showErrorWithStatus:NSLocalizedString(@"PictureSaveFailedTips", nil)];
    } else {
        [SVProgressHUD showSuccessWithStatus:NSLocalizedString(@"PictureSaveSucceedTips", nil)];
    }
}

- (void)startMagicChangeFace
{
    NSString *imageName = @"PhotoImage";

    UIImage *photoImage = [ImageManager compressImage:_photoImage maxSize:1100];
    NSData *imageData = UIImageJPEGRepresentation(photoImage, 0.8);
    NSString *imagePath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg", imageName]];
    [imageData writeToFile:imagePath atomically:YES];

    NSString *txtPath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.txt", imageName]];
    FacePointsTool *facePointsTool = [[FacePointsTool alloc] init];
    BOOL isWriteFacePoints = [facePointsTool writeFacePointsWithImagePath:imagePath toTxtPath:txtPath];
    if (isWriteFacePoints == YES) {
        
        
        [self.magicChangeFaceView startMagicChangeFaceWithTemplateName:_templateName];
        
        if (self.magicChangeFaceView.resultImage) {
            [SVProgressHUD dismiss];
            [self initViews];
            
            NSDictionary *opts = [NSDictionary dictionaryWithObject:
                                  CIDetectorAccuracyHigh forKey:CIDetectorAccuracy];
            // 将图像转换为CIImage
            CIImage *faceImage = [CIImage imageWithCGImage:self.magicChangeFaceView.resultImage.CGImage];
            CIDetector *faceDetector=[CIDetector detectorOfType:CIDetectorTypeFace context:nil options:opts];
            // 识别出人脸数组
            NSArray *features = [faceDetector featuresInImage:faceImage];
            
            // 得到图片的尺寸
            CGSize inputImageSize = [faceImage extent].size;
            //将image沿y轴对称
            CGAffineTransform transform = CGAffineTransformScale(CGAffineTransformIdentity, 1, -1);
            //将图片上移
            transform = CGAffineTransformTranslate(transform, 0, -inputImageSize.height);
            CIFaceFeature *faceFeature = features.firstObject;
            
            CGRect faceViewBounds = CGRectApplyAffineTransform(faceFeature.bounds, transform);
            CGSize viewSize = self.magicChangeFaceView.bounds.size;
            CGFloat scale = MIN(viewSize.width / inputImageSize.width,
                                viewSize.height / inputImageSize.height);
            CGFloat offsetX = (viewSize.width - inputImageSize.width * scale) / 2;
            CGFloat offsetY = (viewSize.height - inputImageSize.height * scale) / 2;
            // 缩放
            CGAffineTransform scaleTransform = CGAffineTransformMakeScale(scale, scale);
            // 修正
            faceViewBounds = CGRectApplyAffineTransform(faceViewBounds,scaleTransform);
            faceViewBounds.origin.x += offsetX;
            faceViewBounds.origin.y += offsetY;
            
            //NSLog(@"faceViewBounds == %@", NSStringFromCGRect(faceViewBounds));
            
            CGFloat originX = faceViewBounds.origin.x - faceViewBounds.size.width * 0.9;
            CGFloat originY = faceViewBounds.origin.y - faceViewBounds.size.height;
            CGRect avatarFrame = CGRectMake(originX, originY, faceViewBounds.size.width * 2.8, faceViewBounds.size.height * 2.8);
            
            // 截取正方形头像
            UIImage *avatarImage = [ImageManager cutImageFromView:self.magicChangeFaceView inFrame:avatarFrame];
            self.avatarImageView.image = avatarImage;
            self.avatarImageView.alpha = 1.0;
            
            self.magicChangeFaceView.alpha = 0;
            
            [self.labelTips setText:NSLocalizedString(@"MagicFaceChangeTips7", nil)];
        } else {
            NSLog(@"111");
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"MagicFaceChangeTips8", nil)];
        }
    } else {
        [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips1", nil)];
        [self.labelTips setText:NSLocalizedString(@"MagicFaceChangeTips8", nil)];
        [FileManager removeDocumentsFileFromFilePath:imagePath];
    }
}

@end
