//
//  MagicViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/20.
//

#import "MagicViewController.h"
#import "MagicResultVC.h"
#import "MyImagePickerController.h"
#import "UIImage+FixOrientation.h"
#import "MyImagePickerResultVC.h"
#import "CheckImageHaveFace.h"
#import "ImageManager.h"
#import "FileManager.h"
#import "DatabaseManager.h"
#import "TimeTool.h"
#import "FacePointsTool.h"

@interface MagicViewController () <UINavigationControllerDelegate, UIImagePickerControllerDelegate, MagicResultVCDelegate>

@property (nonatomic, strong) UILabel *labelTips;
@property (nonatomic, strong) UIButton *magicButton;
@property (nonatomic, strong) UIImageView *cutImageView;
@property (nonatomic, strong) UIImage *photoImage;
@property (nonatomic, strong) NSMutableArray *templateList;
@property (nonatomic, strong) UIScrollView *templateScrollView;

@property (nonatomic) BOOL isSelectTemplate;

@end

@implementation MagicViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.

    self.templateList = [NSMutableArray arrayWithObjects:@"添加魔法变脸模板", nil];
    
    self.isTopButton = NO;
    
    if (self.cutImageView == nil) {
        self.cutImageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, kStatusBarHeight+60, kScreenWidth, kScreenHeight-kStatusBarHeight-60-kBottomSafeHeight-20)];
        self.cutImageView.contentMode = UIViewContentModeScaleAspectFit;
        self.cutImageView.alpha = 0;
        [self.view addSubview:self.cutImageView];
    }
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    if (self.isIPad == YES) {
        [self.imageBox setFrame:CGRectMake(kScreenWidth*0.2, 90, kScreenWidth*0.6, (kScreenWidth*0.6)*1.66)];
    } else {
        [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+6 orStrangeValue:kStatusBarHeight+50+16], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
    }
    
    [self initAddButtonAndAvatar];
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:NSLocalizedString(@"MagicFaceChangeTips1", nil)];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        if (self.isIPad == YES) {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-(kScreenWidth*0.6)*1.66*0.12);
                make.width.equalTo(@(kScreenWidth*0.6*0.6));
            }];
        } else {
            [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
                make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-80);
                make.width.equalTo(@220);
            }];
        }
    }
    
    UILabel *templateTips = [[UILabel alloc] initWithFrame:CGRectMake(20, self.imageBox.frame.origin.y+self.imageBox.bounds.size.height+12, kScreenWidth-40, 15)];
    templateTips.text = NSLocalizedString(@"MagicFaceChangeTips2", nil);
    templateTips.textColor = [UIColor whiteColor];
    templateTips.font = [UIFont systemFontOfSize:11.0];
    [self.view addSubview:templateTips];
    
    if (self.templateScrollView == nil) {
        self.templateScrollView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, templateTips.frame.origin.y+templateTips.bounds.size.height+6, kScreenWidth, 122)];
        self.templateScrollView.showsVerticalScrollIndicator = NO;
        self.templateScrollView.showsHorizontalScrollIndicator = NO;
        [self.view addSubview:self.templateScrollView];
    }
    
    // 底下模板图片
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    NSArray *templateArray = [databaseManager obtainMagicTemplateListTable];
    if (templateArray.count > 0) {
        [self.templateList addObjectsFromArray:templateArray];
    }
    [self initTemplateView];
    
}

- (void)initTemplateView
{
    if (_templateScrollView.subviews.count > 0) {
        [_templateScrollView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    }
    [_templateScrollView setContentSize:CGSizeMake(20+114*self.templateList.count, 122)];
    for (int i = 0; i < self.templateList.count; i++) {
        UIImageView *imgView = [[UIImageView alloc] initWithFrame:CGRectMake(20+114*i, 0, 94, 122)];
        imgView.contentMode = UIViewContentModeScaleAspectFill;
        imgView.clipsToBounds = YES;
        imgView.layer.cornerRadius = 8.0;
        imgView.layer.masksToBounds = YES;
        imgView.userInteractionEnabled = YES;
        [_templateScrollView addSubview:imgView];
        if (i == 0) {
            imgView.image = [UIImage imageNamed:self.templateList[i]];
        } else {
            imgView.image = [UIImage imageWithContentsOfFile:[[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg", self.templateList[i][@"template_name"]]]];
        }
        
        UIButton *button = [[UIButton alloc] initWithFrame:imgView.bounds];
        button.tag = 1000+i;
        [button addTarget:self action:@selector(selectTemplate:) forControlEvents:UIControlEventTouchUpInside];
        [imgView addSubview:button];
    }
}

- (void)cartoonButtonClick:(UIButton *)sender
{
    MagicResultVC *vc = [[MagicResultVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
}


//  Delegate
- (void)reselectPhotoImage
{
    self.avatarImageView.image = nil;
    self.avatarImageView.alpha = 0;
    
    self.photoImage = nil;
    
    self.magicButton.userInteractionEnabled = NO;
    self.magicButton.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
}


- (void)addPhotoButtonClick:(UIButton *)sender
{
    _isSelectTemplate = NO;
    NSString *mediaType = AVMediaTypeVideo;
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];
    if (authStatus == AVAuthorizationStatusNotDetermined) {
        //第一次使用
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (granted) {
                    //第一次，用户选择拒绝
                    [self presentImagePickerController];
                }
            });
        }];
    } else if (authStatus == AVAuthorizationStatusDenied || authStatus == AVAuthorizationStatusRestricted) {
        //无权限
        UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"CameraPowerTips1", nil) message:NSLocalizedString(@"CameraPowerTips2", nil) preferredStyle:UIAlertControllerStyleAlert];
        UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if ([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
            }
        }];
        UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        }];
        [alert addAction:okAction];
        [alert addAction:cancelAction];
        [self presentViewController:alert animated:YES completion:nil];
    } else if (authStatus == AVAuthorizationStatusAuthorized) {
        //用户已授权
        [self presentImagePickerController];
    }
}

- (void)presentImagePickerController
{
    if ([UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        MyImagePickerController *vc = [[MyImagePickerController alloc] init];
        vc.delegate = self; //delegate遵循了两个代理
        vc.allowsEditing = (_isSelectTemplate == YES) ? YES : NO;  // 模板选择时允许编辑
        vc.sourceType = UIImagePickerControllerSourceTypeCamera;
        vc.cameraDevice = UIImagePickerControllerCameraDeviceFront;
        vc.cameraCaptureMode = UIImagePickerControllerCameraCaptureModePhoto;
        vc.mediaTypes = [UIImagePickerController availableMediaTypesForSourceType:UIImagePickerControllerSourceTypeCamera];
        [self presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.openAlbum = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                self.cutImageView.alpha = 1.0;
                self.cutImageView.image = image;
                UIImage *avatarImage = [ImageManager cutImageFromImageView:self.cutImageView];
                
                self.avatarImageView.alpha = 1;
                self.avatarImageView.image = avatarImage;
                
                self.photoImage = image;
                
                self.magicButton.userInteractionEnabled = YES;
                self.magicButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
                
                self.cutImageView.alpha= 0;
            }];
        };
    }
}


#pragma mark - UIImagePickerControllerDelegate
- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey,id> *)info
{
    if (_isSelectTemplate == YES) {
        [SVProgressHUD showWithStatus:NSLocalizedString(@"MagicFaceChangeTips3", nil)];

        // 优先获取编辑后的图像，如果没有则获取原始图像
        UIImage *photoImage = [info valueForKey:UIImagePickerControllerEditedImage];
        if (!photoImage) {
            photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
            // 修正图像方向
            photoImage = [photoImage fixOrientation];
        }

        NSLog(@"模板选择 - 图像尺寸: %@, 方向: %ld", NSStringFromCGSize(photoImage.size), (long)photoImage.imageOrientation);

        if ([CheckImageHaveFace checkHaveFaceWithImage:photoImage]) {
            NSString *imageName = [NSString stringWithFormat:@"image%@", [TimeTool getCurrentTime]];

            UIImage *finalImage = [ImageManager compressImage:photoImage maxSize:1300];
            NSData *imageData = UIImageJPEGRepresentation(finalImage, 0.9);
            NSString *imagePath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg", imageName]];
            [imageData writeToFile:imagePath atomically:YES];

            [picker dismissViewControllerAnimated:YES completion:^{
                NSString *txtPath = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.txt", imageName]];
                FacePointsTool *facePointsTool = [[FacePointsTool alloc] init];
                BOOL isWriteFacePoints = [facePointsTool writeFacePointsWithImagePath:imagePath toTxtPath:txtPath];
                if (isWriteFacePoints == YES) {
                    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
                    [databaseManager insertMagicTemplateTableWithTemplate_name:imageName];
                    
                    NSDictionary *templateDict = [NSDictionary dictionaryWithObjects:@[@"", imageName] forKeys:@[@"template_id", @"template_name"]];
                    [self.templateList addObject:templateDict];
                    [self initTemplateView];
                    
                    [SVProgressHUD showSuccessWithStatus:NSLocalizedString(@"MagicFaceChangeTips4", nil)];
                } else {
                    [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips1", nil)];
                    
                    [FileManager removeDocumentsFileFromFilePath:imagePath];
                }
            }];
        } else {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips1", nil)];
            [picker dismissViewControllerAnimated:YES completion:^{}];
        }
    } else {
        UIImage *photoImage = [info valueForKey:UIImagePickerControllerOriginalImage];
        photoImage = [photoImage fixOrientation];
        //改变该图片的方向
        photoImage = [UIImage imageWithCGImage:photoImage.CGImage scale:photoImage.scale orientation:UIImageOrientationUpMirrored];
        //NSLog(@"imageSize = %@", NSStringFromCGSize(photoImage.size));
        //  截取图像
        
        MyImagePickerResultVC *vc = [[MyImagePickerResultVC alloc] init];
        vc.photoImage = photoImage;
        vc.modalPresentationStyle = UIModalPresentationFullScreen;
        [picker presentViewController:vc animated:YES completion:^{}];
        __weak typeof(vc) weakVC = vc;
        vc.dismissVC = ^(UIImage * _Nonnull image) {
            [weakVC dismissViewControllerAnimated:YES completion:^{
                [picker dismissViewControllerAnimated:NO completion:^{
                }];
                self.photoImage = photoImage;
                self.avatarImageView.alpha = 1;
                self.avatarImageView.image = image;
                self.magicButton.userInteractionEnabled = YES;
                self.magicButton.backgroundColor = [MyColor colorWithHexString:@"#DEDEE0" alpha:1.0];
            }];
        };
    }
}

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker
{
    [picker dismissViewControllerAnimated:YES completion:^{
            
    }];
}

- (void)selectTemplate:(UIButton *)sender
{
    NSInteger index = sender.tag - 1000;
    NSLog(@"index == %ld", (long)index);
    if (index == 0) {
        _isSelectTemplate = YES;
        PHAuthorizationStatus authorizationStatus = [PHPhotoLibrary authorizationStatus];
        if (authorizationStatus == PHAuthorizationStatusNotDetermined) {
            //第一次使用
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus status) {
                if (status == PHAuthorizationStatusAuthorized) {
                    dispatch_async(dispatch_get_main_queue(), ^{
                        UIImagePickerController *vc = [[UIImagePickerController alloc] init];
                        vc.allowsEditing = YES;
                        vc.delegate = self;
                        vc.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
                        [self presentViewController:vc animated:YES completion:nil];
                    });
                }
            }];
        } else if (authorizationStatus == PHAuthorizationStatusDenied || authorizationStatus == PHAuthorizationStatusRestricted) {
            //无权限
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"AlbumPowerTips1", nil) message:NSLocalizedString(@"AlbumPowerTips2", nil) preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                if ([[UIApplication sharedApplication] canOpenURL:url]) {
                    [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
                }
            }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            }];
            [alert addAction:okAction];
            [alert addAction:cancelAction];
            [self presentViewController:alert animated:YES completion:nil];
        } else if (authorizationStatus == PHAuthorizationStatusAuthorized) {
            //用户已授权
            UIImagePickerController *vc = [[UIImagePickerController alloc] init];
            vc.allowsEditing = YES;
            vc.delegate = self;
            vc.sourceType = UIImagePickerControllerSourceTypePhotoLibrary;
            [self presentViewController:vc animated:YES completion:nil];
        }
    } else {
        if (self.avatarImageView.image) {
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"MagicFaceChangeTips5", nil) message:@"" preferredStyle:UIAlertControllerStyleAlert];
            UIAlertAction *okAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"OK", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
                if ([CheckImageHaveFace checkHaveFaceWithImage:self->_photoImage]) {
                    MagicResultVC *vc = [[MagicResultVC alloc] init];
                    vc.photoImage = self.avatarImageView.image;
                    vc.templateName = self->_templateList[index][@"template_name"];
                    vc.delegate = self;
                    [self.navigationController pushViewController:vc animated:YES];
                } else {
                    [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips1", nil)];
                }
            }];
            UIAlertAction *cancelAction = [UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            }];
            [alert addAction:okAction];
            [alert addAction:cancelAction];
            [self presentViewController:alert animated:YES completion:nil];
        } else {
            [SVProgressHUD showInfoWithStatus:NSLocalizedString(@"PhotosNoFaceTips2", nil)];
        }
    }
}


@end
