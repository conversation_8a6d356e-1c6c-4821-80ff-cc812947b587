//
//  MagicChangeFaceView.m
//  TimeMachine
//
//  Created by FS003 on 2021/8/5.
//

#import "MagicChangeFaceView.h"
#import "FileManager.h"

// OpenCV 现代C++头文件
#import <opencv2/opencv.hpp>
#import <opencv2/imgproc.hpp>
#import <opencv2/photo.hpp>
#import <opencv2/imgcodecs.hpp>

// C++ 标准库
#include <iostream>
#include <fstream>
#include <string>
#include <vector>

using namespace cv;
using namespace std;

@implementation MagicChangeFaceView

- (void)drawRect:(CGRect)rect
{
    // Drawing code
}
 

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor clearColor];
    }
    return self;
}

- (void)startMagicChangeFaceWithTemplateName:(NSString *)templateName
{
    //Read input images

    NSString *filePath1 = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:@"PhotoImage.txt"];
    NSString *filePath2 = [[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.txt", templateName]];

    string filename1 = [filePath1 cStringUsingEncoding:NSUTF8StringEncoding];
    string filename2 = [filePath2 cStringUsingEncoding:NSUTF8StringEncoding];
    //cv::Mat img1 = imread(filename1);
    //cv::Mat img2 = imread(filename2);

    cv::Mat img1 = [self cvMatFromUIImage:[UIImage imageWithContentsOfFile:[[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:@"PhotoImage.jpg"]]];
    cv::Mat img2 = [self cvMatFromUIImage:[UIImage imageWithContentsOfFile:[[FileManager pathForDocumentsFileName:MagicTemplate] stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.jpg", templateName]]]];
    cv::Mat img1Warped = img2.clone();
    if (img1.data) {
        NSLog(@"img1 is image data");
    } else {
        NSLog(@"img1 no image data");
    }
    if (img2.data) {
        NSLog(@"img2 is image data");
    } else {
        NSLog(@"img2 no image data");
    }

    //Read points
    vector<cv::Point2f> points1, points2;
    points1 = readPoints(filename1);
    points2 = readPoints(filename2);

    //convert Mat to float data type
    img1.convertTo(img1, CV_32F);
    img1Warped.convertTo(img1Warped, CV_32F);
    
    // Find convex hull
    vector<cv::Point2f> hull1;
    vector<cv::Point2f> hull2;
    vector<int> hullIndex;

    convexHull(points2, hullIndex, false, false);
    for(int i = 0; i < hullIndex.size(); i++) {
        hull1.push_back(points1[hullIndex[i]]);
        hull2.push_back(points2[hullIndex[i]]);
    }
    // Find delaunay triangulation for points on the convex hull
    vector< vector<int> > dt;
    cv::Rect rect(0, 0, img1Warped.cols, img1Warped.rows);
    calculateDelaunayTriangles(rect, hull2, dt);
    // Apply affine transformation to Delaunay triangles
    for(size_t i = 0; i < dt.size(); i++)
    {
        vector<cv::Point2f> t1, t2;
        // Get points for img1, img2 corresponding to the triangles
        for(size_t j = 0; j < 3; j++)
        {
            t1.push_back(hull1[dt[i][j]]);
            t2.push_back(hull2[dt[i][j]]);
        }
        warpTriangle(img1, img1Warped, t1, t2);
    }
    // Calculate mask
    vector<cv::Point> hull8U;
    for(int i = 0; i < hull2.size(); i++) {
        cv::Point pt(hull2[i].x, hull2[i].y);
        hull8U.push_back(pt);
    }
    cv::Mat mask = cv::Mat::zeros(img2.rows, img2.cols, img2.depth());
    fillConvexPoly(mask, hull8U, cv::Scalar(255,255,255));
    // Clone seamlessly.
    cv::Rect r = boundingRect(hull2);
    cv::Point center = (r.tl() + r.br()) / 2;
    cv::Mat output;
    img1Warped.convertTo(img1Warped, CV_8UC3);
    seamlessClone(img1Warped, img2, mask, center, output, cv::NORMAL_CLONE);
    UIImage *resultImage = [self imageFromCVMat:output];
    NSLog(@"resultImage == %@", resultImage);
    self.resultImage = resultImage;

    UIImageView *imageView = [[UIImageView alloc] initWithFrame:self.bounds];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    imageView.userInteractionEnabled = YES;
    [self addSubview:imageView];
    imageView.image = resultImage;
}

//Read points from text file
vector<cv::Point2f> readPoints(string pointsFileName){
    vector<cv::Point2f> points;
    ifstream ifs (pointsFileName.c_str());
    float x, y;
    int count = 0;
    while(ifs >> x >> y)
    {
        points.push_back(cv::Point2f(x,y));
    }
    return points;
}

// Apply affine transform calculated using srcTri and dstTri to src
void applyAffineTransform(cv::Mat &warpImage, cv::Mat &src, vector<cv::Point2f> &srcTri, vector<cv::Point2f> &dstTri)
{
    // Given a pair of triangles, find the affine transform.
    cv::Mat warpMat = getAffineTransform( srcTri, dstTri );
    
    // Apply the Affine Transform just found to the src image
    warpAffine( src, warpImage, warpMat, warpImage.size(), cv::INTER_LINEAR, cv::BORDER_REFLECT_101);
}


// Calculate Delaunay triangles for set of points
// Returns the vector of indices of 3 points for each triangle
static void calculateDelaunayTriangles(cv::Rect rect, vector<cv::Point2f> &points, vector< vector<int> > &delaunayTri){

    // Create an instance of Subdiv2D
    cv::Subdiv2D subdiv(rect);

    // Insert points into subdiv
    for( vector<cv::Point2f>::iterator it = points.begin(); it != points.end(); it++)
        subdiv.insert(*it);

    vector<cv::Vec6f> triangleList;
    subdiv.getTriangleList(triangleList);
    vector<cv::Point2f> pt(3);
    vector<int> ind(3);

    for( size_t i = 0; i < triangleList.size(); i++ )
    {
        cv::Vec6f t = triangleList[i];
        pt[0] = cv::Point2f(t[0], t[1]);
        pt[1] = cv::Point2f(t[2], t[3]);
        pt[2] = cv::Point2f(t[4], t[5 ]);

        if ( rect.contains(pt[0]) && rect.contains(pt[1]) && rect.contains(pt[2])){
            for(int j = 0; j < 3; j++)
                for(size_t k = 0; k < points.size(); k++)
                    if(abs(pt[j].x - points[k].x) < 1.0 && abs(pt[j].y - points[k].y) < 1)
                        ind[j] = k;

            delaunayTri.push_back(ind);
        }
    }
}


// Warps and alpha blends triangular regions from img1 and img2 to img
void warpTriangle(cv::Mat &img1, cv::Mat &img2, vector<cv::Point2f> &t1, vector<cv::Point2f> &t2)
{
    // ------------ 新增校验逻辑 ------------
        // 检查点数量是否为3
        if (t1.size() != 3 || t2.size() != 3) {
            NSLog(@"Error: Triangles must have exactly 3 points (t1=%lu, t2=%lu)", t1.size(), t2.size());
            return;
        }
        
        // 检查输入图像非空
        if (img1.empty() || img2.empty()) {
            NSLog(@"Error: Input images are empty");
            return;
        }
        // --------------------------------------
    cv::Rect r1 = boundingRect(t1);
    cv::Rect r2 = boundingRect(t2);
    // ------------ 新增矩形范围校验 ------------
        // 确保矩形在图像范围内
        r1 = r1 & cv::Rect(0, 0, img1.cols, img1.rows);
        r2 = r2 & cv::Rect(0, 0, img2.cols, img2.rows);
        
        if (r1.area() <= 0 || r2.area() <= 0) {
            NSLog(@"Error: Invalid bounding rectangles (r1=[%d,%d,%d,%d], r2=[%d,%d,%d,%d])",
                 r1.x, r1.y, r1.width, r1.height, r2.x, r2.y, r2.width, r2.height);
            return;
        }
        // --------------------------------------
    
    // Offset points by left top corner of the respective rectangles
    vector<cv::Point2f> t1Rect, t2Rect;
    vector<cv::Point> t2RectInt;
    for(int i = 0; i < 3; i++)
    {
        // ------------ 新增点坐标校验 ------------
            if (t1[i].x < r1.x || t1[i].y < r1.y ||
                t1[i].x >= img1.cols || t1[i].y >= img1.rows) {
                NSLog(@"Error: t1[%d]=(%f,%f) is outside image1 (cols=%d, rows=%d)",
                     i, t1[i].x, t1[i].y, img1.cols, img1.rows);
                return;
            }
            if (t2[i].x < r2.x || t2[i].y < r2.y ||
                t2[i].x >= img2.cols || t2[i].y >= img2.rows) {
                NSLog(@"Error: t2[%d]=(%f,%f) is outside image2 (cols=%d, rows=%d)",
                     i, t2[i].x, t2[i].y, img2.cols, img2.rows);
                return;
            }
            // --------------------------------------
        t1Rect.push_back( cv::Point2f( t1[i].x - r1.x, t1[i].y -  r1.y) );
        t2Rect.push_back( cv::Point2f( t2[i].x - r2.x, t2[i].y - r2.y) );
        t2RectInt.push_back( cv::Point(t2[i].x - r2.x, t2[i].y - r2.y) ); // for fillConvexPoly

    }
    
    // Get mask by filling triangle
    cv::Mat mask = cv::Mat::zeros(r2.height, r2.width, CV_32FC3);
    fillConvexPoly(mask, t2RectInt, cv::Scalar(1.0, 1.0, 1.0), cv::LINE_8, 0);
    
    // Apply warpImage to small rectangular patches
    cv::Mat img1Rect;
    // ------------ 新增矩阵子区域校验 ------------
    if (r1.x + r1.width > img1.cols || r1.y + r1.height > img1.rows) {
        NSLog(@"Error: img1 ROI [%d,%d,%d,%d] exceeds image size [%d,%d]",
             r1.x, r1.y, r1.width, r1.height, img1.cols, img1.rows);
        return;
    }
    
    
    img1(r1).copyTo(img1Rect);
    
    cv::Mat img2Rect = cv::Mat::zeros(r2.height, r2.width, img1Rect.type());
    // ------------ 新增 mask 创建校验 ------------
    if (r2.width <= 0 || r2.height <= 0) {
        NSLog(@"Error: Invalid mask size (r2=[%d,%d,%d,%d])", r2.x, r2.y, r2.width, r2.height);
        return;
    }
    
    
    
    try {
        applyAffineTransform(img2Rect, img1Rect, t1Rect, t2Rect);
        
        multiply(img2Rect, mask, img2Rect);
        multiply(img2(r2), cv::Scalar(1.0,1.0,1.0) - mask, img2(r2));
        img2(r2) = img2(r2) + img2Rect;
    } catch (const cv::Exception &e) {
        // OpenCV 内部错误捕获
        NSLog(@"OpenCV Exception: %s", e.what());
    } catch (...) {
        // 通用 C++ 异常捕获
        NSLog(@"Unknown C++ Exception in warpTriangle");
    }
}

- (cv::Mat)cvMatFromUIImage:(UIImage *)image
{
  CGColorSpaceRef colorSpace = CGImageGetColorSpace(image.CGImage);
  CGFloat cols = image.size.width;
  CGFloat rows = image.size.height;

  cv::Mat cvMat(rows, cols, CV_8UC4); // 8 bits per component, 4 channels (color channels + alpha)

  CGContextRef contextRef = CGBitmapContextCreate(cvMat.data,                 // Pointer to  data
                                                 cols,                       // Width of bitmap
                                                 rows,                       // Height of bitmap
                                                 8,                          // Bits per component
                                                 cvMat.step[0],              // Bytes per row
                                                 colorSpace,                 // Colorspace
                                                 kCGImageAlphaNoneSkipLast |
                                                 kCGBitmapByteOrderDefault); // Bitmap info flags

  CGContextDrawImage(contextRef, CGRectMake(0, 0, cols, rows), image.CGImage);
  CGContextRelease(contextRef);
    cvtColor(cvMat, cvMat, cv::COLOR_BGRA2RGB);
  return cvMat;
}

/*
- (UIImage *)imageFromCVMat:(cv::Mat)cvMat
{
    cvtColor(cvMat, cvMat, 1);
    NSData *data = [NSData dataWithBytes:cvMat.data length:cvMat.elemSize()*cvMat.total()];
    NSLog(@"data == %@", data);
    CGColorSpaceRef colorSpace;
    CGBitmapInfo bitmapInfo;
    if (cvMat.elemSize() == 1) {
        colorSpace = CGColorSpaceCreateDeviceGray();
        bitmapInfo = kCGImageAlphaNone | kCGBitmapByteOrderDefault;
    } else {
        colorSpace = CGColorSpaceCreateDeviceRGB();
        bitmapInfo = kCGBitmapByteOrder32Little | (cvMat.elemSize() == 3? kCGImageAlphaNone : kCGImageAlphaNoneSkipFirst);
        
    }
    CGDataProviderRef provider = CGDataProviderCreateWithCFData((__bridge CFDataRef)data);
    // Creating CGImage from cv::Mat
    CGImageRef imageRef = CGImageCreate(
                                        cvMat.cols,                 //width
                                        cvMat.rows,                 //height
                                        8,                          //bits per component
                                        8 * cvMat.elemSize(),       //bits per pixel
                                        cvMat.step[0],              //bytesPerRow
                                        colorSpace,                 //colorspace
                                        bitmapInfo,                 // bitmap info
                                        provider,                   //CGDataProviderRef
                                        NULL,                       //decode
                                        false,                      //should interpolate
                                        kCGRenderingIntentDefault   //intent
                                        );
    // Getting UIImage from CGImage
    UIImage *finalImage = [UIImage imageWithCGImage:imageRef];
    CGImageRelease(imageRef);
    CGDataProviderRelease(provider);
    CGColorSpaceRelease(colorSpace);
    
    return finalImage;
}    */

- (UIImage *)imageFromCVMat:(cv::Mat)cvMat
{
    cvtColor(cvMat, cvMat, cv::COLOR_RGB2BGR);
    NSData *data = [NSData dataWithBytes:cvMat.data length:cvMat.elemSize()*cvMat.total()];
    //NSLog(@"data == %@", data);
    CGColorSpaceRef colorSpace;
    if (cvMat.elemSize() == 1) {
        colorSpace = CGColorSpaceCreateDeviceGray();
    } else {
        colorSpace = CGColorSpaceCreateDeviceRGB();
        
    }
    CGDataProviderRef provider = CGDataProviderCreateWithCFData((__bridge CFDataRef)data);
    bool alpha = cvMat.channels() == 4;
    //kCGImageAlphaPremultipliedLast保留透明度
    CGBitmapInfo bitmapInfo = (alpha ? kCGImageAlphaPremultipliedLast : kCGImageAlphaNone) | kCGBitmapByteOrderDefault;
    CGImageRef imageRef = CGImageCreate(cvMat.cols,
                                        cvMat.rows,
                                        8,
                                        8 * cvMat.elemSize(),
                                        cvMat.step.p[0],
                                        colorSpace,
                                        bitmapInfo,
                                        provider,
                                        NULL,
                                        false,
                                        kCGRenderingIntentDefault);
    UIImage *newImage = [UIImage imageWithCGImage:imageRef];
    CGImageRelease(imageRef);
    CGDataProviderRelease(provider);
    CGColorSpaceRelease(colorSpace);
    return newImage;
}


@end
