//
//  FacePointsTool.m
//  TimeMachine
//
//  Created by FS003 on 2021/8/5.
//

#import "FacePointsTool.h"
#import <CoreImage/CoreImage.h>
#import <dlib/image_io.h>
#import <dlib/image_processing.h>
#import <dlib/opencv/cv_image_abstract.h>
#import <dlib/opencv.h>
#import <dlib/image_processing/frontal_face_detector.h>
#include <string>
//#import <dlib/image_processing/render_face_detections.h>
#import "FileManager.h"

using namespace std;
using namespace dlib;

@implementation FacePointsTool
{
    dlib::shape_predictor sp;
}

- (instancetype)init
{
    self = [super init];
    if (self) {

    }
    return self;
}

- (BOOL)writeFacePointsWithImagePath:(NSString *)imagePath toTxtPath:(NSString *)txtPath
{
    BOOL isWriteFacePoints = NO;
    
    //NSString *fileTxtPath = [[FileManager pathForDocumentsFileName:@"MagicFace"] stringByAppendingPathComponent:@"MagicFace.txt"];
    [@"" writeToFile:txtPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
    
    //初始化 检测器
    NSString *modelFileName = [[NSBundle mainBundle] pathForResource:@"shape_predictor_68_face_landmarks" ofType:@"dat"];
    string modelFileNameCString = [modelFileName UTF8String];
    dlib::deserialize(modelFileNameCString) >> sp;
    
    dlib::array2d<dlib::rgb_pixel> img_Dlib;

    // 使用 iOS 原生方式加载图片，避免 JPEG 库版本冲突
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    if (!image) {
        NSLog(@"Failed to load image from path: %@", imagePath);
        return NO;
    }

    // 将 UIImage 转换为 dlib 格式
    [self convertUIImageToDlibArray:image dlibArray:img_Dlib];

    // 预处理图像以提高检测率
    [self preprocessImageForFaceDetection:img_Dlib];

    NSLog(@"FacePointsTool: 开始检测人脸，图像尺寸: %ldx%ld", img_Dlib.nc(), img_Dlib.nr());

    dlib::frontal_face_detector detector = get_frontal_face_detector();

    // 尝试不同的检测参数
    std::vector<dlib::rectangle> dets;

    // 首先尝试标准检测
    dets = detector(img_Dlib, 1);
    NSLog(@"FacePointsTool: 标准检测找到 %lu 个人脸", dets.size());

    // 如果没有检测到，尝试更细致的检测
    if (dets.size() == 0) {
        dets = detector(img_Dlib, 0);  // 更细致的检测
        NSLog(@"FacePointsTool: 细致检测找到 %lu 个人脸", dets.size());
    }

    // 如果还是没有检测到，尝试缩放图像
    if (dets.size() == 0) {
        dlib::array2d<dlib::rgb_pixel> scaled_img;
        // 尝试放大图像
        long new_width = (long)(img_Dlib.nc() * 1.5);
        long new_height = (long)(img_Dlib.nr() * 1.5);
        scaled_img.set_size(new_height, new_width);
        dlib::resize_image(img_Dlib, scaled_img);
        dets = detector(scaled_img, 1);
        NSLog(@"FacePointsTool: 放大图像检测找到 %lu 个人脸", dets.size());

        // 如果在放大图像中找到了人脸，需要调整坐标
        if (dets.size() > 0) {
            for (auto& det : dets) {
                det = dlib::rectangle(
                    (long)(det.left() / 1.5),
                    (long)(det.top() / 1.5),
                    (long)(det.right() / 1.5),
                    (long)(det.bottom() / 1.5)
                );
            }
        }
    }

    if (dets.size() > 0) {
        isWriteFacePoints = YES;
        full_object_detection shape = sp(img_Dlib, dets[0]);
        for (int i = 0; i < shape.num_parts(); ++i) {
            float x = shape.part(i).x();
            float y = shape.part(i).y();
            
            NSString *iStr = [NSString stringWithFormat:@"%.0f \t %.0f", x, y];
            NSFileHandle *fileHandle = [NSFileHandle fileHandleForUpdatingAtPath:txtPath];
            [fileHandle seekToEndOfFile];  //将节点跳到文件的末尾
            NSData *stringData = [[NSString stringWithFormat:@"%@\n", iStr] dataUsingEncoding:NSUTF8StringEncoding];
            [fileHandle writeData:stringData]; //追加写入数据
            [fileHandle closeFile];
        }
    }
    NSLog(@"lalala");

    return isWriteFacePoints;
}

// 将 UIImage 转换为 dlib::array2d<dlib::rgb_pixel> 格式
- (void)convertUIImageToDlibArray:(UIImage *)image dlibArray:(dlib::array2d<dlib::rgb_pixel>&)dlibArray
{
    CGImageRef cgImage = image.CGImage;
    size_t width = CGImageGetWidth(cgImage);
    size_t height = CGImageGetHeight(cgImage);

    dlibArray.set_size(height, width);

    // 创建颜色空间
    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();

    // 创建位图上下文
    size_t bytesPerPixel = 4;
    size_t bytesPerRow = bytesPerPixel * width;
    size_t bitsPerComponent = 8;

    unsigned char *rawData = (unsigned char *)malloc(height * width * bytesPerPixel);

    CGContextRef context = CGBitmapContextCreate(rawData, width, height,
                                                bitsPerComponent, bytesPerRow, colorSpace,
                                                kCGImageAlphaPremultipliedLast | kCGBitmapByteOrder32Big);

    CGColorSpaceRelease(colorSpace);

    // 绘制图像到上下文
    CGContextDrawImage(context, CGRectMake(0, 0, width, height), cgImage);
    CGContextRelease(context);

    // 转换像素数据到 dlib 格式
    for (size_t y = 0; y < height; ++y) {
        for (size_t x = 0; x < width; ++x) {
            size_t byteIndex = (bytesPerRow * y) + (x * bytesPerPixel);
            unsigned char red = rawData[byteIndex];
            unsigned char green = rawData[byteIndex + 1];
            unsigned char blue = rawData[byteIndex + 2];

            dlib::rgb_pixel pixel;
            pixel.red = red;
            pixel.green = green;
            pixel.blue = blue;

            dlibArray[y][x] = pixel;
        }
    }

    free(rawData);
}

// 预处理图像以提高人脸检测率
- (void)preprocessImageForFaceDetection:(dlib::array2d<dlib::rgb_pixel>&)image
{
    // 转换为灰度图进行处理
    dlib::array2d<unsigned char> gray_img;
    dlib::assign_image(gray_img, image);

    // 应用直方图均衡化来增强对比度
    dlib::equalize_histogram(gray_img);

    // 转换回RGB格式
    dlib::assign_image(image, gray_img);
}

@end
