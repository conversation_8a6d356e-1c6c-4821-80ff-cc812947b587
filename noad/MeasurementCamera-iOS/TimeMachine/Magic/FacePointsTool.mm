//
//  FacePointsTool.m
//  TimeMachine
//
//  Created by FS003 on 2021/8/5.
//

#import "FacePointsTool.h"
#import <CoreImage/CoreImage.h>
#import <dlib/image_io.h>
#import <dlib/image_processing.h>
#import <dlib/opencv/cv_image_abstract.h>
#import <dlib/opencv.h>
#import <dlib/image_processing/frontal_face_detector.h>
#include <string>
//#import <dlib/image_processing/render_face_detections.h>
#import "FileManager.h"

using namespace std;
using namespace dlib;

@implementation FacePointsTool
{
    dlib::shape_predictor sp;
}

- (instancetype)init
{
    self = [super init];
    if (self) {

    }
    return self;
}

- (BOOL)writeFacePointsWithImagePath:(NSString *)imagePath toTxtPath:(NSString *)txtPath
{
    BOOL isWriteFacePoints = NO;
    
    //NSString *fileTxtPath = [[FileManager pathForDocumentsFileName:@"MagicFace"] stringByAppendingPathComponent:@"MagicFace.txt"];
    [@"" writeToFile:txtPath atomically:YES encoding:NSUTF8StringEncoding error:nil];
    
    //初始化 检测器
    NSString *modelFileName = [[NSBundle mainBundle] pathForResource:@"shape_predictor_68_face_landmarks" ofType:@"dat"];
    string modelFileNameCString = [modelFileName UTF8String];
    dlib::deserialize(modelFileNameCString) >> sp;
    
    dlib::array2d<dlib::rgb_pixel> img_Dlib;
    
    // 图片
    string filename = [imagePath cStringUsingEncoding:NSUTF8StringEncoding];
    dlib::load_image(img_Dlib, filename);
    dlib::frontal_face_detector detector = get_frontal_face_detector();
    std::vector<dlib::rectangle> dets = detector(img_Dlib, 1);
       if (dets.size() > 0) {
        isWriteFacePoints = YES;
        full_object_detection shape = sp(img_Dlib, dets[0]);
        for (int i = 0; i < shape.num_parts(); ++i) {
            float x = shape.part(i).x();
            float y = shape.part(i).y();
            
            NSString *iStr = [NSString stringWithFormat:@"%.0f \t %.0f", x, y];
            NSFileHandle *fileHandle = [NSFileHandle fileHandleForUpdatingAtPath:txtPath];
            [fileHandle seekToEndOfFile];  //将节点跳到文件的末尾
            NSData *stringData = [[NSString stringWithFormat:@"%@\n", iStr] dataUsingEncoding:NSUTF8StringEncoding];
            [fileHandle writeData:stringData]; //追加写入数据
            [fileHandle closeFile];
        }
    }
    NSLog(@"lalala");
    
    return isWriteFacePoints;
}


@end
