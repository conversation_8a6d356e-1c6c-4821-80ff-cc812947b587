//
//  AgednessViewController.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "AgednessViewController.h"
#import "iPhoneXTool.h"

@interface AgednessViewController ()

@property (nonatomic, strong) UILabel *labelTips;

@end

@implementation AgednessViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.isTopButton = NO;
    
    [self initBgImgView];
    
    [self initBackButton];
    
    [self initImageBox];
    if (self.imageBox) {
        [self.imageBox setFrame:CGRectMake([iPhoneXTool setCommonValue:60 orStrangeValue:40], [iPhoneXTool setCommonValue:kStatusBarHeight+50+20 orStrangeValue:kStatusBarHeight+50+30], [iPhoneXTool setCommonValue:kScreenWidth-120 orStrangeValue:kScreenWidth-80], [iPhoneXTool setCommonValue:(kScreenWidth-120)*1.66 orStrangeValue:(kScreenWidth-80)*1.66])];
    }
    
    [self initAddButtonAndAvatar];
    
    if (self.labelTips == nil) {
        self.labelTips = [[UILabel alloc] init];
        [self.labelTips setText:@"可以帮你看到未来的自己!\n添加照片试试吧~"];
        [self.labelTips setTextColor:[UIColor whiteColor]];
        [self.labelTips setFont:[UIFont systemFontOfSize:11.0]];
        [self.labelTips setTextAlignment:NSTextAlignmentCenter];
        [self.labelTips setNumberOfLines:0];
        [self.imageBox addSubview:self.labelTips];
        
        [_labelTips mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(self.imageBox.mas_centerX).with.offset(0);
            make.bottom.equalTo(self.imageBox.mas_bottom).with.offset(-80);
            make.width.equalTo(@220);
        }];
    }
    
    // 选择年龄按钮
    CGFloat selectAgeButtonEdge = [iPhoneXTool setCommonValue:70 orStrangeValue:60];
    CGFloat selectAgeButtonWidth = (kScreenWidth-selectAgeButtonEdge*2-30)/2;
    
    UIButton *selectAgeButton20 = [[UIButton alloc] init];
    [selectAgeButton20 setTitle:@"20年后" forState:UIControlStateNormal];
    [selectAgeButton20 setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [selectAgeButton20.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
    selectAgeButton20.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
    selectAgeButton20.layer.cornerRadius = 20;
    [self.view addSubview:selectAgeButton20];
    [selectAgeButton20 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset(25);
        make.leading.equalTo(self.view.mas_leading).with.offset(selectAgeButtonEdge);
        make.width.equalTo(@(selectAgeButtonWidth));
        make.height.equalTo(@40);
    }];
    
    UIButton *selectAgeButton40 = [[UIButton alloc] init];
    [selectAgeButton40 setTitle:@"40年后" forState:UIControlStateNormal];
    [selectAgeButton40 setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [selectAgeButton40.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
    selectAgeButton40.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
    selectAgeButton40.layer.cornerRadius = 20;
    [self.view addSubview:selectAgeButton40];
    [selectAgeButton40 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.imageBox.mas_bottom).with.offset(25);
        make.trailing.equalTo(self.view.mas_trailing).with.offset(-selectAgeButtonEdge);
        make.width.equalTo(@(selectAgeButtonWidth));
        make.height.equalTo(@40);
    }];
    
    UIButton *selectAgeButton60 = [[UIButton alloc] init];
    [selectAgeButton60 setTitle:@"60年后" forState:UIControlStateNormal];
    [selectAgeButton60 setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
    [selectAgeButton60.titleLabel setFont:[UIFont systemFontOfSize:13.0]];
    selectAgeButton60.backgroundColor = [MyColor colorWithHexString:@"#42424D" alpha:1.0];
    selectAgeButton60.layer.cornerRadius = 20;
    [self.view addSubview:selectAgeButton60];
    [selectAgeButton60 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(selectAgeButton40.mas_bottom).with.offset(25);
        make.centerX.equalTo(self.view.mas_centerX).with.offset(0);
        make.width.equalTo(@(selectAgeButtonWidth));
        make.height.equalTo(@40);
    }];
    
}


@end
