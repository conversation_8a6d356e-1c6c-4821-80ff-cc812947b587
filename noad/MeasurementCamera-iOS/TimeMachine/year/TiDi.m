

#import "TiDi.h"
#import <BRPickerView/NSDate+BRPickerView.h>
@interface TiDi ()

@property(nonatomic,strong)NSCalendar * gregorianCalendar;

@property (nonatomic, strong) NSArray *arrTian;
@property (nonatomic, strong) NSArray *arrDi;
@property (nonatomic, strong) NSArray *arrShengxiao;
@property (nonatomic, strong) NSMutableDictionary *tian;
@property (nonatomic, strong) NSMutableDictionary *di;

@end

@implementation TiDi
+(TiDi *)sharedInstance
{
    static TiDi * _app;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _app = [[TiDi alloc] init];
    });
    
    return _app;
}
- (instancetype)init
{
    self = [super init];
    if (self) {
        NSCalendar *calendar = [[NSCalendar alloc] initWithCalendarIdentifier:NSCalendarIdentifierGregorian];
        self.gregorianCalendar = calendar;
    }
    return self;
}
- (NSString *)getShengxiao:(NSDate *)date{
    NSDateComponents * comps = [[NSDateComponents alloc] init];
    NSInteger unitFlags = NSCalendarUnitDay | NSCalendarUnitWeekday | NSCalendarUnitMonth | NSCalendarUnitYear;
    comps = [self.gregorianCalendar components:unitFlags fromDate:date];
    NSInteger year = [comps year];
    NSInteger index = (year - 4)%12;
    return self.arrShengxiao[index];
}

-(NSString *)getAstroWithMonth:(NSInteger)m day:(NSInteger)d{
    
    NSString *astroString = @"魔羯水瓶双鱼白羊金牛双子巨蟹狮子处女天秤天蝎射手魔羯";
    
    NSString *astroFormat = @"102123444543";
    
    NSString *result;
    
    if (m<1||m>12||d<1||d>31){
        
        return @"错误日期格式!";
        
    }
    
    if(m==2 && d>29)
        
    {
        
        return @"错误日期格式!!";
        
    }else if(m==4 || m==6 || m==9 || m==11) {
        
        if (d>30) {
            
            return @"错误日期格式!!!";
            
        }
        
    }
    
    result=[NSString stringWithFormat:@"%@",[astroString substringWithRange:NSMakeRange(m*2-(d < [[astroFormat substringWithRange:NSMakeRange((m-1), 1)] intValue] - (-19))*2,2)]];
    
    return [result stringByAppendingString:@"座"];
    
}

-(NSArray *)getTiDi:(NSDate *)date
{
    return [self tianDiDay:date];
}
- (NSArray *)tianDiDay:(NSDate *)time{
    NSDateComponents * comps = [[NSDateComponents alloc] init];
    NSInteger unitFlags = NSCalendarUnitHour | NSCalendarUnitDay | NSCalendarUnitWeekday | NSCalendarUnitMonth | NSCalendarUnitYear;
    comps = [self.gregorianCalendar components:unitFlags fromDate:time];
    NSInteger days = [comps day];
    NSInteger month = [comps month];
    NSInteger year = [comps year];
    NSInteger hour = [comps hour];
    Solar* l = [Solar fromDateWithDate:time];
    Lunar* lunar = [Lunar fromSolarWithSolar:l];
    
    NSInteger last_year = year%100;
    NSInteger first_year = year / 100;
    NSInteger indexTian = (year - 3) % 10; //天
    NSInteger indexDi = (year - 3) % 12;    //地
    NSString * yearOfTianDi = [NSString stringWithFormat:@"%@%@", [self.tian valueForKey:[NSString stringWithFormat:@"%ld",indexTian]], [self.di valueForKey:[NSString stringWithFormat:@"%ld", indexDi]]];
    //日的天
    int g = (int) (4 * first_year + (first_year / 4) + 5 * last_year + (last_year / 4) + (3 * (month + 1) / 5) + days - 3);
    g = g % 10;
    //日的地
    int i = 0;
    if (month % 2 == 0) {
        i = 6;
    }
    int z = (int) (8 * first_year + (first_year / 4) + 5 * last_year + (last_year / 4)
                   + (3 * (month + 1) / 5) + days + 7 + i);
    z = z % 12;
    NSString * c = [NSString stringWithFormat:@"%@%@", [self.tian valueForKey:[NSString stringWithFormat:@"%d", g]], [self.di valueForKey:[NSString stringWithFormat:@"%d", z]]];//得到日的天干地支
    //计算月的天干地支
    NSString *strMonth = [NSString stringWithFormat:@"%ld月", month];
    NSString *tianGanOfYear = [self.tian valueForKey:[NSString stringWithFormat:@"%ld", indexTian]]; //获取年的天干
    //判断输入日期年的天干对应月地支的位置
    NSInteger indexOfmonth = [self indexOfmonthTable:tianGanOfYear];
    
    if ([self.monthTable.allKeys containsObject:strMonth]) {
        NSDictionary *stmpMonth = [(NSDictionary *) self.monthTable valueForKey:strMonth];
        
        NSString * yearTD = yearOfTianDi;
        NSString * monthTD = [stmpMonth valueForKey:[NSString stringWithFormat:@"%ld", indexOfmonth]];
        NSString * dayTD = [self datTD:comps];
        NSString * hourTD = [self shitiandi:hour dtg:[dayTD substringToIndex:1]];
        return @[yearTD,monthTD,dayTD,hourTD];
    } else {
        return nil;
    }
}



- (NSString*)datTD:(NSDateComponents*)datecom
{
    NSArray* tg_num = @[@"甲", @"乙", @"丙" , @"丁" ,@"戊" ,
                                @"己", @"庚", @"辛", @"壬", @"癸"];

    NSArray* dz_num = @[@"子", @"丑", @"寅", @"卯", @"辰", @"巳",
                        @"午", @"未", @"申", @"酉", @"戌", @"亥" ];
    int C_num = (int)datecom.year/100;
        int Y_num =  datecom.year % 100;
        int M_num ;
        if (datecom.month == 1) {
            M_num = 13;
            Y_num -=1;
        }else if(datecom.month == 2){
            M_num = 14;
            Y_num -=1;
        }else{
            M_num = (int)datecom.month;
        }
        int js = (datecom.month%2) ? 0 : 6;
        int G = 4*C_num + (C_num / 4) + 5*Y_num + (Y_num / 4) + (3 * (M_num + 1) / 5) + (int)datecom.day - 3 -1;
        int Z = 8*C_num + (C_num / 4) + 5*Y_num + (Y_num/ 4) + (3 * (M_num + 1) / 5) + (int)datecom.day +7 + js -1;
        
        
        NSString *d_tg = tg_num[G%10];
        NSString *d_dz = dz_num[Z%12];
        NSString *d = [d_tg stringByAppendingString:d_dz]; ;
        
    return  d;
}

- (NSString*)shitiandi:(int)hour dtg:(NSString*)d_tg
{

NSArray* _tg_num = @[@"甲", @"乙", @"丙" , @"丁" ,@"戊" ,
                            @"己", @"庚", @"辛", @"壬", @"癸"];

NSArray* dz_num = @[@"子", @"丑", @"寅", @"卯", @"辰", @"巳",
                    @"午", @"未", @"申", @"酉", @"戌", @"亥" ];
    int h_inx = (hour+1)/ 2;
        h_inx = h_inx>11?0:h_inx;
        NSString * h_dz = dz_num[h_inx];
        //日天干
        
        NSString *h_tg = @"";
        if ([@"甲己" containsString:d_tg]) {
            int tg_indx = h_inx % 10;
            h_tg = _tg_num[tg_indx];
        }else if([@"乙庚"containsString:d_tg]){
            int tg_indx = (h_inx +2) % 10;
            h_tg = _tg_num[tg_indx];
        }else if([@"丙辛"containsString:d_tg]){
            int tg_indx = (h_inx +4) % 10;
            h_tg = _tg_num[tg_indx];
        }else if([@"丁壬"containsString:d_tg]){
            int tg_indx = (h_inx +6) % 10;
            h_tg = _tg_num[tg_indx];
        }else if([@"戊癸"containsString:d_tg]){
            int tg_indx = (h_inx +8) % 10;
            h_tg = _tg_num[tg_indx];
        }
        NSString *h = [h_tg stringByAppendingString:h_dz];
    return h;
}
////将天干、地支数组添加到Map集合
-(void)tianDiPut:(NSMutableDictionary *)tiandi arr:(NSArray *)arr{
    for (int i = 0; i < arr.count; i++) {
            if (i != arr.count - 1) {
                [tiandi setValue:arr[i] forKey:[NSString stringWithFormat:@"%d", (i + 1)]];
            } else {
                [tiandi setValue:arr[arr.count - 1] forKey:@"0"];
            }
        }
}
-(NSMutableDictionary *)tian{
    if (!_tian) {
        _tian = [[NSMutableDictionary alloc] init];
        [self tianDiPut:_tian arr:self.arrTian];
    }
    return _tian;
}
-(NSMutableDictionary *)di{
    if (!_di) {
        _di = [[NSMutableDictionary alloc] init];
        [self tianDiPut:_di arr:self.arrDi];
    }
    return _di;
}

//月地支表
- (NSDictionary *)monthTable{
    NSArray <NSArray *>*arr = @[
                                @[@"丙寅", @"戊寅", @"庚寅", @"壬寅", @"甲寅"],
                                @[@"丁卯", @"己卯", @"辛卯", @"癸卯", @"乙卯"],
                                @[@"戊辰", @"庚辰", @"壬辰", @"甲辰", @"丙辰"],
                                @[@"己巳", @"辛巳", @"癸巳", @"乙巳", @"丁巳"],
                                @[@"庚午", @"壬午", @"甲午", @"丙午", @"戊午"],
                                @[@"辛未", @"癸未", @"乙未", @"丁未", @"己未"],
                                @[@"壬申", @"甲申", @"丙申", @"戊申", @"庚申"],
                                @[@"癸酉", @"乙酉", @"丁酉", @"己酉", @"辛酉"],
                                @[@"甲戌", @"丙戌", @"戊戌", @"庚戌", @"壬戌"],
                                @[@"乙亥", @"丁亥", @"己亥", @"辛亥", @"癸亥"],
                                @[@"丙子", @"戊子", @"庚子", @"壬子", @"甲子"],
                                @[@"丁丑", @"己丑", @"辛丑", @"癸丑", @"乙丑"]
                                ];
    NSArray *monthNum = @[@"1月",@"2月", @"3月", @"4月", @"5月", @"6月", @"7月", @"8月", @"9月", @"10月", @"11月", @"12月" ];
    NSMutableDictionary *map = [[NSMutableDictionary alloc] init];
    for (int i = 0; i < arr.count; i++) {
        NSMutableDictionary *map1 = [[NSMutableDictionary alloc] init];
        for (int j = 0; j < arr[i].count; j++) {
            
            [map1 setValue:arr[i][j] forKey:[NSString stringWithFormat:@"%d", (j + 1)]];
        }
        [map setValue:map1 forKey:monthNum[i]];
    }
    return map;
}


//年的天干对应月地支的位置
- (NSInteger)indexOfmonthTable:(NSString *)tianGanOfYear{
    NSArray *tmp = @[@"", @"甲己", @"乙庚", @"丙辛", @"丁壬", @"戊癸"];
    int num = 0;
    for (int i = 0; i < tmp.count; i++) {
        if ([tmp[i] rangeOfString:tianGanOfYear].location !=NSNotFound) {
            num = i;
        }
    }
    return num;
}

-(NSArray *)arrTian{
    if (!_arrTian) {
        _arrTian = @[@"甲", @"乙", @"丙", @"丁", @"戊", @"己", @"庚", @"辛", @"壬", @"癸"];
    }
    return _arrTian;
}
-(NSArray *)arrDi{
    if (!_arrDi) {
        _arrDi = @[@"子", @"丑", @"寅", @"卯", @"辰", @"巳", @"午", @"未", @"申", @"酉", @"戌", @"亥"];
    }
    return _arrDi;
}
-(NSArray *)arrShengxiao{
    if (!_arrShengxiao) {
        _arrShengxiao = @[@"鼠", @"牛", @"虎", @"兔", @"龙", @"蛇", @"马", @"羊", @"猴", @"鸡", @"狗", @"猪"];
    }
    return _arrShengxiao;
}

- (BOOL)ifrun:(int)run
{
    if(run%400==0)
    {
        return 1;
     
    }
     
    if(run%100==0)
    {
     
        return 0;
     
    }
     
    if(run%4==0)
    {
     
        return 1;
     
    }
         return 0;
}

- (NSString*)conventLnarWith:(NSDate*)date
{
    
    NSString* dateStr = [NSDate br_stringFromDate:date dateFormat:@"yyyy MM dd HH"];
    Solar* l = [Solar fromDateWithDate:date];
    int i = [[dateStr componentsSeparatedByString:@" "][3] intValue];
    Lunar* lunar = [Lunar fromSolarWithSolar:l];
    NSArray* arr = @[@"丑时",@"寅时",@"卯时",@"辰时",@"巳时",@"午时",@"末时",@"申时",@"酉时",@"戌时",@"亥时",@"子时"];
    if(i==0)
    {
        i=24;
    }
    int index = (i-1)/2;
    
    
    return  [[self formatlunarWithYear:lunar.year AndMonth:lunar.month AndDay:lunar.day]stringByAppendingFormat:@"%@",arr[index]];
}

- (NSString *)formatlunarWithYear:(int)year AndMonth:(int)month AndDay:(int)day
{
    NSArray *chineseMonths=[NSArray arrayWithObjects:
                            @"正月", @"二月", @"三月", @"四月", @"五月", @"六月", @"七月", @"八月",
                            @"九月", @"十月", @"冬月", @"腊月", nil];
    NSArray *chineseDays=[NSArray arrayWithObjects:
                          @"初一", @"初二", @"初三", @"初四", @"初五", @"初六", @"初七", @"初八", @"初九", @"初十",
                          @"十一", @"十二", @"十三", @"十四", @"十五", @"十六", @"十七", @"十八", @"十九", @"二十",
                          @"廿一", @"廿二", @"廿三", @"廿四", @"廿五", @"廿六", @"廿七", @"廿八", @"廿九", @"三十",  nil];

    return [NSString stringWithFormat:@"%d年%@%@", year, chineseMonths[month - 1], chineseDays[day - 1]];
}

- (NSString*)zhuantiangan:(NSString*)tiangan
{
    if([tiangan isEqual:@"甲"]||[tiangan isEqual:@"乙"])
    {
        return @"木";
    }
    if([tiangan isEqual:@"丙"]||[tiangan isEqual:@"丁"])
    {
        return @"火";
    }
    if([tiangan isEqual:@"戊"]||[tiangan isEqual:@"己"])
    {
        return @"土";
    }
    if([tiangan isEqual:@"庚"]||[tiangan isEqual:@"辛"])
    {
        return @"金";
    }
    if([tiangan isEqual:@"壬"]||[tiangan isEqual:@"癸"])
    {
        return @"水";
    }
    return @"";
}

- (NSString*)zhuandizhi:(NSString*)dizhi
{
    if([dizhi isEqual:@"寅"]||[dizhi isEqual:@"卯"])
    {
        return @"木";
    }
    if([dizhi isEqual:@"巳"]||[dizhi isEqual:@"午"])
    {
        return @"火";
    }
    if([dizhi isEqual:@"丑"]||[dizhi isEqual:@"辰"]||[dizhi isEqual:@"未"]||[dizhi isEqual:@"戌"])
    {
        return @"土";
    }
    if([dizhi isEqual:@"申"]||[dizhi isEqual:@"酉"])
    {
        return @"金";
    }
    if([dizhi isEqual:@"子"]||[dizhi isEqual:@"亥"])
    {
        return @"水";
    }
    return @"";
}


@end

static int lunar_month_days[] =
        {
                1887, 0x1694, 0x16aa, 0x4ad5, 0xab6, 0xc4b7, 0x4ae, 0xa56, 0xb52a,
                0x1d2a, 0xd54, 0x75aa, 0x156a, 0x1096d, 0x95c, 0x14ae, 0xaa4d, 0x1a4c, 0x1b2a, 0x8d55, 0xad4, 0x135a, 0x495d,
                0x95c, 0xd49b, 0x149a, 0x1a4a, 0xbaa5, 0x16a8, 0x1ad4, 0x52da, 0x12b6, 0xe937, 0x92e, 0x1496, 0xb64b, 0xd4a,
                0xda8, 0x95b5, 0x56c, 0x12ae, 0x492f, 0x92e, 0xcc96, 0x1a94, 0x1d4a, 0xada9, 0xb5a, 0x56c, 0x726e, 0x125c,
                0xf92d, 0x192a, 0x1a94, 0xdb4a, 0x16aa, 0xad4, 0x955b, 0x4ba, 0x125a, 0x592b, 0x152a, 0xf695, 0xd94, 0x16aa,
                0xaab5, 0x9b4, 0x14b6, 0x6a57, 0xa56, 0x1152a, 0x1d2a, 0xd54, 0xd5aa, 0x156a, 0x96c, 0x94ae, 0x14ae, 0xa4c,
                0x7d26, 0x1b2a, 0xeb55, 0xad4, 0x12da, 0xa95d, 0x95a, 0x149a, 0x9a4d, 0x1a4a, 0x11aa5, 0x16a8, 0x16d4,
                0xd2da, 0x12b6, 0x936, 0x9497, 0x1496, 0x1564b, 0xd4a, 0xda8, 0xd5b4, 0x156c, 0x12ae, 0xa92f, 0x92e, 0xc96,
                0x6d4a, 0x1d4a, 0x10d65, 0xb58, 0x156c, 0xb26d, 0x125c, 0x192c, 0x9a95, 0x1a94, 0x1b4a, 0x4b55, 0xad4,
                0xf55b, 0x4ba, 0x125a, 0xb92b, 0x152a, 0x1694, 0x96aa, 0x15aa, 0x12ab5, 0x974, 0x14b6, 0xca57, 0xa56, 0x1526,
                0x8e95, 0xd54, 0x15aa, 0x49b5, 0x96c, 0xd4ae, 0x149c, 0x1a4c, 0xbd26, 0x1aa6, 0xb54, 0x6d6a, 0x12da, 0x1695d,
                0x95a, 0x149a, 0xda4b, 0x1a4a, 0x1aa4, 0xbb54, 0x16b4, 0xada, 0x495b, 0x936, 0xf497, 0x1496, 0x154a, 0xb6a5,
                0xda4, 0x15b4, 0x6ab6, 0x126e, 0x1092f, 0x92e, 0xc96, 0xcd4a, 0x1d4a, 0xd64, 0x956c, 0x155c, 0x125c, 0x792e,
                0x192c, 0xfa95, 0x1a94, 0x1b4a, 0xab55, 0xad4, 0x14da, 0x8a5d, 0xa5a, 0x1152b, 0x152a, 0x1694, 0xd6aa,
                0x15aa, 0xab4, 0x94ba, 0x14b6, 0xa56, 0x7527, 0xd26, 0xee53, 0xd54, 0x15aa, 0xa9b5, 0x96c, 0x14ae, 0x8a4e,
                0x1a4c, 0x11d26, 0x1aa4, 0x1b54, 0xcd6a, 0xada, 0x95c, 0x949d, 0x149a, 0x1a2a, 0x5b25, 0x1aa4, 0xfb52,
                0x16b4, 0xaba, 0xa95b, 0x936, 0x1496, 0x9a4b, 0x154a, 0x136a5, 0xda4, 0x15ac
        };

static int solar_1_1[] =
        {
                1887, 0xec04c, 0xec23f, 0xec435, 0xec649, 0xec83e, 0xeca51, 0xecc46, 0xece3a,
                0xed04d, 0xed242, 0xed436, 0xed64a, 0xed83f, 0xeda53, 0xedc48, 0xede3d, 0xee050, 0xee244, 0xee439, 0xee64d,
                0xee842, 0xeea36, 0xeec4a, 0xeee3e, 0xef052, 0xef246, 0xef43a, 0xef64e, 0xef843, 0xefa37, 0xefc4b, 0xefe41,
                0xf0054, 0xf0248, 0xf043c, 0xf0650, 0xf0845, 0xf0a38, 0xf0c4d, 0xf0e42, 0xf1037, 0xf124a, 0xf143e, 0xf1651,
                0xf1846, 0xf1a3a, 0xf1c4e, 0xf1e44, 0xf2038, 0xf224b, 0xf243f, 0xf2653, 0xf2848, 0xf2a3b, 0xf2c4f, 0xf2e45,
                0xf3039, 0xf324d, 0xf3442, 0xf3636, 0xf384a, 0xf3a3d, 0xf3c51, 0xf3e46, 0xf403b, 0xf424e, 0xf4443, 0xf4638,
                0xf484c, 0xf4a3f, 0xf4c52, 0xf4e48, 0xf503c, 0xf524f, 0xf5445, 0xf5639, 0xf584d, 0xf5a42, 0xf5c35, 0xf5e49,
                0xf603e, 0xf6251, 0xf6446, 0xf663b, 0xf684f, 0xf6a43, 0xf6c37, 0xf6e4b, 0xf703f, 0xf7252, 0xf7447, 0xf763c,
                0xf7850, 0xf7a45, 0xf7c39, 0xf7e4d, 0xf8042, 0xf8254, 0xf8449, 0xf863d, 0xf8851, 0xf8a46, 0xf8c3b, 0xf8e4f,
                0xf9044, 0xf9237, 0xf944a, 0xf963f, 0xf9853, 0xf9a47, 0xf9c3c, 0xf9e50, 0xfa045, 0xfa238, 0xfa44c, 0xfa641,
                0xfa836, 0xfaa49, 0xfac3d, 0xfae52, 0xfb047, 0xfb23a, 0xfb44e, 0xfb643, 0xfb837, 0xfba4a, 0xfbc3f, 0xfbe53,
                0xfc048, 0xfc23c, 0xfc450, 0xfc645, 0xfc839, 0xfca4c, 0xfcc41, 0xfce36, 0xfd04a, 0xfd23d, 0xfd451, 0xfd646,
                0xfd83a, 0xfda4d, 0xfdc43, 0xfde37, 0xfe04b, 0xfe23f, 0xfe453, 0xfe648, 0xfe83c, 0xfea4f, 0xfec44, 0xfee38,
                0xff04c, 0xff241, 0xff436, 0xff64a, 0xff83e, 0xffa51, 0xffc46, 0xffe3a, 0x10004e, 0x100242, 0x100437,
                0x10064b, 0x100841, 0x100a53, 0x100c48, 0x100e3c, 0x10104f, 0x101244, 0x101438, 0x10164c, 0x101842, 0x101a35,
                0x101c49, 0x101e3d, 0x102051, 0x102245, 0x10243a, 0x10264e, 0x102843, 0x102a37, 0x102c4b, 0x102e3f, 0x103053,
                0x103247, 0x10343b, 0x10364f, 0x103845, 0x103a38, 0x103c4c, 0x103e42, 0x104036, 0x104249, 0x10443d, 0x104651,
                0x104846, 0x104a3a, 0x104c4e, 0x104e43, 0x105038, 0x10524a, 0x10543e, 0x105652, 0x105847, 0x105a3b, 0x105c4f,
                0x105e45, 0x106039, 0x10624c, 0x106441, 0x106635, 0x106849, 0x106a3d, 0x106c51, 0x106e47, 0x10703c, 0x10724f,
                0x107444, 0x107638, 0x10784c, 0x107a3f, 0x107c53, 0x107e48
        };

int GetBitInt(int data, int length, int shift) {
    return (data & (((1 << length) - 1) << shift)) >> shift;
}

//WARNING: Dates before Oct. 1582 are inaccurate
long SolarToInt(int y, int m, int d) {
    m = (m + 9) % 12;
    y = y - m / 10;
    return 365 * y + y / 4 - y / 100 + y / 400 + (m * 306 + 5) / 10 + (d - 1);
}



