//
//  TMYearViewController.m
//  TimeMachine
//
//  Created by fs0011 on 2025/3/3.
//

#import "TMYearViewController.h"
#import <BRPickerView/BRPickerView.h>
#import <BRPickerView/BRAddressPickerView.h>
#import "UIButton+CenterImageAndTitle.h"
#import "TMZhanbuController.h"
@interface TMYearViewController ()
@property NSDate* choosedate;
@property NSInteger sex;
@property NSString* choosecity;
@property NSString* choosework;
@property UIButton* startTest;
@property NSInteger chooseWorkIndex;
@end

@implementation TMYearViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.view.backgroundColor = [UIColor colorWithHexString:@"#040412" alpha:1];
    [self initBackButton];
    [self initUI];
}

- (void)initUI
{
    UILabel* ti1 = [UILabel createLabelWithTitle:local(@"请输入您的个人信息") textColor:[[UIColor whiteColor]colorWithAlphaComponent:0.87] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:20*scaleX weight:UIFontWeightMedium]];
    [self.view addSubview:ti1];
    [ti1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(36*scaleX);
        make.top.mas_equalTo(kStatusBarHeight + 50 + 34*scaleX);
    }];
    
    UILabel* detail1 = [UILabel createLabelWithTitle:local(@"请输入您的出生日期") textColor:[[UIColor whiteColor]colorWithAlphaComponent:0.6] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:16*scaleX]];
    [self.view addSubview:detail1];
    [detail1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(ti1.mas_bottom).offset(15*scaleX);
    }];
    
    
    NSDate* dayDate = [NSDate date];
    NSDateFormatter* f1 = [NSDateFormatter new];
    [f1 setDateFormat:@"yyyy年MM月dd日"];
    NSDateFormatter* f2 = [NSDateFormatter new];
    [f2 setDateFormat:@"HH：mm"];
    self.choosedate = dayDate;
    UIButton* chooseymd = [UIButton createButtonWithTitle:[f1 stringFromDate:dayDate] color:[UIColor whiteColor] font:standMedlumFont];
    chooseymd.layer.cornerRadius = 10;
    chooseymd.layer.masksToBounds = YES;
    chooseymd.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseymd];
    [chooseymd mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40*scaleX);
        make.top.mas_equalTo(detail1.mas_bottom).offset(10*scaleX);
        make.width.mas_equalTo(140*scaleX);
        make.height.mas_equalTo(42*scaleX);
    }];
    
    
    UIButton* chooseHm = [UIButton createButtonWithTitle:[f2 stringFromDate:dayDate] color:[UIColor whiteColor] font:standMedlumFont];
    chooseHm.layer.cornerRadius = 10;
    chooseHm.layer.masksToBounds = YES;
    chooseHm.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseHm];
    [chooseHm mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-40*scaleX);
        make.left.mas_equalTo(chooseymd.mas_right).offset(19*scaleX);
        make.top.mas_equalTo(detail1.mas_bottom).offset(10*scaleX);
        make.height.mas_equalTo(42*scaleX);
    }];
    
    [chooseymd bk_whenTapped:^{
        [self updateChooseDate:chooseymd :chooseHm];
    }];
    [chooseHm bk_whenTapped:^{
        [self updateChooseDate:chooseymd :chooseHm];
    }];
    
    
    UILabel* detail2 = [UILabel createLabelWithTitle:local(@"性别") textColor:[[UIColor whiteColor]colorWithAlphaComponent:0.6] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:16*scaleX]];
    [self.view addSubview:detail2];
    [detail2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(chooseymd.mas_bottom).offset(15*scaleX);
    }];
    
    
    UIButton* chooseSex0 = [UIButton createButtonWithNormalImageName:@"性别未选" normalTitle:local(@"男") normalColor:[UIColor whiteColor] seletedName:@"性别选中" seletedTitle:local(@"男") seletColor:[UIColor whiteColor] font:standMedlumFont];
    chooseSex0.layer.cornerRadius = 10;
    chooseSex0.layer.masksToBounds = YES;
    chooseSex0.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseSex0];
    [chooseSex0 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40*scaleX);
        make.width.mas_equalTo(70*scaleX);
        make.height.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(detail2.mas_bottom).offset(10*scaleX);
    }];
    chooseSex0.selected = YES;
    [self.view layoutIfNeeded];
    [chooseSex0 setImagePositionWithType:SSImagePositionTypeLeft spacing:6*scaleX];
    
    UIButton* chooseSex1 = [UIButton createButtonWithNormalImageName:@"性别未选" normalTitle:local(@"女") normalColor:[UIColor whiteColor] seletedName:@"性别选中" seletedTitle:local(@"女") seletColor:[UIColor whiteColor] font:standMedlumFont];
    chooseSex1.layer.cornerRadius = 10;
    chooseSex1.layer.masksToBounds = YES;
    chooseSex1.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseSex1];
    [chooseSex1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(chooseSex0.mas_right).offset(19*scaleX);
        make.width.mas_equalTo(70*scaleX);
        make.height.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(detail2.mas_bottom).offset(10*scaleX);
    }];
    [self.view layoutIfNeeded];
    [chooseSex1 setImagePositionWithType:SSImagePositionTypeLeft spacing:6*scaleX];
    [chooseSex0 bk_whenTapped:^{
        chooseSex0.selected = YES;
        chooseSex1.selected = NO;
        self.sex = 0;
    }];
    [chooseSex1 bk_whenTapped:^{
        chooseSex1.selected = YES;
        chooseSex0.selected = NO;
        self.sex = 1;
    }];
    
    UILabel* detail3 = [UILabel createLabelWithTitle:local(@"请输入您的出生地点") textColor:[[UIColor whiteColor]colorWithAlphaComponent:0.6] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:16*scaleX]];
    [self.view addSubview:detail3];
    [detail3 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(chooseSex0.mas_bottom).offset(15*scaleX);
    }];
    
    UIButton* chooseCity = [UIButton createButtonWithTitle:local(@"出生地点") color:[UIColor whiteColor] font:standMedlumFont];
    chooseCity.layer.cornerRadius = 10;
    chooseCity.layer.masksToBounds = YES;
    chooseCity.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseCity];
    [chooseCity mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40*scaleX);
        make.right.mas_equalTo(-40*scaleX);
        make.height.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(detail3.mas_bottom).offset(10*scaleX);
    }];
    [chooseCity bk_whenTapped:^{
        [BRAddressPickerView showAddressPickerWithMode:BRAddressPickerModeArea selectIndexs:@[@0, @0] isAutoSelect:NO resultBlock:^(BRProvinceModel * _Nullable province, BRCityModel * _Nullable city, BRAreaModel * _Nullable area) {
            NSMutableString* muti = [NSMutableString new];
            if(![province.name isEqualToString:@""])
            {
                [muti appendFormat:@"%@",province.name];
            }
            if(![city.name isEqualToString:@""])
            {
                [muti appendFormat:@"%@",city.name];
            }
            if(![area.name isEqualToString:@""])
            {
                [muti appendFormat:@"%@",area.name];
            }
            self.choosecity = muti;
            [chooseCity setTitle:self.choosecity forState:0];
            if (self.choosework) {
                self.startTest.enabled = YES;
                self.startTest.alpha = 1;
            }
        }];
    }];
    
    
    UILabel* detail4 = [UILabel createLabelWithTitle:local(@"选择您的职业") textColor:[[UIColor whiteColor]colorWithAlphaComponent:0.6] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:16*scaleX]];
    [self.view addSubview:detail4];
    [detail4 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(chooseCity.mas_bottom).offset(15*scaleX);
    }];
    
    UIButton* chooseWork = [UIButton createButtonWithTitle:local(@"职业") color:[UIColor whiteColor] font:standMedlumFont];
    chooseWork.layer.cornerRadius = 10;
    chooseWork.layer.masksToBounds = YES;
    chooseWork.backgroundColor =  [UIColor colorWithHexString:@"#11111D" alpha:1];
    [self.view addSubview:chooseWork];
    [chooseWork mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(40*scaleX);
        make.right.mas_equalTo(-40*scaleX);
        make.height.mas_equalTo(42*scaleX);
        make.top.mas_equalTo(detail4.mas_bottom).offset(10*scaleX);
    }];
    [chooseWork bk_whenTapped:^{
        
        NSArray *occupations = @[
            @"其他",
            @"教师",
            @"医生",
            @"工程师",
            @"护士",
            @"律师",
            @"会计师",
            @"程序员",
            @"设计师",
            @"销售代表",
            @"市场营销",
            @"建筑师",
            @"作家/编辑",
            @"厨师",
            @"警察",
            @"司机",
            @"学生",
            @"自由职业者",
            @"企业家",
            @"行政人员",
            @"客服专员",
            @"退休人员",
            @"科研人员"
        ];
        
        [BRTextPickerView showPickerWithTitle:local(@"选择职业") dataSourceArr:occupations selectIndex:self.chooseWorkIndex resultBlock:^(BRTextModel * _Nullable model, NSInteger index) {
            [chooseWork setTitle:model.text forState:0];
            self.chooseWorkIndex = index;
            self.choosework = model.text;
            if (self.choosecity) {
                self.startTest.enabled = YES;
                self.startTest.alpha = 1;
            }
        }];
    }];
    
    
    UIButton* startTest = [UIButton createButtonWithTitle:local(@"开始测试") color:darkTextColor font:[UIFont systemFontOfSize:22*scaleX weight:UIFontWeightMedium]];
    [self.view addSubview:startTest];
    startTest.backgroundColor = [UIColor whiteColor];
    startTest.layer.cornerRadius = 40*0.5*scaleX;
    startTest.layer.masksToBounds = YES;
    [startTest mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.width.mas_equalTo(160*scaleX);
        make.height.mas_equalTo(40*scaleX);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin).offset(-64*scaleX);
    }];
    startTest.enabled = NO;
    self.startTest = startTest;
    self.startTest.alpha = 0.3;
    [startTest bk_whenTapped:^{
        TMZhanbuController* vc = [TMZhanbuController new];
        vc.type = 0;
        vc.date = self.choosedate;
        vc.sex = self.sex;
        vc.work = self.choosework;
        [self.navigationController pushViewController:vc animated:YES];
    }];
}


- (void)updateChooseDate:(UIButton*)ymd :(UIButton*)Hm
{
    NSDate* dayDate = [NSDate date];
    NSDateFormatter* f1 = [NSDateFormatter new];
    [f1 setDateFormat:@"yyyy年MM月dd日"];
    NSDateFormatter* f2 = [NSDateFormatter new];
    [f2 setDateFormat:@"HH：mm"];
    NSDateFormatter* f3 = [NSDateFormatter new];
    [f3 setDateFormat:@"yyyy-MM-dd HH:mm"];
    
    [BRDatePickerView showDatePickerWithMode:BRDatePickerModeYMDHM title:local(@"选择时间") selectValue:[f3 stringFromDate:self.choosedate] resultBlock:^(NSDate * _Nullable selectDate, NSString * _Nullable selectValue) {
        self.choosedate = selectDate;
        [ymd setTitle:[f1 stringFromDate:self.choosedate] forState:0];
        [Hm setTitle:[f2 stringFromDate:self.choosedate] forState:0];
    }];
}
/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
