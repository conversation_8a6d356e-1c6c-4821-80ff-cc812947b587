//
//  TMZhanbuController.m
//  TimeMachine
//
//  Created by fs0011 on 2025/3/6.
//

#import "TMZhanbuController.h"
#import "TiDi.h"
#import <BRPickerView/NSDate+BRPickerView.h>
#import <BlocksKit/NSArray+BlocksKit.h>
#import "DeepSeekStreamClient.h"
#import "OSSUploadImageTool.h"
@interface TMZhanbuController ()
//typ0
@property NSInteger chooseIndex;
@property UIScrollView* baogaoSC;
@property UIScrollView* baziSc;
@property UIScrollView* liunianSc;
@property UIScrollView* meiyueSc;
@property UIScrollView* zongheSc;
@property NSMutableString* bazi;
@property NSMutableString* liunian;
@property NSMutableString* meiyue;
@property NSMutableString* zonghe;
@property UILabel* baziLa;
@property UILabel* liunianLa;
@property UILabel* meiyueLa;
@property UILabel* zongheLa;
@property NSString* baziString;
@property BOOL isbaziEnd;
@property BOOL isliunianEnd;
@property BOOL ismeiyueEnd;
@property BOOL iszongheEnd;

@property DeepSeekStreamClient* client;
@property NSString* requestID1;
@property NSString* requestID2;
@property NSString* requestID3;
@property NSString* requestID4;
//

//type1
@property UIScrollView* handsSc;
@property NSMutableString* hands;
@property UILabel* handsLa;
@property NSString* requestID5;
//
@end

@implementation TMZhanbuController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor colorWithHexString:@"#040412" alpha:1];
    [self initBackButton];
    
    if(self.type==0)
    {
        [self creatTitleLabel:local(@"运势解析报告")];
        [self initYear];
    }    // Do any additional setup after loading the view.
    
    else
    {
        [self creatTitleLabel:local(@"手掌解析报告")];
        [self initHands];
//        if(!self.client)
//        {
//            DeepSeekStreamClient *client = [[DeepSeekStreamClient alloc] initWithAPIKey:@"************************************"];
//            self.client = client;
//        }
//        [[OSSUploadImageTool shared]uploadImage:self.seletImage completion:^(NSString * _Nonnull url, NSError * _Nonnull error) {
//            [self.client sendImageStreamRequestWithMessages:@"帮我看看我的手掌，根据我的掌纹的整体特征，结合中国命理理论，告诉我，我的财富，事业，健康，人际，家庭，爱情的信息" andImageUrl:url callback:^(NSString * _Nonnull requestID, NSString * _Nonnull chunkContent, NSError * _Nonnull error) {
//                NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
//            }];
//        }];
    }
    
}

- (void)initHands
{
    
    UIImageView* im = [UIImageView new];
    im.layer.cornerRadius = 10*scaleX;
    im.layer.masksToBounds = YES;
    im.image = self.seletImage;
    [self.view addSubview:im];
    [im mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.mas_equalTo(0);
        make.top.mas_equalTo(self.tiltleLabel.mas_bottom).offset(20*scaleX);
        make.height.mas_equalTo(250*scaleX-20*2*scaleX);
        make.width.mas_equalTo(im.mas_height).multipliedBy(self.seletImage.size.width*1.0/self.seletImage.size.height);
    }];
    
    self.baogaoSC = [UIScrollView new];
    self.baogaoSC.pagingEnabled = YES;
    self.baogaoSC.showsHorizontalScrollIndicator = NO;
    self.baogaoSC.scrollEnabled = NO;
    [self.view addSubview:self.baogaoSC];
    [self.baogaoSC mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(im.mas_bottom).offset(20*scaleX);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    [self.view layoutIfNeeded];
    self.baogaoSC.contentSize = CGSizeMake(SCREEN_WIDTH, self.baogaoSC.height);
    
    UIView* wai = [UIView new];
    [self.baogaoSC addSubview:wai];
    wai.frame = CGRectMake(0,0 , SCREEN_WIDTH, self.baogaoSC.height);
    UIView * nei = [UIView new];
    nei.backgroundColor = [UIColor colorWithHexString:@"#FEF7D9" alpha:1];
    nei.layer.cornerRadius = 10*scaleX;
    nei.layer.borderWidth = 5*scaleX;
    nei.layer.borderColor = [UIColor colorWithHexString:@"#FAEABF" alpha:1].CGColor;
    [wai addSubview:nei];
    nei.frame = CGRectMake(24*scaleX, 0, SCREEN_WIDTH-24*scaleX*2, self.baogaoSC.height-90*scaleX);
    
    UIScrollView* sc = [UIScrollView new];
    sc.frame = CGRectMake(0, 0, nei.width, nei.height-50*scaleX);
    [nei addSubview:sc];
    UILabel* la = [UILabel createLabelWithTitle:@"" textColor:[UIColor colorWithHexString:@"#7C573C" alpha:1] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    la.numberOfLines = 0;
    [sc addSubview:la];
    la.frame = CGRectMake(16*scaleX,24*scaleX, nei.width-16*scaleX*2, 0);
    self.handsLa = la;
    self.handsSc = sc;
    
    UILabel* tip = [UILabel createLabelWithTitle:local(@"以上结果仅供参考。") textColor:[UIColor colorWithHexString:@"#D8CAA3" alpha:1] textAlignment:NSTextAlignmentLeft font:detailFont];
    [nei addSubview:tip];
    [tip mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16*scaleX);
        make.bottom.mas_equalTo(-24*scaleX);
    }];
    
    if(!self.client)
    {
        DeepSeekStreamClient *client = [[DeepSeekStreamClient alloc] initWithAPIKey:@"************************************"];
        self.client = client;
    }
    
    if (!self.hands) {
        
        [SVProgressHUD show];
        [[OSSUploadImageTool shared]uploadImage:self.seletImage completion:^(NSString * _Nonnull url, NSError * _Nonnull error) {
            if(!error)
            {
                [self.client sendImageStreamRequestWithMessages:@"帮我看看我的手掌，根据我的掌纹的整体特征，结合中国命理理论，告诉我，我的财富，事业，健康，人际，家庭，爱情的信息" andImageUrl:url callback:^(NSString * _Nonnull requestID, NSString * _Nonnull chunkContent, NSError * _Nonnull error) {
                    NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    
                    if (error) {
                        [SVProgressHUD showErrorWithStatus:@"获取失败，请检查网络重新获取"];
                        return;
                    }
                    
                    if (chunkContent) {
                        [SVProgressHUD dismiss];
                        if (!self.hands) {
                            self.hands = [NSMutableString string];
                        }
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.hands appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    
                                    self.handsLa.text = self.hands;
                                    self.handsLa.width = self.handsSc.width-16*scaleX*2;
                                    [self.handsLa sizeToFit];
                                    
                                    self.handsSc.contentSize = CGSizeMake(self.handsSc.width, self.handsLa.bottom+24*scaleX);
                                    [self.handsSc scrollRectToVisible:CGRectMake(0, self.handsLa.bottom-self.handsSc.height, self.handsSc.width, self.handsSc.height) animated:NO];
                                    
                                });
                            }
                        });
                        
                        
                        
                        
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                        self.isbaziEnd = YES;
                    }
                }];
            }
            else
            {
                [SVProgressHUD showErrorWithStatus:@"上传图片失败"];
            }
        }];
        // 发起请求1
       
    }
    
    
}

- (void)initYear
{
    self.title = local(@"运势解析报告");
    
    NSDateFormatter* f1 = [NSDateFormatter new];
    [f1 setDateFormat:@"yyyy年MM月dd日 HH：mm"];
    
    UILabel* gongli = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"公历：%@"),[f1 stringFromDate:self.date]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    gongli.numberOfLines = 0;
    [self.view addSubview:gongli];
    [gongli mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(18*scaleX);
        make.right.mas_equalTo(-18*scaleX);
        make.top.mas_equalTo(kStatusBarHeight + 50 + 45*scaleX);
    }];
    Solar * s = [Solar fromDateWithDate:self.date];
    Lunar * l = [Lunar fromSolarWithSolar:s];
    NSDate* lunarDate = [NSDate br_setYear:l.year month:l.month day:l.day hour:l.hour minute:l.minute second:l.second];
    UILabel* nongli = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"农历：%@年%@月%@日%@时"),[l yearInGanZhi],[l monthInGanZhi],[l dayInGanZhi],[l timeInGanZhi]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    nongli.numberOfLines = 0;
    [self.view addSubview:nongli];
    [nongli mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(18*scaleX);
        make.right.mas_equalTo(-18*scaleX);
        make.top.mas_equalTo(gongli.mas_bottom).offset(10*scaleX);
    }];
    NSArray* arrganzhi = @[[l yearInGanZhi],[l monthInGanZhi],[l dayInGanZhi],[l timeInGanZhi]];
    UILabel* bazi = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"八字：%@"),[arrganzhi componentsJoinedByString:@""]] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    [self.view addSubview:bazi];
    [bazi mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(18*scaleX);
        make.right.mas_equalTo(-18*scaleX);
        make.top.mas_equalTo(nongli.mas_bottom).offset(10*scaleX);
    }];
    self.baziString = [arrganzhi componentsJoinedByString:@""];
    
    UILabel* xingbie = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"性别：%@"),self.sex==0?@"男":@"女"] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    [self.view addSubview:xingbie];
    [xingbie mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(18*scaleX);
        make.right.mas_equalTo(-18*scaleX);
        make.top.mas_equalTo(bazi.mas_bottom).offset(10*scaleX);
    }];
    
    UILabel* zhiye = [UILabel createLabelWithTitle:[NSString stringWithFormat:local(@"职业：%@"),self.work] textColor:[UIColor whiteColor] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
    [self.view addSubview:zhiye];
    [zhiye mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(18*scaleX);
        make.right.mas_equalTo(-18*scaleX);
        make.top.mas_equalTo(xingbie.mas_bottom).offset(10*scaleX);
    }];
    [self.view layoutIfNeeded];
    
    NSArray* arr = @[local(@"八字解析"),local(@"流年运势"),local(@"每月运势"),local(@"TM_综合建议")];
    
    UIView* sleteView = [UIView new];
    sleteView.backgroundColor = [UIColor colorWithHexString:@"#D8CAA3" alpha:1];
    [self.view addSubview:sleteView];
    sleteView.width = 16*scaleX;
    sleteView.height = 2*scaleX;
    NSMutableArray<UIButton*>* mutiarr = [NSMutableArray array];
    for (NSString* choosestr in arr) {
        UIButton* btn = [UIButton createButtonWithNormalTitle:choosestr normalColor:[[UIColor whiteColor]colorWithAlphaComponent:0.65] seletedTitle:choosestr seletColor:[UIColor colorWithHexString:@"#D8CAA3" alpha:1] font:[UIFont systemFontOfSize:[local(@"TMZhanbuController_tab_font") intValue] weight:UIFontWeightMedium]];
        [self.view addSubview:btn];
        btn.frame = CGRectMake([arr indexOfObject:choosestr]*SCREEN_WIDTH*1.0/4, zhiye.bottom+45*scaleX, SCREEN_WIDTH*1.0/4, 20*scaleX);
        btn.clipsToBounds = NO;
        if ([arr indexOfObject:choosestr]==self.chooseIndex) {
            btn.selected = YES;
            sleteView.y = btn.bottom;
            sleteView.centerX = btn.centerX;
        }
        [mutiarr addObject:btn];
        [btn bk_whenTapped:^{
            NSInteger newInedx =  [arr indexOfObject:btn.titleLabel.text];
            [mutiarr bk_each:^(UIButton * _Nonnull obj) {
                obj.selected = NO;
            }];
            btn.selected = YES;
            [self.baogaoSC scrollRectToVisible:CGRectMake(newInedx*SCREEN_WIDTH, 0, SCREEN_WIDTH, self.baogaoSC.height) animated:YES];
            [UIView animateWithDuration:0.5 animations:^{
                sleteView.y = btn.bottom;
                sleteView.centerX = btn.centerX;
            }completion:^(BOOL finished) {
                
                self.chooseIndex = newInedx;
                [self switchToindex:newInedx];
            }];
            
        }];
    }
    
    self.baogaoSC = [UIScrollView new];
    self.baogaoSC.pagingEnabled = YES;
    self.baogaoSC.showsHorizontalScrollIndicator = NO;
    self.baogaoSC.scrollEnabled = NO;
    [self.view addSubview:self.baogaoSC];
    [self.baogaoSC mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.mas_equalTo(0);
        make.top.mas_equalTo(sleteView.bottom+20*scaleX);
        make.bottom.mas_equalTo(self.view.mas_bottomMargin);
    }];
    [self.view layoutIfNeeded];
    self.baogaoSC.contentSize = CGSizeMake(SCREEN_WIDTH*4, self.baogaoSC.height);
    
    for (int i=0; i<4; i++) {
        UIView* wai = [UIView new];
        [self.baogaoSC addSubview:wai];
        wai.frame = CGRectMake(i*SCREEN_WIDTH,0 , SCREEN_WIDTH, self.baogaoSC.height);
        UIView * nei = [UIView new];
        nei.backgroundColor = [UIColor colorWithHexString:@"#FEF7D9" alpha:1];
        nei.layer.cornerRadius = 10*scaleX;
        nei.layer.borderWidth = 5*scaleX;
        nei.layer.borderColor = [UIColor colorWithHexString:@"#FAEABF" alpha:1].CGColor;
        [wai addSubview:nei];
        nei.frame = CGRectMake(24*scaleX, 0, SCREEN_WIDTH-24*scaleX*2, self.baogaoSC.height-90*scaleX);
        
        UIScrollView* sc = [UIScrollView new];
        sc.frame = CGRectMake(0, 0, nei.width, nei.height-50*scaleX);
        [nei addSubview:sc];
        UILabel* la = [UILabel createLabelWithTitle:@"" textColor:[UIColor colorWithHexString:@"#7C573C" alpha:1] textAlignment:NSTextAlignmentLeft font:bigMedlumFont];
        la.numberOfLines = 0;
        [sc addSubview:la];
        la.frame = CGRectMake(16*scaleX,24*scaleX, nei.width-16*scaleX*2, 0);
        switch (i) {
            case 0:
            {
                self.baziSc = sc;
                self.baziLa = la;
            }
                break;
            case 1:
            {
                self.liunianSc = sc;
                self.liunianLa = la;
            }
                break;
            case 2:
            {
                self.meiyueSc = sc;
                self.meiyueLa = la;
            }
                break;
            case 3:
            {
                self.zongheSc = sc;
                self.zongheLa = la;
            }
                break;
            default:
                break;
        }
        
        UILabel* tip = [UILabel createLabelWithTitle:@"以上结果仅供参考。" textColor:[UIColor colorWithHexString:@"#D8CAA3" alpha:1] textAlignment:NSTextAlignmentLeft font:detailFont];
        [nei addSubview:tip];
        [tip mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.mas_equalTo(16*scaleX);
            make.bottom.mas_equalTo(-24*scaleX);
        }];
        
    }
    [self switchToindex:0];
    
    
}

- (void)switchToindex:(NSInteger)index
{
    
    
    NSString* str = [NSString stringWithFormat:@"我的八字为：%@,工作是：%@，今年为2025年，性别为%@",self.baziString,self.work,self.sex==0?@"男":@"女"];
    if(!self.client)
    {
        DeepSeekStreamClient *client = [[DeepSeekStreamClient alloc] initWithAPIKey:@"************************************"];
        self.client = client;
    }
    switch (index) {
        case 0:
        {
            if (!self.bazi) {
                
                
                
                // 发起请求1
                NSString *requestID1 = [self.client sendStreamRequestWithMessages:@[
                    @{@"role": @"user", @"content": [NSString stringWithFormat:@"%@,帮我做八字命盘解析，结合今年",str],}
                ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
                    if (error) {
                        NSLog(@"请求%@出错: %@", requestID, error);
                        [self switchToindex:0];
                        return;
                    }
                    
                    if (chunkContent) {
                        if (!self.bazi) {
                            self.bazi = [NSMutableString string];
                        }
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.bazi appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    if (self.chooseIndex==0) {
                                        self.baziLa.text = self.bazi;
                                        self.baziLa.width = self.baziSc.width-16*scaleX*2;
                                        [self.baziLa sizeToFit];
                                        
                                        self.baziSc.contentSize = CGSizeMake(self.baziSc.width, self.baziLa.bottom+24*scaleX);
                                        [self.baziSc scrollRectToVisible:CGRectMake(0, self.baziLa.bottom-self.baziSc.height, self.baziSc.width, self.baziSc.height) animated:NO];
                                    }
                                });
                            }
                        });
                        
                        
                        
                        
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                        self.isbaziEnd = YES;
                    }
                }];
                self.requestID1 = requestID1;
            }
            else
            {
                if (self.chooseIndex==0) {
                    self.baziLa.text = self.bazi;
                    self.baziLa.width = self.baziSc.width-16*scaleX*2;
                    [self.baziLa sizeToFit];
                    
                    self.baziSc.contentSize = CGSizeMake(self.baziSc.width, self.baziLa.bottom+24*scaleX);
//                    [self.baziSc scrollRectToVisible:CGRectMake(0, self.baziLa.bottom-self.baziSc.height, self.baziSc.width, self.baziSc.height) animated:NO];
                }
            }
        }
            break;
        case 1:
        {
            if (!self.liunian) {
                
                
                
                // 发起请求1
                NSString *requestID2 = [self.client sendStreamRequestWithMessages:@[
                    @{@"role": @"user", @"content": [NSString stringWithFormat:@"%@,帮我做分析流年运势，结合今年",str],}
                ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
                    if (error) {
                        NSLog(@"请求%@出错: %@", requestID, error);
                        [self switchToindex:1];
                        return;
                    }
                    
                    if (chunkContent) {
                        if (!self.liunian) {
                            self.liunian = [NSMutableString string];
                        }
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.liunian appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    if (self.chooseIndex==1) {
                                        self.liunianLa.text = self.liunian;
                                        self.liunianLa.width = self.liunianSc.width-16*scaleX*2;
                                        [self.liunianLa sizeToFit];
                                        self.liunianSc.contentSize = CGSizeMake(self.liunianSc.width, self.liunianLa.bottom+24*scaleX);
                                        [self.liunianSc scrollRectToVisible:CGRectMake(0, self.liunianLa.bottom-self.liunianSc.height, self.liunianSc.width, self.liunianSc.height) animated:NO];
                                    }
                                });
                            }
                        });
                        
                        
                        
                        
                        
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                        self.isliunianEnd = YES;
                    }
                }];
                self.requestID2 = requestID2;
            }
            else
            {
                if (self.chooseIndex==1) {
                    self.liunianLa.text = self.liunian;
                    self.liunianLa.width = self.liunianSc.width-16*scaleX*2;
                    [self.liunianLa sizeToFit];
                    self.liunianSc.contentSize = CGSizeMake(self.liunianSc.width, self.liunianLa.bottom+24*scaleX);
//                    [self.liunianSc scrollRectToVisible:CGRectMake(0, self.liunianLa.bottom-self.liunianSc.height, self.liunianSc.width, self.liunianSc.height) animated:NO];
                }
            }
        }
            break;
        case 2:
        {
            if (!self.meiyue) {
                
                
                
                // 发起请求1
                NSString *requestID3 = [self.client sendStreamRequestWithMessages:@[
                    @{@"role": @"user", @"content": [NSString stringWithFormat:@"%@,给我每个月运势提醒，结合今年",str],}
                ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
                    if (error) {
                        NSLog(@"请求%@出错: %@", requestID, error);
                        [self switchToindex:2];
                        return;
                    }
                    
                    if (chunkContent) {
                        if (!self.meiyue) {
                            self.meiyue = [NSMutableString string];
                        }
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.meiyue appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    if(self.chooseIndex==2)
                                    {
                                        self.meiyueLa.text = self.meiyue;
                                        self.meiyueLa.width = self.meiyueSc.width-16*scaleX*2;
                                        [self.meiyueLa sizeToFit];
                                        self.meiyueSc.contentSize = CGSizeMake(self.meiyueSc.width, self.meiyueLa.bottom+24*scaleX);
                                        [self.meiyueSc scrollRectToVisible:CGRectMake(0, self.meiyueLa.bottom-self.meiyueSc.height, self.meiyueSc.width, self.meiyueSc.height) animated:NO];
                                    }
                                });
                            }
                        });
                        
                        
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                        self.ismeiyueEnd = YES;
                    }
                }];
                self.requestID3 = requestID3;
            }
            else
            {
                if(self.chooseIndex==2)
                {
                    self.meiyueLa.text = self.meiyue;
                    self.meiyueLa.width = self.meiyueSc.width-16*scaleX*2;
                    [self.meiyueLa sizeToFit];
                    self.meiyueSc.contentSize = CGSizeMake(self.meiyueSc.width, self.meiyueLa.bottom+24*scaleX);
//                    [self.meiyueSc scrollRectToVisible:CGRectMake(0, self.meiyueLa.bottom-self.meiyueSc.height, self.meiyueSc.width, self.meiyueSc.height) animated:NO];
                }
            }
        }
            break;
        case 3:
        {
            if (!self.zonghe) {
                
                
                
                // 发起请求1
                NSString *requestID4 = [self.client sendStreamRequestWithMessages:@[
                    @{@"role": @"user", @"content": [NSString stringWithFormat:@"%@,根据我今年的综合运势，给予我一些综合的建议",str],}
                ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
                    if (error) {
                        NSLog(@"请求%@出错: %@", requestID, error);
                        [self switchToindex:3];
                        return;
                    }
                    
                    if (chunkContent) {
                        if (!self.zonghe) {
                            self.zonghe = [NSMutableString string];
                        }
                        
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.zonghe appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    if(self.chooseIndex==3)
                                    {
                                        self.zongheLa.text = self.zonghe;
                                        self.zongheLa.width = self.zongheSc.width-16*scaleX*2;
                                        [self.zongheLa sizeToFit];
                                        self.zongheSc.contentSize = CGSizeMake(self.zongheSc.width, self.zongheLa.bottom+24*scaleX);
                                        [self.zongheSc scrollRectToVisible:CGRectMake(0, self.zongheLa.bottom-self.zongheSc.height, self.zongheSc.width, self.zongheSc.height) animated:NO];
                                    }
                                });
                            }
                        });
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                        self.iszongheEnd = YES;
                    }
                }];
                self.requestID4 = requestID4;
            }
            else
            {
                if(self.chooseIndex==3)
                {
                    self.zongheLa.text = self.zonghe;
                    self.zongheLa.width = self.zongheSc.width-16*scaleX*2;
                    [self.zongheLa sizeToFit];
                    self.zongheSc.contentSize = CGSizeMake(self.zongheSc.width, self.zongheLa.bottom+24*scaleX);
//                    [self.zongheSc scrollRectToVisible:CGRectMake(0, self.zongheLa.bottom-self.zongheSc.height, self.zongheSc.width, self.zongheSc.height) animated:NO];
                }
            }
        }
            break;
        default:
            break;
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [self.client cancelRequestWithID:self.requestID1];
    [self.client cancelRequestWithID:self.requestID2];
    [self.client cancelRequestWithID:self.requestID3];
    [self.client cancelRequestWithID:self.requestID4];
    
    
    
    
}
/*
 #pragma mark - Navigation
 
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

- (void)backButtonClick:(UIButton *)sender
{
    [self.navigationController popToRootViewControllerAnimated:YES];
}

@end
