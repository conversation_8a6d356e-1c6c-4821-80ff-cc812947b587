

#import <Foundation/Foundation.h>
#import "TimeMachine-Swift.h"
NS_ASSUME_NONNULL_BEGIN

@interface TiDi : NSObject

+(TiDi *)sharedInstance;
//获取日期的天干地支
-(NSArray *)getTiDi:(NSDate *)date;
//获取生肖
- (NSString *)getShengxiao:(NSDate *)date;

- (NSString *)getAstroWithMonth:(NSInteger)m day:(NSInteger)d;
- (NSString*)conventLnarWith:(NSDate*)date;

- (NSString*)zhuantiangan:(NSString*)tiangan;
- (NSString*)zhuandizhi:(NSString*)dizhi;
@end

NS_ASSUME_NONNULL_END
