//
//  MainTabbarVC.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/27.
//

#import "MainTabbarVC.h"
#import "ViewController.h"
#import "MentalTestVC.h"

@interface MainTabbarVC ()

@end

@implementation MainTabbarVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    
    ViewController *home = [[ViewController alloc] init];
    UINavigationController *navi1 = [[UINavigationController alloc] initWithRootViewController:home];
    UITabBarItem *tab1 = [[UITabBarItem alloc] init];
    tab1.title = local(@"首页");
    tab1.image = [[UIImage imageNamed:@"首页-未选中"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    tab1.selectedImage = [[UIImage imageNamed:@"首页-选中"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    navi1.tabBarItem = tab1;
    [navi1.navigationBar setHidden:YES];
    
    MentalTestVC *mental = [[MentalTestVC alloc] init];
    UINavigationController *navi2 = [[UINavigationController alloc] initWithRootViewController:mental];
    UITabBarItem *tab2 = [[UITabBarItem alloc] init];
    tab2.title = local(@"心理测试");
    tab2.image = [[UIImage imageNamed:@"心理测试-未选中"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    tab2.selectedImage = [[UIImage imageNamed:@"心理测试-选中"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    navi2.tabBarItem = tab2;
    [navi2.navigationBar setHidden:YES];
    
    self.viewControllers = [NSArray arrayWithObjects:navi1,navi2,nil];
    self.tabBar.tintColor = tabbarSelectColor;
    self.tabBar.unselectedItemTintColor = tabbarUnSelectColor;
    
    if (@available(iOS 13.0, *)) {
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc] init];
        appearance.backgroundColor = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
        appearance.stackedLayoutAppearance.normal.titlePositionAdjustment = UIOffsetMake(0, 2);
        appearance.stackedLayoutAppearance.selected.titlePositionAdjustment = UIOffsetMake(0, 2);
        appearance.stackedLayoutAppearance.normal.titleTextAttributes =  @{
            NSFontAttributeName: [UIFont systemFontOfSize:10 weight:UIFontWeightSemibold],
            NSForegroundColorAttributeName: tabbarUnSelectColor
        };
        appearance.stackedLayoutAppearance.selected.titleTextAttributes =  @{
            NSFontAttributeName: [UIFont systemFontOfSize:10 weight:UIFontWeightSemibold],
            NSForegroundColorAttributeName: tabbarSelectColor
        };
        appearance.shadowImage = [MyColor createImageWithColor:[MyColor colorWithHexString:@"#F9DB92" alpha:0.15]];
        self.tabBar.standardAppearance = appearance;
    } else {
        self.tabBar.barTintColor  = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
        self.tabBar.translucent  = NO;
    }
    
    
    UINavigationBar.appearance.tintColor = tabbarSelectColor;
    
    
}

#pragma mark - Rotation Handling
 
- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
 
- (BOOL)shouldAutorotate {
    return NO;
}
 
- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}




@end
