//
//  AllFuncShowView.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/28.
//

#import "AllFuncShowView.h"

@interface AllFuncShowView ()

@property (nonatomic, strong) NSMutableArray<CardView *> *cardViews;
@property (nonatomic, strong) NSArray<NSString *> *imageNames;

@end

@implementation AllFuncShowView


- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _cardViews = [NSMutableArray array];
        _imageNames = @[@"变脸魔法",@"手相解读",@"卡通特效",@"面相测试"];
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    self.backgroundColor = [MyColor colorWithHexString:@"#000000" alpha:0.5];
    
    UIView *contentView = [[UIView alloc] initWithFrame:CGRectZero];
    contentView.backgroundColor = [MyColor colorWithHexString:@"#11111D" alpha:1.0];
    contentView.layer.cornerRadius = 16;
    contentView.layer.maskedCorners  = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;
    [self addSubview:contentView];
    [contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(@(215));
        make.bottom.equalTo(self.mas_safeAreaLayoutGuideBottom);
    }];
    
    for (NSInteger i = 0; i < self.imageNames.count;  i++) {
        CardView *view = [[CardView alloc] initWithFrame:CGRectZero];
        [view setbackImage:_imageNames[i]];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
                                              initWithTarget:self
                                              action:@selector(viewTapped:)];
         
        // 开启用户交互（UIView默认是NO，必须设置为YES）
        view.userInteractionEnabled  = YES;
         
        // 添加手势到视图
        [view addGestureRecognizer:tapGesture];
        [contentView addSubview:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(109*scaleX));
            make.height.equalTo(@(191*scaleX));
            CGFloat row = i / 2 ;
            if(i % 2 == 0) {
                make.left.equalTo(@(55*scaleX));
            } else {
                make.right.equalTo(@(-55*scaleX));
            }
            make.top.equalTo(@(((191 + 39)*row+30)*scaleX));
        }];
        
        
    }
    
}

- (void)viewTapped:(UITapGestureRecognizer *)gesture {
    
    CardView *view = (CardView*)gesture.view;
    if ([view.tagName isEqualToString:@"变脸魔法"]) {
        [self.delegate faceMagicTouch:view];
    } else if ([view.tagName isEqualToString:@"手相解读"]) {
        [self.delegate handTouch:view];
    } else if ([view.tagName isEqualToString:@"卡通特效"]) {
        [self.delegate catoonTouch:view];
    } else if ([view.tagName isEqualToString:@"面相测试"]) {
        [self.delegate timeOldTouch:view];
    }
    
}

- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
        UITouch *touch = [touches anyObject];
        CGPoint touchLocation = [touch locationInView:self];
        
        // 检查点击是否在子视图上
        for (UIView *subview in self.subviews)  {
            if (CGRectContainsPoint(subview.frame,  touchLocation)) {
                return;
            }
        }
        
        NSLog(@"父视图被点击");
        [super touchesBegan:touches withEvent:event]; // 调用父类实现
        [self removeFromSuperview];
}



@end
