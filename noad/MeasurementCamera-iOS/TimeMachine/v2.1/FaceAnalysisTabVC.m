//
//  FaceAnalysisTabVC.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/30.
//

#import "FaceAnalysisTabVC.h"

@interface FaceAnalysisTabVC ()<WMPageControllerDelegate>

@property(nonatomic,strong) NSArray<NSString*> *allTitle;

@end

@implementation FaceAnalysisTabVC

- (instancetype)init {
    if (self = [super init]) {
        self.allTitle = @[local(@"五官分析"), local(@"本年运势"),local(@"每月运势提醒"),local(@"综合建议")];
        // 设置样式为 line
        self.menuViewStyle  = WMMenuViewStyleLine;
        // 设置标题颜色等样式
        self.titleColorSelected  = [UIColor colorWithHexString:@"#D8CAA3" alpha:1.0];
        self.titleColorNormal  = [UIColor colorWithHexString:@"#FFFFFF" alpha:0.64];
        self.menuViewLayoutMode  = WMMenuViewLayoutModeCenter;
        self.progressWidth  = 20; // 下划线的宽度
        self.progressColor  = [UIColor colorWithHexString:@"#D8CAA3" alpha:1.0]; // 下划线颜色
        self.progressHeight  = 2; // 下划线高度
        self.progressViewCornerRadius  = 1; // 下划线圆角
        self.progressViewBottomSpace = 4;
        self.titleSizeNormal  = 14.0*scaleX;   // 未选中时的字号
        self.titleSizeSelected  = 14.0*scaleX; // 选中时的字号（可设置比未选中时大）
        self.itemsMargins  = @[@(24*scaleX), @(25*scaleX),@(25*scaleX), @(25*scaleX),@(24*scaleX)];
        // 2. 设置菜单视图的左右边距(左边距设置为0，因为第一个间距已经是0)
        self.menuViewContentMargin  = 0;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor blackColor];
    
}

#pragma mark - WMPageController 数据源和配置
 
// 标题数组
- (NSArray<NSString *> *)titles {
    return self.allTitle;
}
 
// 子控制器数量
- (NSInteger)numbersOfChildControllersInPageController:(WMPageController *)pageController {
    return self.titles.count;
}
 
// 子控制器实例
- (UIViewController *)pageController:(WMPageController *)pageController viewControllerAtIndex:(NSInteger)index {
    FaceAnalysisResultVC *vc1 = [[FaceAnalysisResultVC alloc] init];
    vc1.resultIndex = index;
    vc1.selectImage = self.image;
    FaceAnalysisResultVC *vc2 = [[FaceAnalysisResultVC alloc] init];
    vc2.resultIndex = index;
    vc2.selectImage = self.image;
    FaceAnalysisResultVC *vc3 = [[FaceAnalysisResultVC alloc] init];
    vc3.resultIndex = index;
    vc3.selectImage = self.image;
    FaceAnalysisResultVC *vc4 = [[FaceAnalysisResultVC alloc] init];
    vc4.resultIndex = index;
    vc4.selectImage = self.image;
    if (index == 0) {
        return vc1;
    } else if (index == 1) {
        return vc2;
    } else if (index == 2) {
        return vc3;
    } else {
        return vc4;
    }
}
 
// 标题宽度
- (CGFloat)menuView:(WMMenuView *)menu widthForItemAtIndex:(NSInteger)index {
    
       NSString *title = [self.allTitle objectAtIndex:index];
       
       // 2. 计算文字实际需要的宽度
       NSDictionary *attributes = @{
           NSFontAttributeName: [UIFont systemFontOfSize:self.titleSizeNormal]
       };
       CGRect textRect = [title boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)
                                             options:NSStringDrawingUsesFontLeading
                                          attributes:attributes
                                             context:nil];
       
       return ceil(textRect.size.width);
}
 
// 菜单栏高度
- (CGFloat)menuView:(WMMenuView *)menu itemMarginAtIndex:(NSInteger)index {
    return 20;
}
 
// 菜单栏位置
- (CGRect)pageController:(WMPageController *)pageController preferredFrameForMenuView:(WMMenuView *)menuView {
    return CGRectMake(0, 0, self.view.frame.size.width,  44);
}

- (CGRect)pageController:(WMPageController *)pageController preferredFrameForContentView:(WMScrollView *)contentView {
    CGFloat originY = CGRectGetMaxY([self pageController:pageController preferredFrameForMenuView:self.menuView]);
    return CGRectMake(0, originY, self.view.frame.size.width,  self.view.frame.size.height  - originY);
}



@end
