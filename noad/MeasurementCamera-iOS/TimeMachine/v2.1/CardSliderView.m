//
//  CardSliderView.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/28.
//

#import "CardSliderView.h"


@interface CardSliderView ()
 
@property (nonatomic, strong) NSMutableArray<CardView *> *cardViews;
@property (nonatomic, strong) NSArray<NSString *> *imageNames;
@property (nonatomic, strong) UIButton *leftButton;
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, assign) CGFloat cardSpacing;
@property (nonatomic, assign) NSInteger totalCards;
@property (nonatomic, assign) BOOL isAnimating;
@property (nonatomic, strong) UIPanGestureRecognizer *panGesture;
@property (nonatomic, assign) CGPoint panStartPoint;
 
@end

@implementation CardSliderView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        _cardViews = [NSMutableArray array];
        _imageNames = @[@"变脸魔法",@"手相解读",@"卡通特效",@"面相测试"];
        _currentIndex = 1;
        _cardSpacing = 14.0*scaleX;
        _totalCards = 4;
        _isAnimating = NO;
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    UIButton *left = [[UIButton alloc] initWithFrame:CGRectZero];
    [left setImage:[UIImage imageNamed:@"首页左滑"] forState:UIControlStateNormal];
    [self addSubview:left];
    [left mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@(24*scaleX));
        make.left.equalTo(@(5));
        make.top.equalTo(@(94));
    }];
    [left addTarget:self action:@selector(leftButtonClick) forControlEvents:UIControlEventTouchUpInside];
    self.leftButton = left;
    
    UIButton *right = [[UIButton alloc] initWithFrame:CGRectZero];
    [right setImage:[UIImage imageNamed:@"首页右滑"] forState:UIControlStateNormal];
    [self addSubview:right];
    [right mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@(24*scaleX));
        make.right.equalTo(@(-5));
        make.top.equalTo(@(94));
    }];
    [right addTarget:self action:@selector(rightButtonClick) forControlEvents:UIControlEventTouchUpInside];
    self.rightButton = right;
    
    [self.cardViews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    [self.cardViews removeAllObjects];
    
    for (NSInteger i = 0; i < self.totalCards;  i++) {
        
        CardView *view = [[CardView alloc] initWithFrame:CGRectZero];
        [view setbackImage:_imageNames[i]];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
                                              initWithTarget:self
                                              action:@selector(viewTapped:)];
         
        // 开启用户交互（UIView默认是NO，必须设置为YES）
        view.userInteractionEnabled  = YES;
         
        // 添加手势到视图
        [view addGestureRecognizer:tapGesture];
        [self.cardViews addObject:view];
        [self addSubview:view];
    }
    
    self.panGesture  = [[UIPanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGesture:)];
    [self addGestureRecognizer:self.panGesture];
    
    [self layoutCardsAnimated:NO];
    
}

#pragma mark - 手势处理
 
- (void)handlePanGesture:(UIPanGestureRecognizer *)gesture {
    if (self.isAnimating  || self.cardViews.count  == 0) return;
    
    switch (gesture.state)  {
        case UIGestureRecognizerStateBegan: {
            // 记录手势开始时的位置
            self.panStartPoint  = [gesture locationInView:self];
            break;
        }
            
        case UIGestureRecognizerStateChanged: {
            // 获取平移量
            CGPoint translation = [gesture translationInView:self];
            
            // 只处理水平移动，忽略垂直移动
            if (fabs(translation.y) == 0) {
                // 计算水平滑动距离
                CGPoint currentPoint = [gesture locationInView:self];
                CGFloat offsetX = currentPoint.x - self.panStartPoint.x;
                
                // 更新卡片位置
                [self updateCardsPositionWithOffset:offsetX];
            }
            break;
        }
            
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled: {
            // 获取滑动速度
            CGPoint velocity = [gesture velocityInView:self];
            
            // 只处理水平滑动
            if (fabs(velocity.x) > fabs(velocity.y)) {
                // 判断水平滑动方向
                if (velocity.x > 500) {
                    // 快速向右滑动，切换到上一张
                    [self rightButtonClick];
                } else if (velocity.x < -500) {
                    // 快速向左滑动，切换到下一张
                    [self leftButtonClick];
                } else {
                    // 慢速滑动，根据滑动距离决定是否切换
                    CGPoint currentPoint = [gesture locationInView:self];
                    CGFloat offsetX = currentPoint.x - self.panStartPoint.x;
                    
                    if (offsetX > self.frame.size.width  / 4) {
                        [self rightButtonClick];
                    } else if (offsetX < -self.frame.size.width  / 4) {
                        [self leftButtonClick];
                    } else {
                        // 回弹到当前卡片
                        [self layoutCardsAnimated:YES];
                    }
                }
            } else {
                // 如果是垂直滑动，回弹到原始位置
                [self layoutCardsAnimated:YES];
            }
            break;
        }
            
        default:
            break;
    }
}
 
- (void)updateCardsPositionWithOffset:(CGFloat)offsetX {
    CGFloat progress = offsetX / self.frame.size.width;
    progress = MAX(-1, MIN(1, progress)); // 限制在-1到1之间
    
    CGFloat littleCardWidth = 76*scaleX;
    CGFloat bigCardWidth = 120*scaleX;
    CGFloat startX = self.frame.size.width/2  - bigCardWidth/2 - 14*scaleX - littleCardWidth;
    
    for (NSInteger i = 0; i < self.cardViews.count;  i++) {
        CardView *card = self.cardViews[i];
        NSInteger relativePosition = i - self.currentIndex;
        
        // 处理无限循环边界情况
        if (self.currentIndex  == 0 && i == self.cardViews.count  - 1) {
            relativePosition = -1; // 最后一张卡片在左边
        } else if (self.currentIndex  == self.cardViews.count  - 1 && i == 0) {
            relativePosition = 1; // 第一张卡片在右边
        }
        
        // 计算原始位置
        CGFloat originalX = startX;
        if (relativePosition == -1) {
            originalX = startX;
        } else if (relativePosition == 0) {
            originalX = startX + littleCardWidth + self.cardSpacing;
        } else if (relativePosition == 1) {
            originalX = startX + littleCardWidth + bigCardWidth + 2 * self.cardSpacing;
        }
        
        // 计算目标位置
        CGFloat targetX;
        if (offsetX > 0) {
            // 向右滑动，卡片向左移动
            targetX = originalX - (littleCardWidth + self.cardSpacing);
        } else {
            // 向左滑动，卡片向右移动
            targetX = originalX + (littleCardWidth + self.cardSpacing);
        }
        
        // 计算中间位置
        CGFloat newX = originalX + (targetX - originalX) * fabs(progress);
        
        // 更新卡片位置
        CGRect frame = card.frame;
        frame.origin.x  = newX;
        card.frame  = frame;
        
        // 更新卡片大小（根据位置）
        BOOL isMiddleCard = (relativePosition == 0 && progress == 0);
        CGFloat scale = isMiddleCard ? 1.0 : 0.8;
        card.transform  = CGAffineTransformMakeScale(scale, scale);
    }
}
 


- (void)viewTapped:(UITapGestureRecognizer *)gesture {
    
    CardView *view = (CardView*)gesture.view;
    if ([view.tagName isEqualToString:@"变脸魔法"]) {
        [self.delegate faceMagicTouch:view];
    } else if ([view.tagName isEqualToString:@"手相解读"]) {
        [self.delegate handTouch:view];
    } else if ([view.tagName isEqualToString:@"卡通特效"]) {
        [self.delegate catoonTouch:view];
    } else if ([view.tagName isEqualToString:@"面相测试"]) {
        [self.delegate timeOldTouch:view];
    }
    
}

- (void)changeCardPosition {
    CGFloat littleCardWidth = 76*scaleX;
    CGFloat litttleCardHeight = 133*scaleX;
    CGFloat bigCardWidth = 120*scaleX;
    CGFloat bigCardHeight = 210*scaleX;
    
    CGFloat containerWidth = self.frame.size.width - (5+24+8)*scaleX*2;
    CGFloat containerHeight = 210*scaleX;
    
    CGFloat startX = self.frame.size.width/2 - bigCardWidth/2 - 14*scaleX - littleCardWidth;
    
    for (NSInteger i = 0; i < self.cardViews.count;  i++) {
        CardView *card = self.cardViews[i];
        
        // 计算相对位置（考虑无限循环）
        NSInteger relativePosition = i - self.currentIndex;
        
        // 处理无限循环
        if (self.currentIndex  == 0 && i == self.cardViews.count  - 1) {
            relativePosition = -1; // 最后一张卡片在左边
        } else if (self.currentIndex  == self.cardViews.count  - 1 && i == 0) {
            relativePosition = 1; // 第一张卡片在右边
        }
        
        // 确定卡片大小
        BOOL isMiddleCard = (relativePosition == 0);
        CGFloat cardWidth = isMiddleCard ? bigCardWidth : littleCardWidth;
        CGFloat cardHeight = isMiddleCard ? bigCardHeight : litttleCardHeight;
        
        // 计算卡片位置
        CGFloat x = startX;
        if (relativePosition == -1) {
            x = startX ; // 左边卡片
        } else if (relativePosition == 0) {
            x = startX + littleCardWidth + self.cardSpacing ;  // 中间卡片
        } else if (relativePosition == 1) {
            x = startX + littleCardWidth + bigCardWidth + 2 * self.cardSpacing ;  // 右边卡片
        }
        
        // 处理边界情况（无限循环）
        if (relativePosition > 1) {
            x = -littleCardWidth - self.cardSpacing;  // 左边的卡片在屏幕外
        } else if (relativePosition < -1) {
            x = containerWidth + littleCardWidth + self.cardSpacing;  // 右边的卡片在屏幕外
        }
        
        CGFloat y = (containerHeight - cardHeight) / 2;
        
        card.frame  = CGRectMake(x, y, cardWidth, cardHeight);
        card.layer.zPosition  = isMiddleCard ? 1 : 0;
        
        if(i == 0) {
            [self.leftButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.width.height.equalTo(@(24*scaleX));
                make.left.equalTo(@(5*scaleX));
                make.centerY.equalTo(card);
            }];
            [self.rightButton mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.width.height.equalTo(@(24*scaleX));
                make.right.equalTo(@(-5*scaleX));
                make.centerY.equalTo(card);
            }];
        }
        
    }
}
    
- (void)layoutCardsAnimated:(BOOL)animated {
        
        if (self.cardViews.count  == 0 || self.totalCards  == 0 || self.isAnimating)  {
            return;
        }
        
        if (animated) {
            self.isAnimating  = YES;
            [UIView animateWithDuration:0.4
                                  delay:0
                 usingSpringWithDamping:0.5
                  initialSpringVelocity:0.5
                                options:UIViewAnimationOptionCurveEaseInOut | UIViewAnimationOptionAllowUserInteraction
                             animations:^{
                [self changeCardPosition];
            }completion:^(BOOL finished) {
                self.isAnimating  = NO;
            }];
        } else {
            [self changeCardPosition];
        }
        
    }

- (void)layoutSubviews {
    [super layoutSubviews];
    [self layoutCardsAnimated:NO];
}

- (void)leftButtonClick {
    if (self.cardViews.count  == 0 || self.isAnimating)  return;
    
    NSInteger newIndex = self.currentIndex  + 1;
    if (newIndex >= self.cardViews.count)  {
        newIndex = 0;
    }
    
    self.currentIndex  = newIndex;
    [self layoutCardsAnimated:YES];
}

- (void)rightButtonClick {
    if (self.cardViews.count  == 0 || self.isAnimating)  return;
    
    NSInteger newIndex = self.currentIndex  - 1;
    if (newIndex < 0) {
        newIndex = self.cardViews.count  - 1;
    }
    
    self.currentIndex  = newIndex;
    [self layoutCardsAnimated:YES];
}

@end
