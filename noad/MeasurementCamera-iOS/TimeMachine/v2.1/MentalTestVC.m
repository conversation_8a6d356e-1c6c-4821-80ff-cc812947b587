//
//  MentalTestVC.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/27.
//

#import "MentalTestVC.h"
#import "DatabaseManager.h"
#import "HomeCell.h"
#import "PsychologicalViewController.h"

@interface MentalTestVC () <UITableViewDelegate,UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) NSArray *psychologicalList;

@end

@implementation MentalTestVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    DatabaseManager *databaseManager = [[DatabaseManager alloc] init];
    self.psychologicalList = [databaseManager obtainPsychologicalListTable];
    
    [self.tableView reloadData];
}

- (void)setupUI {
    
    UIImageView *background = [[UIImageView alloc] initWithFrame:self.view.bounds];
    background.image = [UIImage imageNamed:@"心理测试背景"];
    [self.view addSubview:background];
    
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero];
    title.textColor = [UIColor whiteColor];
    title.text = local(@"心理测试");
    title.font = [UIFont systemFontOfSize:16*scaleX weight:UIFontWeightSemibold];
    [self.view addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@(28));
        make.top.equalTo(@(48));
    }];
    
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    [self.view addSubview:self.tableView];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    [self.tableView setSeparatorStyle:UITableViewCellSeparatorStyleNone];
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;
    self.tableView.bounces = NO;
    UITabBarController *tabBarController = (UITabBarController *)[UIApplication sharedApplication].keyWindow.rootViewController;
    CGFloat tabBarHeight = tabBarController.tabBar.frame.size.height;
    NSLog(@"TabBar 高度: %f", tabBarHeight);
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(title.mas_bottom).offset(16);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(@(-tabBarHeight));
    }];
    
    if (@available(iOS 11.0, *)) {
        self.tableView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    } else {
        self.automaticallyAdjustsScrollViewInsets = NO;
    }
    
    
    
}

#pragma mark - tableview delegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.psychologicalList.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *CellIdentifier = @"Cell";
    HomeCell *cell = [tableView dequeueReusableCellWithIdentifier:CellIdentifier];
    if (cell == nil) {
        cell = [[HomeCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:CellIdentifier];
    }
    NSDictionary *pDict = _psychologicalList[indexPath.row];
    cell.labelTitle.text = [NSString stringWithFormat:@"%ld.%@", indexPath.row+1, pDict[@"desc"]];
    //cell.labelTitle.text = pDict[@"title"];
    cell.labelDesc.text = pDict[@"desc"];
    
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath
{
    if (tableView == _tableView) {
        UITableViewCell *cell = [self tableView:tableView cellForRowAtIndexPath:indexPath];
        return cell.bounds.size.height;
    }
    return 0;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if (tableView == _tableView) {
        PsychologicalViewController *vc = [[PsychologicalViewController alloc] init];
        NSDictionary *pDict = _psychologicalList[indexPath.row];
        vc.pDict = pDict;
        vc.hidesBottomBarWhenPushed = YES;
        [self.navigationController pushViewController:vc animated:YES];
    }
}




@end
