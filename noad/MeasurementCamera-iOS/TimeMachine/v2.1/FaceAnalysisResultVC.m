//
//  FaceAnalysisResultVC.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/30.
//

#import "FaceAnalysisResultVC.h"
#import "DeepSeekStreamClient.h"
#import "OSSUploadImageTool.h"
#import "CopyLabel.h"
#import "ZYELabel.h"
#import "Toast/Toast.h"

@interface FaceAnalysisResultVC ()

@property(nonatomic,strong) UIScrollView* resultScroll;
@property(nonatomic,strong) UILabel* resultLabel;
@property(nonatomic,strong) DeepSeekStreamClient* client;
@property(nonatomic,strong) NSString *requestStr;
@property NSMutableString* resulStr;

@end

@implementation FaceAnalysisResultVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    self.view.backgroundColor = [UIColor blackColor];
    [self initClient];
}

- (void)viewWillAppear:(BOOL)animated {
    [self requestResult];
}

- (void)setupUI {
    UIView * show = [UIView new];
    show.backgroundColor = [UIColor colorWithHexString:@"#FEF7D9" alpha:1];
    show.layer.cornerRadius = 10*scaleX;
    show.layer.borderWidth = 5*scaleX;
    show.layer.borderColor = [UIColor colorWithHexString:@"#FAEABF" alpha:1].CGColor;
    [self.view addSubview:show];
    [show mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(@(18));
        make.centerX.equalTo(self.view);
        make.width.mas_equalTo(327*scaleX);
        make.height.mas_equalTo(319*scaleX);
    }];
    [show layoutIfNeeded];
    
    UIScrollView* sc = [UIScrollView new];
    sc.frame = CGRectMake(0, 0, show.width, show.height-50*scaleX);
    [show addSubview:sc];
    
    UILabel* la = [UILabel createLabelWithTitle:@"" textColor:[UIColor colorWithHexString:@"#7C573C" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:16*scaleX weight:UIFontWeightMedium]];
    la.numberOfLines = 0;
    [sc addSubview:la];
    la.frame = CGRectMake(16*scaleX,24*scaleX, show.width-16*scaleX*2, 0);
    la.userInteractionEnabled = YES;
    UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc]
                                             initWithTarget:self
                                             action:@selector(handleLongPress:)];
    [la addGestureRecognizer:longPress];
    
    __weak typeof(self) weakSelf = self;
    ((ZYELabel *)la).afterCopy = ^{
        __strong typeof(weakSelf) strongSelf = weakSelf;
        [strongSelf.view makeToast:@"复制成功" duration:1.0 position:CSToastPositionCenter];
    };
    
    self.resultScroll = sc;
    self.resultLabel = la;
    
    UILabel* tip = [UILabel createLabelWithTitle:local(@"以上结果仅供参考。") textColor:[UIColor colorWithHexString:@"#D8CAA3" alpha:1] textAlignment:NSTextAlignmentLeft font:[UIFont systemFontOfSize:14*scaleX]];
    [show addSubview:tip];
    [tip mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16*scaleX);
        make.bottom.mas_equalTo(-24*scaleX);
    }];
    
}

- (void)handleLongPress:(UILongPressGestureRecognizer *)gesture {
    if (gesture.state  == UIGestureRecognizerStateBegan) {
       
        UILabel *label = (UILabel *)gesture.view;
    
        [label becomeFirstResponder];
        
        CGPoint location = [gesture locationInView:label];
        
        UIMenuController *menu = [UIMenuController sharedMenuController];
        
        CGRect targetRect = CGRectMake(location.x, location.y, 1, 1);
        [menu setTargetRect:targetRect inView:label];
        [menu setMenuVisible:YES animated:YES];
    }
}

- (void)initClient {
    if(!self.client)
    {
        DeepSeekStreamClient *client = [[DeepSeekStreamClient alloc] initWithAPIKey:@"************************************"];
        self.client = client;
    }
}

- (void)requestResult {
    if (!self.requestStr || self.requestStr.length == 0) {
        switch (self.resultIndex) {
            case 0:
                self.requestStr = local(@"根据图片，结合中国命理理论，分析他的五官");
                break;
            case 1:
                self.requestStr = local(@"分析图片面相，结合中国命理理论，推算综合本年运势分析，只生成综合本年运势分析");
                break;
            case 2:
                self.requestStr = local(@"分析图片面相，结合中国命理理论，告诉我本年的每月运势，不能返回除每月运势外的任何参数");
                break;
            case 3:
                self.requestStr = local(@"分析图片面相，结合中国命理理论，给我一些本年的综合建议，不能返回除综合建议外的字段");
                break;
            default:
                break;
        }
        
        [SVProgressHUD show];
        [[OSSUploadImageTool shared]uploadImage:self.selectImage completion:^(NSString * _Nonnull url, NSError * _Nonnull error) {
            if(!error)
            {
                [self.client sendImageStreamRequestWithMessages:self.requestStr andImageUrl:url callback:^(NSString * _Nonnull requestID, NSString * _Nonnull chunkContent, NSError * _Nonnull error) {
                    NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    
                    if (error) {
                        [SVProgressHUD showErrorWithStatus:@"获取失败，请检查网络重新获取"];
                        return;
                    }
                    
                    if (chunkContent) {
                        [SVProgressHUD dismiss];
                        if (!self.resulStr) {
                            self.resulStr = [NSMutableString string];
                        }
                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                            if (chunkContent) {
                                @synchronized (self) {
                                    [self.resulStr appendFormat:@"%@", chunkContent];
                                }
                                // 主线程仅负责UI更新
                                dispatch_async(dispatch_get_main_queue(), ^{
                                    
                                    self.resultLabel.text = self.resulStr;
                                    self.resultLabel.width = self.resultScroll.width-16*scaleX*2;
                                    [self.resultLabel sizeToFit];
                                    
                                    self.resultScroll.contentSize = CGSizeMake(self.resultScroll.width, self.resultLabel.bottom+24*scaleX);
                                    [self.resultScroll scrollRectToVisible:CGRectMake(0, self.resultLabel.bottom-self.resultScroll.height, self.resultScroll.width, self.resultScroll.height) animated:NO];
                                    
                                });
                            }
                        });
                        
                        NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
                    } else {
                        NSLog(@"请求%@完成", requestID);
                    }
                }];
            }
            else
            {
                [SVProgressHUD showErrorWithStatus:@"上传图片失败"];
            }
        }];
        
        
    }
    
}

@end
