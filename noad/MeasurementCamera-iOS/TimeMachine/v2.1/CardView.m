//
//  CardView.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/28.
//

#import "CardView.h"

@interface CardView()

@property (nonatomic,strong) UIImageView *mainImageView;

@property (nonatomic,strong) UIImageView *aiImageView;

@property (nonatomic,strong) UILabel *label;

@end

@implementation CardView

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    
    
    UIImageView *main = [[UIImageView alloc] initWithFrame:CGRectZero];
    [self addSubview:main];
    [main mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];
    self.mainImageView = main;
    
    UIImageView *ai = [[UIImageView alloc] initWithFrame:CGRectZero];
    ai.image = [UIImage imageNamed:@"新ai"];
    [main addSubview:ai];
    [ai mas_makeConstraints:^(MASConstraintMaker *make) {
        CGFloat width = self.frame.size.width / (120*scaleX) * (26*scaleX);
        make.width.height.equalTo(@(width));
        make.top.equalTo(main).offset(9);
        make.right.equalTo(main).offset(-8);
    }];
    self.aiImageView = ai;
    [self.aiImageView setHidden:YES];
    
    UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero];
    label.textColor = [MyColor colorWithHexString:@"#FFFFFF" alpha:0.86];
    label.font = [UIFont systemFontOfSize:14*scaleX weight:UIFontWeightMedium];
    [self addSubview:label];
    [label mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(main.mas_bottom).offset(10);
        make.centerX.equalTo(self);
    }];
    self.label = label;
    
}

- (void)layoutSubviews {
    [self.aiImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
        CGFloat width = self.frame.size.width / (120*scaleX) * (26*scaleX);
        make.width.height.equalTo(@(width));
        make.top.equalTo(self.mainImageView).offset(9);
        make.right.equalTo(self.mainImageView).offset(-8);
    }];
}

- (void)setbackImage:(NSString *)imageName {
    UIImage *image = [UIImage imageNamed:imageName];
    self.mainImageView.image = image;
    self.label.text = local(imageName);
    self.tagName = imageName;
    if ([imageName isEqualToString:@"手相解读"]) {
        [self.aiImageView setHidden:NO];
    }
}




@end
