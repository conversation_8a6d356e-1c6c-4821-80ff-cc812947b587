//
//  FaceAnalysisVC.m
//  TimeMachine
//
//  Created by fs0012 on 2025/4/29.
//

#import "FaceAnalysisVC.h"
#import "FaceAnalysisTabVC.h"

@interface FaceAnalysisVC ()

@property(nonatomic,strong) FaceAnalysisTabVC *tabVC;

@end

@implementation FaceAnalysisVC

- (void)viewDidLoad {
    [super viewDidLoad];
    [self initBackButton];
    [self setupUI];
    self.view.backgroundColor = [UIColor blackColor];
}

- (void)setupUI {
    
    UILabel *title = [[UILabel alloc] initWithFrame:CGRectZero];
    title.text = local(@"面相分析");
    title.textColor = [UIColor whiteColor];
    title.font = [UIFont systemFontOfSize:16*scaleX weight:UIFontWeightMedium];
    [self.view addSubview:title];
    [title mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.backButton);
        make.centerX.equalTo(self.view);
    }];
    
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectZero];
    imageView.image = self.image;
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.view addSubview:imageView];
    [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@(233));
        make.centerX.equalTo(self.view);
        make.top.equalTo(title.mas_bottom).offset(30);
    }];
    imageView.layer.cornerRadius = 233 / 2;
    imageView.layer.borderWidth = 2;
    imageView.layer.borderColor = [MyColor colorWithHexString:@"#FEECBF" alpha:1.0].CGColor;
    imageView.layer.masksToBounds = YES;
    
    FaceAnalysisTabVC *tab = [[FaceAnalysisTabVC alloc] init];
    tab.image = self.image;
    [self addChildViewController:tab];
    [self.view addSubview:tab.view];
    tab.view.translatesAutoresizingMaskIntoConstraints = NO;
    [tab.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(imageView.mas_bottom).offset(31);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
    }];
    [tab didMoveToParentViewController:self];
    self.tabVC = tab;
    
}


@end
