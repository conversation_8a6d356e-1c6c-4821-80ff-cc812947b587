//
//  CopyLabel.m
//  TimeMachine
//
//  Created by fs0012 on 2025/5/7.
//

#import "CopyLabel.h"

@implementation CopyLabel

- (BOOL)canBecomeFirstResponder {
    return YES;
}
 
- (BOOL)canPerformAction:(SEL)action withSender:(id)sender {
    
    return (action == @selector(customCopy:));
}
 
- (void)customCopy:(id)sender {
    UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
    pasteboard.string  = self.text;
    NSLog(@"已复制: %@", self.text);
}

@end
