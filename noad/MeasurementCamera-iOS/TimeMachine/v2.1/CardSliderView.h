//
//  CardSliderView.h
//  TimeMachine
//
//  Created by fs0012 on 2025/4/28.
//

#import <UIKit/UIKit.h>
#import "CardView.h"

NS_ASSUME_NONNULL_BEGIN

@protocol CardSliderViewDelegate <NSObject>

@optional

- (void)faceMagicTouch:(CardView *)view;
- (void)handTouch:(CardView *)view;
- (void)catoonTouch:(CardView *)view;
- (void)timeOldTouch:(CardView *)view;

@end

@interface CardSliderView : UIView

@property (nonatomic, assign) NSInteger currentIndex;

@property (nonatomic, weak) id<CardSliderViewDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
