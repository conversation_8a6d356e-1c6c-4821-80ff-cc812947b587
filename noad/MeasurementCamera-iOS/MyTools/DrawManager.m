//
//  DrawManager.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/22.
//

#import "DrawManager.h"

@implementation DrawManager

- (void)drawline:(CGContextRef)context
      startPoint:(CGPoint)startPoint
       stopPoint:(CGPoint)stopPoint
           color:(UIColor *)color
       lineWidth:(double)lineWitdth
            dash:(BOOL)flag
{
    CGContextSetStrokeColorWithColor(context, color.CGColor);
    CGContextSetLineWidth(context, lineWitdth);
    if (flag == YES) {
        CGFloat lengths[] = {4,10};
        CGContextSetLineDash(context, 0, lengths, 2);  //画虚线
    }
    if (flag == NO) {
        CGFloat lengths[] = {0,0};
        CGContextSetLineDash(context, 0, lengths, 0);
    }
    CGContextBeginPath(context);
    CGContextMoveToPoint(context, startPoint.x, startPoint.y);
    CGContextAddLineToPoint(context, stopPoint.x,stopPoint.y);
    CGContextStrokePath(context);
}

@end
