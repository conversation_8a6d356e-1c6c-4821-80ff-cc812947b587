//
//  OSSUploadImageTool.m
//  TimeMachine
//
//  Created by fs0011 on 2025/3/8.
//

#import "OSSUploadImageTool.h"
static NSString *const AccessKey = @"LTAI5tRqxurEtszQkPRMZQmU";
static NSString *const SecretKey = @"******************************";
static NSString *const BucketName = @"cecexiangji";
static NSString *const Endpoint = @"oss-cn-guangzhou.aliyuncs.com";
@implementation OSSUploadImageTool
+ (instancetype)shared {
    static OSSUploadImageTool *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[OSSUploadImageTool alloc] init];
        [instance setupOSSClient];
    });
    return instance;
}

- (void)setupOSSClient {

    id<OSSCredentialProvider> credential =
        [[OSSPlainTextAKSKPairCredentialProvider alloc]
            initWithPlainTextAccessKey:AccessKey
                            secretKey:SecretKey];
    
    OSSClientConfiguration *config = [OSSClientConfiguration new];
    config.maxRetryCount = 3;
    
    _client = [[OSSClient alloc] initWithEndpoint:Endpoint
                               credentialProvider:credential
                              clientConfiguration:config];
}

// 上传图片并获取URL
- (void)uploadImage:(UIImage *)image
         completion:(void (^)(NSString *url, NSError *error))completion {
    
    // 生成唯一文件名
    NSString *objectKey = [NSString stringWithFormat:@"images/%@.jpg", [[NSUUID UUID] UUIDString]];
    
    // 转换图片为NSData
    NSData *imageData = UIImageJPEGRepresentation(image, 0.8);
    
    // 创建上传请求
    OSSPutObjectRequest *request = [OSSPutObjectRequest new];
    request.bucketName = BucketName;
    request.objectKey = objectKey;
    request.uploadingData = imageData;
    request.objectMeta = @{@"x-oss-object-acl": @"public-read"}; // 设置公开读权限
    
    // 异步上传
    OSSTask *task = [self.client putObject:request];
    [task continueWithBlock:^id(OSSTask *task) {
        if (task.error) {
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(nil, task.error);
                });
            }
        } else {
            // 构造永久访问URL
//            NSString *urlString = [NSString stringWithFormat:@"%@/%@", Endpoint, objectKey];
            NSString *urlString = [NSString stringWithFormat:@"https://%@.%@/%@",
                                  BucketName,
                                  [Endpoint stringByReplacingOccurrencesOfString:@"https://" withString:@""],
                                  objectKey];
            // 如果使用CDN或自定义域名
            // NSString *urlString = [NSString stringWithFormat:@"https://your-cdn-domain/%@", objectKey];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(urlString, nil);
            });
        }
        return nil;
    }];
}

@end
