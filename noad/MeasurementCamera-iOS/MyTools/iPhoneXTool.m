//
//  iPhoneXTool.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/9.
//

#import "iPhoneXTool.h"

#define iPhoneX kScreenWidth >=375.0f && kScreenHeight >=812.0f && kIs_iphone

@implementation iPhoneXTool

+ (CGFloat)setCommonValue:(CGFloat)commonValue orStrangeValue:(CGFloat)strangeValue
{
    CGFloat value = 0;
    if (iPhoneX) {
        value = strangeValue;
    } else {
        value = commonValue;
    }
    return value;
}

@end
