//
//  FileManager.h
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface FileManager : NSObject

+ (NSString *)pathForDocuments;
+ (NSString *)pathForDocumentsFileName:(NSString *)fileName;
+ (NSString *)pathForDocumentsFileName:(NSString *)fileName withSubFileName:(NSString *)subFileName;
+ (NSString *)pathForFilePath:(NSString *)filePath withName:(NSString *)name;
+ (BOOL)removeDocumentsFileName:(NSString *)filename;
+ (BOOL)removeDocumentsFileFromFilePath:(NSString *)filePath;
+ (BOOL)moveFileAtPath:(NSString *)fromPath toFolderPath:(NSString *)toFolderPath toPathName:(NSString *)toPathName overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error;
+ (BOOL)moveFileAtPath:(NSString *)fromPath toPath:(NSString *)toPath overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error;
+ (BOOL)copyFileAtPath:(NSString *)fromPath toFolderPath:(NSString *)toFolderPath toPathName:(NSString *)toPathName overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error;
+ (NSString *)copyFolderPath:(NSString *)folderPath fromFileName:(NSString *)fromFileName format:(NSString *)format error:(NSError *__autoreleasing *)error;
//+ (BOOL)copyFileAtPath:(NSString *)fromPath toPath:(NSString *)toPath overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error;

+ (NSString *)copyFileAtPath:(NSString *)fromPath toPath:(NSString *)toPath overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error;
+ (BOOL)fileIsExistAtPath:(NSString *)filePath;
+ (NSArray *)getDocumentFolderAllFilesForFolderPath:(NSString *)folderPath;
+ (NSString *)getDocumentsFileName:(NSString *)filePath;
+ (NSString *)getDocumentsFileExtension:(NSString *)filePath;

@end

NS_ASSUME_NONNULL_END
