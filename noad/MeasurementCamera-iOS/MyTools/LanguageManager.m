//
//  LanguageManager.m
//  IDPhoto
//
//  Created by FS003 on 2021/8/17.
//

#import "LanguageManager.h"

@implementation LanguageManager

// 判断是中文还是其他语言
+ (BOOL)getCurrentLanguageIsZH
{
    BOOL isZH = NO;
    
    NSString *currentLanguage1 = [[NSUserDefaults standardUserDefaults] objectForKey:@"AppleLanguages"][0];
    NSString *currentLanguage2 = [[NSBundle mainBundle] preferredLocalizations][0];
    //NSLog(@"language1 == %@   language2 == %@", currentLanguage1, currentLanguage2);

    if ([currentLanguage1 containsString:@"zh-Ha"]  ||  [currentLanguage2 containsString:@"zh-Ha"]) {
        isZH = YES;
        NSLog(@"这个是中文");
    }
    
    return isZH;
}

@end
