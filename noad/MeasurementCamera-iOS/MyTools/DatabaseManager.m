//
//  DatabaseManager.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/22.
//

#import "DatabaseManager.h"
#import "FileManager.h"
#import "FMDB.h"

@implementation DatabaseManager


//创建数据库
- (FMDatabase *)loadDatabse
{
    NSString *databsePath = [[FileManager pathForDocumentsFileName:@"DATABASE"] stringByAppendingPathComponent:DATABASE];
    FMDatabase *database = [FMDatabase databaseWithPath:databsePath];
    if (![database open]) {
        NSLog(@"数据库打开失败");
        return nil;
    } else {
        NSLog(@"数据库打开成功");
    }
    return database;
}

//创建手相资料的表
- (BOOL)createPalmsTable;
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if (![database tableExists:@"Palms"]) {
        NSLog(@"创建Palms表成功");
        NSString *tableString = [NSString stringWithFormat:@"create table Palms (palms_id INTEGER PRIMARY KEY AUTOINCREMENT, label1 text, label2 text, label3 text, label4 text, personality_content text, feeling_content text, work_content text)"];
        isSucceed = [database executeUpdate:tableString];
    } else {
        NSLog(@"数据库的表已存在");
    }
    [database close];
    
    return isSucceed;
}

- (BOOL)insertPalmsTableWithLabel1:(NSString *)label1 label2:(NSString *)label2 label3:(NSString *)label3 label4:(NSString *)label4 personality_content:(NSString *)personality_content feeling_content:(NSString *)feeling_content work_content:(NSString *)work_content
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"Palms"]) {
        isSucceed = [database executeUpdate:[NSString stringWithFormat:@"insert into Palms (label1, label2, label3, label4, personality_content, feeling_content, work_content) values (?, ?, ?, ?, ?, ?, ?)"], label1, label2, label3, label4, personality_content, feeling_content, work_content];
    }
    [database close];
    
    return isSucceed;
}

- (NSArray *)obtainPalmsTable
{
    NSMutableArray *array = [NSMutableArray array];
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"Palms"]) {
        FMResultSet *rs = [database executeQuery:@"select * from Palms"];
        while ([rs next]) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            if ([rs stringForColumn:@"palms_id"]) {
                [dict setObject:[rs stringForColumn:@"palms_id"] forKey:@"palms_id"];
            } else {
                [dict setObject:@"" forKey:@"palms_id"];
            }
            if ([rs stringForColumn:@"label1"]) {
                [dict setObject:[rs stringForColumn:@"label1"] forKey:@"label1"];
            } else {
                [dict setObject:@"" forKey:@"label1"];
            }
            if ([rs stringForColumn:@"label2"]) {
                [dict setObject:[rs stringForColumn:@"label2"] forKey:@"label2"];
            } else {
                [dict setObject:@"" forKey:@"label2"];
            }
            if ([rs stringForColumn:@"label3"]) {
                [dict setObject:[rs stringForColumn:@"label3"] forKey:@"label3"];
            } else {
                [dict setObject:@"" forKey:@"label3"];
            }
            if ([rs stringForColumn:@"label4"]) {
                [dict setObject:[rs stringForColumn:@"label4"] forKey:@"label4"];
            } else {
                [dict setObject:@"" forKey:@"label4"];
            }
            if ([rs stringForColumn:@"personality_content"]) {
                [dict setObject:[rs stringForColumn:@"personality_content"] forKey:@"personality_content"];
            } else {
                [dict setObject:@"" forKey:@"personality_content"];
            }
            if ([rs stringForColumn:@"feeling_content"]) {
                [dict setObject:[rs stringForColumn:@"feeling_content"] forKey:@"feeling_content"];
            } else {
                [dict setObject:@"" forKey:@"feeling_content"];
            }
            if ([rs stringForColumn:@"work_content"]) {
                [dict setObject:[rs stringForColumn:@"work_content"] forKey:@"work_content"];
            } else {
                [dict setObject:@"" forKey:@"work_content"];
            }
            [array addObject:dict];
        }
    }
    [database close];
    return array;
}


//创建心理测试列表资料的表
- (BOOL)createPsychologicalListTable;
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if (![database tableExists:@"PsychologicalList"]) {
        NSLog(@"创建PsychologicalList表成功");
        NSString *tableString = [NSString stringWithFormat:@"create table PsychologicalList (psychological_id INTEGER PRIMARY KEY AUTOINCREMENT, title text, desc text, result text)"];
        isSucceed = [database executeUpdate:tableString];
    } else {
        NSLog(@"数据库的表已存在");
    }
    [database close];
    
    return isSucceed;
}

- (BOOL)insertPsychologicalListTableWithTitle:(NSString *)title desc:(NSString *)desc result:(NSString *)result
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"PsychologicalList"]) {
        isSucceed = [database executeUpdate:[NSString stringWithFormat:@"insert into PsychologicalList (title, desc, result) values (?, ?, ?)"], title, desc, result];
    }
    [database close];
    
    return isSucceed;
}

- (NSArray *)obtainPsychologicalListTable
{
    NSMutableArray *array = [NSMutableArray array];
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"PsychologicalList"]) {
        FMResultSet *rs = [database executeQuery:@"select * from PsychologicalList"];
        while ([rs next]) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            if ([rs stringForColumn:@"psychological_id"]) {
                [dict setObject:[rs stringForColumn:@"psychological_id"] forKey:@"psychological_id"];
            } else {
                [dict setObject:@"" forKey:@"psychological_id"];
            }
            if ([rs stringForColumn:@"title"]) {
                [dict setObject:[rs stringForColumn:@"title"] forKey:@"title"];
            } else {
                [dict setObject:@"" forKey:@"title"];
            }
            if ([rs stringForColumn:@"desc"]) {
                [dict setObject:[rs stringForColumn:@"desc"] forKey:@"desc"];
            } else {
                [dict setObject:@"" forKey:@"desc"];
            }
            if ([rs stringForColumn:@"result"]) {
                [dict setObject:[rs stringForColumn:@"result"] forKey:@"result"];
            } else {
                [dict setObject:@"" forKey:@"result"];
            }
            
            [array addObject:dict];
        }
    }
    [database close];
    return array;
}

//创建心理测试题目内容的表
- (BOOL)createPsychologicalSubjectTable;
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if (![database tableExists:@"PsychologicalSubject"]) {
        NSLog(@"创建PsychologicalSubject表成功");
        NSString *tableString = [NSString stringWithFormat:@"create table PsychologicalSubject (psychological_id text, subject_index text, subject_title text, subject_answer1 text, answer1_toindex text, subject_answer2 text, answer2_toindex text, subject_answer3 text, answer3_toindex text, subject_answer4 text, answer4_toindex text, result_type text)"];
        isSucceed = [database executeUpdate:tableString];
    } else {
        NSLog(@"数据库的表已存在");
    }
    [database close];
    
    return isSucceed;
}

- (BOOL)insertPsychologicalSubjectTableWithPsychological_id:(NSString *)psychological_id subject_index:(NSString *)subject_index subject_title:(NSString *)subject_title subject_answer1:(NSString *)subject_answer1 answer1_toindex:(NSString *)answer1_toindex subject_answer2:(NSString *)subject_answer2 answer2_toindex:(NSString *)answer2_toindex subject_answer3:(NSString *)subject_answer3 answer3_toindex:(NSString *)answer3_toindex subject_answer4:(NSString *)subject_answer4 answer4_toindex:(NSString *)answer4_toindex result_type:(NSString *)result_type
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"PsychologicalSubject"]) {
        isSucceed = [database executeUpdate:[NSString stringWithFormat:@"insert into PsychologicalSubject (psychological_id, subject_index, subject_title, subject_answer1, answer1_toindex, subject_answer2, answer2_toindex, subject_answer3, answer3_toindex, subject_answer4, answer4_toindex, result_type) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"], psychological_id, subject_index, subject_title, subject_answer1, answer1_toindex, subject_answer2, answer2_toindex, subject_answer3, answer3_toindex, subject_answer4, answer4_toindex, result_type];
    }
    [database close];
    
    return isSucceed;
}

- (NSArray *)obtainPsychologicalSubjectTableWithPsychological_id:(NSString *)psychological_id
{
    NSMutableArray *array = [NSMutableArray array];
    FMDatabase *database = [self loadDatabse];
    if ([database tableExists:@"PsychologicalSubject"]) {
        FMResultSet *rs = [database executeQuery:[NSString stringWithFormat:@"select * from PsychologicalSubject where psychological_id=?"], psychological_id];
        while ([rs next]) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            if ([rs stringForColumn:@"psychological_id"]) {
                [dict setObject:[rs stringForColumn:@"psychological_id"] forKey:@"psychological_id"];
            } else {
                [dict setObject:@"" forKey:@"psychological_id"];
            }
            if ([rs stringForColumn:@"subject_index"]) {
                [dict setObject:[rs stringForColumn:@"subject_index"] forKey:@"subject_index"];
            } else {
                [dict setObject:@"" forKey:@"subject_index"];
            }
            if ([rs stringForColumn:@"subject_title"]) {
                [dict setObject:[rs stringForColumn:@"subject_title"] forKey:@"subject_title"];
            } else {
                [dict setObject:@"" forKey:@"subject_title"];
            }
            if ([rs stringForColumn:@"subject_answer1"]) {
                [dict setObject:[rs stringForColumn:@"subject_answer1"] forKey:@"subject_answer1"];
            } else {
                [dict setObject:@"" forKey:@"subject_answer1"];
            }
            if ([rs stringForColumn:@"answer1_toindex"]) {
                [dict setObject:[rs stringForColumn:@"answer1_toindex"] forKey:@"answer1_toindex"];
            } else {
                [dict setObject:@"" forKey:@"answer1_toindex"];
            }
            if ([rs stringForColumn:@"subject_answer2"]) {
                [dict setObject:[rs stringForColumn:@"subject_answer2"] forKey:@"subject_answer2"];
            } else {
                [dict setObject:@"" forKey:@"subject_answer2"];
            }
            if ([rs stringForColumn:@"answer2_toindex"]) {
                [dict setObject:[rs stringForColumn:@"answer2_toindex"] forKey:@"answer2_toindex"];
            } else {
                [dict setObject:@"" forKey:@"answer2_toindex"];
            }
            if ([rs stringForColumn:@"subject_answer3"]) {
                [dict setObject:[rs stringForColumn:@"subject_answer3"] forKey:@"subject_answer3"];
            } else {
                [dict setObject:@"" forKey:@"subject_answer3"];
            }
            if ([rs stringForColumn:@"answer3_toindex"]) {
                [dict setObject:[rs stringForColumn:@"answer3_toindex"] forKey:@"answer3_toindex"];
            } else {
                [dict setObject:@"" forKey:@"answer3_toindex"];
            }
            if ([rs stringForColumn:@"subject_answer4"]) {
                [dict setObject:[rs stringForColumn:@"subject_answer4"] forKey:@"subject_answer4"];
            } else {
                [dict setObject:@"" forKey:@"subject_answer4"];
            }
            if ([rs stringForColumn:@"answer4_toindex"]) {
                [dict setObject:[rs stringForColumn:@"answer4_toindex"] forKey:@"answer4_toindex"];
            } else {
                [dict setObject:@"" forKey:@"answer4_toindex"];
            }
            if ([rs stringForColumn:@"result_type"]) {
                [dict setObject:[rs stringForColumn:@"result_type"] forKey:@"result_type"];
            } else {
                [dict setObject:@"" forKey:@"result_type"];
            }
            
            [array addObject:dict];
        }
    }
    [database close];
    return array;
}


//创建数据库
- (FMDatabase *)loadDatabseMagic
{
    NSString *databsePath = [[FileManager pathForDocumentsFileName:@"DATABASEMagic"] stringByAppendingPathComponent:DATABASEMagic];
    FMDatabase *database = [FMDatabase databaseWithPath:databsePath];
    if (![database open]) {
        NSLog(@"数据库打开失败");
        return nil;
    } else {
        NSLog(@"数据库打开成功");
    }
    return database;
}

- (BOOL)createMagicTemplateListTable
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabseMagic];
    if (![database tableExists:@"MagicTemplate"]) {
        NSLog(@"创建MagicTemplate表成功");
        NSString *tableString = [NSString stringWithFormat:@"create table MagicTemplate (template_id INTEGER PRIMARY KEY AUTOINCREMENT, template_name text)"];
        isSucceed = [database executeUpdate:tableString];
    } else {
        NSLog(@"数据库的表已存在");
    }
    [database close];
    
    return isSucceed;
}

- (BOOL)insertMagicTemplateTableWithTemplate_name:(NSString *)template_name
{
    BOOL isSucceed = NO;
    FMDatabase *database = [self loadDatabseMagic];
    if ([database tableExists:@"MagicTemplate"]) {
        isSucceed = [database executeUpdate:[NSString stringWithFormat:@"insert into MagicTemplate (template_name) values (?)"], template_name];
    }
    [database close];
    
    return isSucceed;
}

- (NSArray *)obtainMagicTemplateListTable
{
    NSMutableArray *array = [NSMutableArray array];
    FMDatabase *database = [self loadDatabseMagic];
    if ([database tableExists:@"MagicTemplate"]) {
        FMResultSet *rs = [database executeQuery:@"select * from MagicTemplate"];
        while ([rs next]) {
            NSMutableDictionary *dict = [NSMutableDictionary dictionary];
            if ([rs stringForColumn:@"template_id"]) {
                [dict setObject:[rs stringForColumn:@"template_id"] forKey:@"template_id"];
            } else {
                [dict setObject:@"" forKey:@"template_id"];
            }
            if ([rs stringForColumn:@"template_name"]) {
                [dict setObject:[rs stringForColumn:@"template_name"] forKey:@"template_name"];
            } else {
                [dict setObject:@"" forKey:@"template_name"];
            }
            
            [array addObject:dict];
        }
    }
    [database close];
    return array;
}


@end
