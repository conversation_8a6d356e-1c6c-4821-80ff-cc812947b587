//
//  FileManager.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import "FileManager.h"

@implementation FileManager

+ (NSString *)pathForDocuments;
{
    NSString *path = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) lastObject];
    //NSLog(@"docPath == %@", path);
    
    return path;
}

+ (NSString *)pathForDocumentsFileName:(NSString *)fileName
{
    NSString *docPath = [self pathForDocuments];
    NSString *filePath = [docPath stringByAppendingPathComponent:fileName];
    if(![[NSFileManager defaultManager] fileExistsAtPath:filePath]){
        [[NSFileManager defaultManager] createDirectoryAtPath:filePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    return filePath;
}

+ (NSString *)pathForDocumentsFileName:(NSString *)fileName withSubFileName:(NSString *)subFileName
{
    NSString *docPath = [self pathForDocuments];
    NSString *filePath = [[docPath stringByAppendingPathComponent:fileName] stringByAppendingPathComponent:subFileName];
    if(![[NSFileManager defaultManager] fileExistsAtPath:[self pathForDocumentsFileName:fileName]]){
        [[NSFileManager defaultManager] createDirectoryAtPath:[self pathForDocumentsFileName:fileName] withIntermediateDirectories:YES attributes:nil error:nil];
    }
    if(![[NSFileManager defaultManager] fileExistsAtPath:filePath]){
        [[NSFileManager defaultManager] createDirectoryAtPath:filePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    return filePath;
}

+ (NSString *)pathForFilePath:(NSString *)filePath withName:(NSString *)name
{
    NSString *path = [filePath stringByAppendingPathComponent:name];
    if(![[NSFileManager defaultManager] fileExistsAtPath:path]){
        [[NSFileManager defaultManager] createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:nil];
    }
    return path;
}

+ (BOOL)removeDocumentsFileName:(NSString *)filename
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSString *filePath = [[self pathForDocuments] stringByAppendingPathComponent:filename];
    BOOL isSuccess = NO;
    if ([fileManager fileExistsAtPath:filePath]) {
        isSuccess = [fileManager removeItemAtPath:filePath error:nil];
    }
    return isSuccess;
}

+ (BOOL)removeDocumentsFileFromFilePath:(NSString *)filePath
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    BOOL isSuccess = NO;
    if ([fileManager fileExistsAtPath:filePath]) {
        isSuccess = [fileManager removeItemAtPath:filePath error:nil];
    }
    return isSuccess;
}

+ (BOOL)moveFileAtPath:(NSString *)fromPath toFolderPath:(NSString *)toFolderPath toPathName:(NSString *)toPathName overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error
{
    //先要保证源文件路径存在，不然抛出异常
    if (![[NSFileManager defaultManager] fileExistsAtPath:fromPath]) {
        [NSException raise:@"非法的源文件路径" format:@"源文件路径%@不存在，请检查源文件路径", fromPath];
        return NO;
    }
    //获得目标文件的上级目录
    if (![[NSFileManager defaultManager] fileExistsAtPath:toFolderPath]) {
        // 创建移动路径
        [[NSFileManager defaultManager] createDirectoryAtPath:toFolderPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    //目标文件的路径
    NSString *toPath = [toFolderPath stringByAppendingPathComponent:toPathName];
    //NSLog(@"toPath == %@", toPath);
    if ([fromPath isEqualToString:toPath]) {
        return NO;
    }
    // 判断目标路径文件是否存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:toPath]) {
        //如果覆盖，删除目标路径文件
        if (overwrite) {
            //删掉目标路径文件
            [[NSFileManager defaultManager] removeItemAtPath:toPath error:nil];
        }
    }
    
    // 移动文件，当要移动到的文件路径文件存在，会移动失败
    BOOL isSuccess = [[NSFileManager defaultManager] moveItemAtPath:fromPath toPath:toPath error:error];
    if (isSuccess == YES) {
        //删掉被移动文件
        [[NSFileManager defaultManager] removeItemAtPath:fromPath error:nil];
    }
    return isSuccess;
}

+ (BOOL)moveFileAtPath:(NSString *)fromPath toPath:(nonnull NSString *)toPath overwrite:(BOOL)overwrite error:(NSError *__autoreleasing  _Nullable * _Nullable)error
{
    //先要保证源文件路径存在，不然抛出异常
    if (![[NSFileManager defaultManager] fileExistsAtPath:fromPath]) {
        [NSException raise:@"非法的源文件路径" format:@"源文件路径%@不存在，请检查源文件路径", fromPath];
        return NO;
    }
    //获得目标文件的上级目录
    if (![[NSFileManager defaultManager] fileExistsAtPath:toPath]) {
        // 创建移动路径
        [[NSFileManager defaultManager] createDirectoryAtPath:toPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    //NSLog(@"toPath == %@", toPath);
    if ([fromPath isEqualToString:toPath]) {
        return NO;
    }
    // 判断目标路径文件是否存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:toPath]) {
        //如果覆盖，删除目标路径文件
        if (overwrite) {
            //删掉目标路径文件
            [[NSFileManager defaultManager] removeItemAtPath:toPath error:nil];
        }
    }
    
    // 移动文件，当要移动到的文件路径文件存在，会移动失败
    BOOL isSuccess = [[NSFileManager defaultManager] moveItemAtPath:fromPath toPath:toPath error:error];
    if (isSuccess == YES) {
        //删掉被移动文件
        [[NSFileManager defaultManager] removeItemAtPath:fromPath error:nil];
    }
    return isSuccess;
}

+ (BOOL)copyFileAtPath:(NSString *)fromPath toFolderPath:(NSString *)toFolderPath toPathName:(NSString *)toPathName overwrite:(BOOL)overwrite error:(NSError *__autoreleasing *)error
{
    //先要保证源文件路径存在，不然抛出异常
    if (![[NSFileManager defaultManager] fileExistsAtPath:fromPath]) {
        [NSException raise:@"非法的源文件路径" format:@"源文件路径%@不存在，请检查源文件路径", fromPath];
        return NO;
    }
    //获得目标文件的上级目录
    if (![[NSFileManager defaultManager] fileExistsAtPath:toFolderPath]) {
        // 创建移动路径
        [[NSFileManager defaultManager] createDirectoryAtPath:toFolderPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    //目标文件的路径
    NSString *toPath = [toFolderPath stringByAppendingPathComponent:toPathName];
    //NSLog(@"toPath == %@", toPath);
    // 判断目标路径文件是否存在
    if ([[NSFileManager defaultManager] fileExistsAtPath:toPath]) {
        //如果覆盖，删除目标路径文件
        if (overwrite) {
            //删掉目标路径文件
            [[NSFileManager defaultManager] removeItemAtPath:toPath error:nil];
        } else {
           //删掉被移动文件
            [[NSFileManager defaultManager] removeItemAtPath:fromPath error:nil];
            return YES;
        }
    }
    
    // 移动文件，当要移动到的文件路径文件存在，会移动失败
    BOOL isSuccess = [[NSFileManager defaultManager] copyItemAtPath:fromPath toPath:toPath error:error];
    return isSuccess;
}

+ (NSString *)copyFolderPath:(NSString *)folderPath fromFileName:(NSString *)fromFileName format:(NSString *)format error:(NSError *__autoreleasing *)error
{
    //先要保证源文件路径存在，不然抛出异常
    NSString *fromFilePath = [folderPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.%@", fromFileName, format]];
    if (![[NSFileManager defaultManager] fileExistsAtPath:fromFilePath]) {
        [NSException raise:@"非法的源文件路径" format:@"源文件路径%@不存在，请检查源文件路径", fromFilePath];
        return @"";
    }
    int toFileNameNum = 1;
    NSString *toFileName = [NSString stringWithFormat:@"%@(%d)", fromFileName, toFileNameNum];
    NSString *toFilePath = [folderPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.%@", toFileName, format]];
    do {
        toFileNameNum = toFileNameNum + 1;
        toFileName = [NSString stringWithFormat:@"%@(%d)", fromFileName, toFileNameNum];
        toFilePath = [folderPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.%@", toFileName, format]];
    } while ([[NSFileManager defaultManager] fileExistsAtPath:toFilePath]);
    
    BOOL isSuccess = [[NSFileManager defaultManager] copyItemAtPath:fromFilePath toPath:toFilePath error:error];
    if (isSuccess == YES) {
        return toFilePath;
    }
    
    return @"";
}

+ (NSString *)copyFileAtPath:(NSString *)fromPath toPath:(NSString *)toPath overwrite:(BOOL)overwrite error:(NSError *__autoreleasing  _Nullable *)error
{
    //先要保证源文件路径存在，不然抛出异常
    if (![[NSFileManager defaultManager] fileExistsAtPath:fromPath]) {
        [NSException raise:@"非法的源文件路径" format:@"源文件路径%@不存在，请检查源文件路径", fromPath];
        return @"";
    }
    /*
    int toFileNameNum = 1;
    do {
        toFileNameNum = toFileNameNum + 1;
        toFileName = [NSString stringWithFormat:@"%@(%d)", fromFileName, toFileNameNum];
        toFilePath = [folderPath stringByAppendingPathComponent:[NSString stringWithFormat:@"%@.%@", toFileName, format]];
    } while ([[NSFileManager defaultManager] fileExistsAtPath:toFilePath]);
    */
    BOOL isSuccess = [[NSFileManager defaultManager] copyItemAtPath:fromPath toPath:toPath error:error];
    if (isSuccess == YES) {
        return toPath;
    }
    
    return @"";
}

+ (BOOL)fileIsExistAtPath:(NSString *)filePath
{
    if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        return YES;
    }
    NSLog(@"文件路径不存在");
    return NO;
}

+ (NSArray *)getDocumentFolderAllFilesForFolderPath:(NSString *)folderPath
{
    NSFileManager *fileManager = [NSFileManager defaultManager];
    //在这里获取应用程序Documents文件夹里的文件及文件夹列表
    //NSArray *fileList = [[NSArray alloc] init];
    //fileList便是包含有该文件夹下所有文件的文件名及文件夹名的数组
    NSError *error = nil;
    if ([[NSFileManager defaultManager] fileExistsAtPath:folderPath]) {
        NSArray *fileList = [fileManager contentsOfDirectoryAtPath:folderPath error:&error];
        return fileList;
    }
    
    return [NSArray array];
}

+ (NSString *)getDocumentsFileName:(NSString *)filePath
{
    NSString *lastName = [filePath lastPathComponent];
    NSString *fileName = [lastName stringByDeletingPathExtension];
    
    return fileName;
}

+ (NSString *)getDocumentsFileExtension:(NSString *)filePath
{
    NSString *lastName = [filePath lastPathComponent];
    NSString *fileExtension = [lastName pathExtension];
    
    return fileExtension;
}

@end
