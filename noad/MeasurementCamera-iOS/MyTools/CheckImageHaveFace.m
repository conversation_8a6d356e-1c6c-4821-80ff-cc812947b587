//
//  CheckImageHaveFace.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import "CheckImageHaveFace.h"

@implementation CheckImageHaveFace

+ (BOOL)checkHaveFaceWithImage:(UIImage *)image
{
    BOOL result = NO;
            
    //CGFloat scale = [UIScreen mainScreen].bounds.size.width / image.size.width;
    //CGFloat topMargin = ([UIScreen mainScreen].bounds.size.height - resultImage.size.height * scale) * 0.5;

    CIImage *ciImage = [[CIImage alloc] initWithImage:image];
    CIDetector *detector = [CIDetector  detectorOfType:CIDetectorTypeFace context:nil options:@{CIDetectorAccuracy: CIDetectorAccuracyHigh,CIDetectorMinFeatureSize:[[NSNumber alloc] initWithFloat:0.2],CIDetectorTracking:[NSNumber numberWithBool:YES]}];
    NSArray<CIFaceFeature *> *results = (NSArray<CIFaceFeature *> *) [detector featuresInImage:ciImage options:@{CIDetectorImageOrientation:[[NSNumber alloc] initWithInt:1]}];
    
    if (results.count > 0) {
        result = YES;
    }
    return result;
}

@end
