//
//  CheckImageHaveFace.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import "CheckImageHaveFace.h"

@implementation CheckImageHaveFace

+ (BOOL)checkHaveFaceWithImage:(UIImage *)image
{
    BOOL result = NO;

    if (!image) {
        NSLog(@"CheckImageHaveFace: 输入图像为空");
        return NO;
    }

    // 创建 CIImage
    CIImage *ciImage = [[CIImage alloc] initWithImage:image];
    if (!ciImage) {
        NSLog(@"CheckImageHaveFace: 无法创建 CIImage");
        return NO;
    }

    // 创建人脸检测器，优化参数
    NSDictionary *detectorOptions = @{
        CIDetectorAccuracy: CIDetectorAccuracyHigh,
        CIDetectorMinFeatureSize: @(0.15),  // 降低最小特征尺寸，提高小脸检测
        CIDetectorTracking: @(YES),
        CIDetectorMaxFeatureCount: @(10)    // 增加最大检测人脸数
    };

    CIDetector *detector = [CIDetector detectorOfType:CIDetectorTypeFace
                                              context:nil
                                              options:detectorOptions];

    if (!detector) {
        NSLog(@"CheckImageHaveFace: 无法创建人脸检测器");
        return NO;
    }

    // 获取图像的正确方向
    NSNumber *orientation = [self getImageOrientationNumber:image];

    // 尝试多种方向进行检测
    NSArray *orientationsToTry = @[
        orientation,                    // 图像原始方向
        @(1),                          // 正常方向
        @(6),                          // 顺时针90度
        @(3),                          // 180度
        @(8)                           // 逆时针90度
    ];

    for (NSNumber *orientationNum in orientationsToTry) {
        NSDictionary *imageOptions = @{CIDetectorImageOrientation: orientationNum};
        NSArray<CIFaceFeature *> *results = (NSArray<CIFaceFeature *> *)[detector featuresInImage:ciImage options:imageOptions];

        NSLog(@"CheckImageHaveFace: 方向 %@ 检测到 %lu 个人脸", orientationNum, (unsigned long)results.count);

        if (results.count > 0) {
            // 验证检测到的人脸是否有效
            for (CIFaceFeature *face in results) {
                CGRect bounds = face.bounds;
                // 检查人脸区域是否合理（不能太小或超出图像边界）
                if (bounds.size.width > 20 && bounds.size.height > 20 &&
                    bounds.origin.x >= 0 && bounds.origin.y >= 0 &&
                    CGRectGetMaxX(bounds) <= ciImage.extent.size.width &&
                    CGRectGetMaxY(bounds) <= ciImage.extent.size.height) {
                    NSLog(@"CheckImageHaveFace: 检测到有效人脸，位置: %@", NSStringFromCGRect(bounds));
                    result = YES;
                    break;
                }
            }
            if (result) break;
        }
    }

    NSLog(@"CheckImageHaveFace: 最终结果 = %@", result ? @"检测到人脸" : @"未检测到人脸");
    return result;
}

// 获取图像方向对应的数字
+ (NSNumber *)getImageOrientationNumber:(UIImage *)image
{
    switch (image.imageOrientation) {
        case UIImageOrientationUp:
            return @(1);
        case UIImageOrientationDown:
            return @(3);
        case UIImageOrientationLeft:
            return @(8);
        case UIImageOrientationRight:
            return @(6);
        case UIImageOrientationUpMirrored:
            return @(2);
        case UIImageOrientationDownMirrored:
            return @(4);
        case UIImageOrientationLeftMirrored:
            return @(5);
        case UIImageOrientationRightMirrored:
            return @(7);
        default:
            return @(1);
    }
}

@end
