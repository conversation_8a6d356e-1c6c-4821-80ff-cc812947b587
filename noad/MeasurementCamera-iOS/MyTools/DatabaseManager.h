//
//  DatabaseManager.h
//  TimeMachine
//
//  Created by FS003 on 2021/7/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface DatabaseManager : NSObject

//创建手相资料的表
- (BOOL)createPalmsTable;

- (BOOL)insertPalmsTableWithLabel1:(NSString *)label1 label2:(NSString *)label2 label3:(NSString *)label3 label4:(NSString *)label4 personality_content:(NSString *)personality_content feeling_content:(NSString *)feeling_content work_content:(NSString *)work_content;

- (NSArray *)obtainPalmsTable;


//创建心理测试列表资料的表
- (BOOL)createPsychologicalListTable;

- (BOOL)insertPsychologicalListTableWithTitle:(NSString *)title desc:(NSString *)desc result:(NSString *)result;

- (NSArray *)obtainPsychologicalListTable;

//创建心理测试题目内容的表
- (BOOL)createPsychologicalSubjectTable;

- (BOOL)insertPsychologicalSubjectTableWithPsychological_id:(NSString *)psychological_id subject_index:(NSString *)subject_index subject_title:(NSString *)subject_title subject_answer1:(NSString *)subject_answer1 answer1_toindex:(NSString *)answer1_toindex subject_answer2:(NSString *)subject_answer2 answer2_toindex:(NSString *)answer2_toindex subject_answer3:(NSString *)subject_answer3 answer3_toindex:(NSString *)answer3_toindex subject_answer4:(NSString *)subject_answer4 answer4_toindex:(NSString *)answer4_toindex result_type:(NSString *)result_type;

- (NSArray *)obtainPsychologicalSubjectTableWithPsychological_id:(NSString *)psychological_id;

// 创建魔法变脸模板图片的表
- (BOOL)createMagicTemplateListTable;

- (BOOL)insertMagicTemplateTableWithTemplate_name:(NSString *)template_name;

- (NSArray *)obtainMagicTemplateListTable;

@end

NS_ASSUME_NONNULL_END
