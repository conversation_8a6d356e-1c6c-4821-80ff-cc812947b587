//
//  MyAdManager.h
//  FootprintMap
//
//  Created by 叶庭文 on 2022/4/9.
//

#import <Foundation/Foundation.h>
#import "BaseViewController.h"
#define startWithADTimes @"startWithADTimes" 
NS_ASSUME_NONNULL_BEGIN

@protocol MyAdManagerDelegate <NSObject>

//
- (void)myAdManagerFullscreenVideoAdCloseRefresh;

- (void)myAdManagerFullscreenVideoAdDidDownLoadVideo;

- (void)myAdManagerLoadFullscreenVideoAdFailed;


//
- (void)myAdManagerBannerViewCloseRefresh;

- (void)myAdManagerBannerAdViewRenderSuccess;

- (void)myAdManagerLoadBannerViewFailed;

@end

@interface MyAdManager : NSObject

@property (nonatomic, weak) id <MyAdManagerDelegate> delegate;

// 显示新插屏广告
- (void)showFullscreenVideoAdWithViewController:(BaseViewController *)viewController;

// 显示banner广告
- (void)showBannerAdWithViewController:(BaseViewController *)viewController frame:(CGRect)frame size:(CGSize)size;


@end

NS_ASSUME_NONNULL_END
