//
//  ImageManager.m
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import "ImageManager.h"

@implementation ImageManager

+ (UIImage *)compressImage:(UIImage *)image maxSize:(CGFloat)maxSize
{
    UIImage *resultImage = nil;
    CGSize imageSize = image.size;
    CGFloat OriginalWidth = imageSize.width;
    CGFloat OriginalHeight = imageSize.height;
    if (OriginalWidth >= maxSize  &&  OriginalHeight >= maxSize) {
        if (OriginalWidth > OriginalHeight) {
            CGFloat resultWidth = maxSize-100;
            CGFloat resultHeight = OriginalHeight / OriginalWidth * resultWidth;
            UIGraphicsBeginImageContext(CGSizeMake(resultWidth, resultHeight));
            [image drawInRect:CGRectMake(0, 0, resultWidth,  resultHeight)];
            resultImage = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
        } else {
            CGFloat resultHeight = maxSize-120;
            CGFloat resultWidth = OriginalWidth / OriginalHeight * resultHeight;
            UIGraphicsBeginImageContext(CGSizeMake(resultWidth, resultHeight));
            [image drawInRect:CGRectMake(0, 0, resultWidth,  resultHeight)];
            resultImage = UIGraphicsGetImageFromCurrentImageContext();
            UIGraphicsEndImageContext();
        }
    } else if (OriginalWidth >= maxSize  &&  OriginalHeight < maxSize) {
        CGFloat resultWidth = maxSize-120;
        CGFloat resultHeight = OriginalHeight / OriginalWidth * resultWidth;
        UIGraphicsBeginImageContext(CGSizeMake(resultWidth, resultHeight));
        [image drawInRect:CGRectMake(0, 0, resultWidth,  resultHeight)];
        resultImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    } else if (OriginalWidth < maxSize  &&  OriginalHeight >= maxSize) {
        CGFloat resultHeight = maxSize-120;
        CGFloat resultWidth = OriginalWidth / OriginalHeight * resultHeight;
        UIGraphicsBeginImageContext(CGSizeMake(resultWidth, resultHeight));
        [image drawInRect:CGRectMake(0, 0, resultWidth,  resultHeight)];
        resultImage = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
    } else if (OriginalWidth <= 800  &&  OriginalHeight <= 800) {
        NSData *imageData = UIImageJPEGRepresentation(image, 0.8);
        resultImage = [UIImage imageWithData:imageData];
        return resultImage;
    } else {
        resultImage = image;
    }
    NSData *imageData = UIImageJPEGRepresentation(resultImage, 0.5);
    //NSLog(@"imageData.length == %ld", imageData.length);
    resultImage = [UIImage imageWithData:imageData];
    
    return resultImage;
}

//截取图片
+ (UIImage *)cutImageFromImageView:(UIImageView *)imgView
{
    CGFloat imageWidth = imgView.bounds.size.width * 0.8;
    //NSLog(@"originX == %f,  viewWidth == %f", originX, viewWidth);
    //UIGraphicsBeginImageContextWithOptions(区域大小, 是否是非透明的, 屏幕密度);
    //屏幕密度
    CGFloat screenScale = [UIScreen mainScreen].scale;
    UIGraphicsBeginImageContextWithOptions(imgView.bounds.size, YES, screenScale);
    [imgView.layer renderInContext:UIGraphicsGetCurrentContext()];
    //[waveimage drawAtPoint:CGPointMake(originX, 0)];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    CGRect rect = CGRectMake(imgView.bounds.size.width * 0.1 * screenScale, (imgView.bounds.size.height-imageWidth) / 2 * screenScale, imageWidth * screenScale, imageWidth * screenScale);
    UIImage *resultImage = [UIImage imageWithCGImage:CGImageCreateWithImageInRect(image.CGImage, rect)];
    
    return resultImage;
}

+ (UIImage *)cutImageFromView:(UIView *)view
{
    CGFloat imageWidth = view.bounds.size.width;
    CGFloat imageHeight = view.bounds.size.height;
    //NSLog(@"originX == %f,  viewWidth == %f", originX, viewWidth);
    //UIGraphicsBeginImageContextWithOptions(区域大小, 是否是非透明的, 屏幕密度);
    //屏幕密度
    CGFloat screenScale = [UIScreen mainScreen].scale;
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, NO, screenScale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    //[waveimage drawAtPoint:CGPointMake(originX, 0)];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    CGRect rect = CGRectMake(0, 0, imageWidth * screenScale, imageHeight * screenScale);
    UIImage *resultImage = [UIImage imageWithCGImage:CGImageCreateWithImageInRect(image.CGImage, rect)];
    
    return resultImage;
}

+ (UIImage *)cutImageFromView:(UIView *)view inFrame:(CGRect)frame
{
    CGFloat screenScale = [UIScreen mainScreen].scale;
    UIGraphicsBeginImageContextWithOptions(view.bounds.size, YES, screenScale);
    [view.layer renderInContext:UIGraphicsGetCurrentContext()];
    //[waveimage drawAtPoint:CGPointMake(originX, 0)];
    UIImage *image = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    CGRect rect = CGRectMake(frame.origin.x * screenScale, frame.origin.y * screenScale, frame.size.width * screenScale, frame.size.height * screenScale);
    UIImage *resultImage = [UIImage imageWithCGImage:CGImageCreateWithImageInRect(image.CGImage, rect)];
    
    return resultImage;
}

//人脸位置检测，并裁剪包含五官的人脸
+ (UIImage *)detectFace:(UIImage *)fullImage
{
    UIImage *resultImage;
    if (fullImage) {
        CIImage *cgImage = [[CIImage alloc] initWithImage:fullImage];
        CIContext *context = [CIContext contextWithOptions:nil];
        CIDetector *faceDetector = [CIDetector detectorOfType:CIDetectorTypeFace context:context options:@{CIDetectorAccuracy:CIDetectorAccuracyHigh}];
        //检测到的人脸数组
        NSArray *faceArray = [faceDetector featuresInImage:cgImage];
        //NSLog(@"faceArray == %@", faceArray);
        if (faceArray.count > 0) {
            //检测到人脸时获取最后一次监测到的人脸
            CIFeature *faceFeature = [faceArray lastObject];
            CGRect faceBounds = faceFeature.bounds;
            //NSLog(@"faceBounds == %@", NSStringFromCGRect(faceBounds));
            //cgImage计算的尺寸是像素，需要与空间的尺寸做个计算
            //下面几句是为了获取到额头部位做的处理，如果只需要定位到五官可直接取faceBounds的值
            /*
            CGFloat scale = cgImage.extent.size.width/ScreenWidth;
            CGFloat offsetY = fabs(self.circleView.circleRect.origin.y*scale - faceBounds.origin.y);
            faceBounds.origin.y -= offsetY;
            faceBounds.size.height += offsetY*2;
             */
            //这种裁剪方法在低头时和抬头时会截取不到完整的脸部，但是可以定位全脸位置更精确
            //faceBounds.origin.y -= faceBounds.size.height * 0.1;
            //faceBounds.size.height += faceBounds.size.height * 0.2;
            //CGImageRef newImage = CGImageCreateWithImageInRect(self.photoImage.CGImage, faceBounds);
            //resultImage = [[UIImage alloc] initWithCGImage:newImage];
            //CGImageRelease(newImage);
            
            //这种裁剪方法不会出现脸部裁剪不到的情况，但是会裁剪到脖子的位置
            CIImage *faceImage = [cgImage imageByCroppingToRect:faceBounds];
            resultImage = [UIImage imageWithCGImage:[context createCGImage:faceImage fromRect:faceImage.extent]];
        }
    }
    return resultImage;
}



@end
