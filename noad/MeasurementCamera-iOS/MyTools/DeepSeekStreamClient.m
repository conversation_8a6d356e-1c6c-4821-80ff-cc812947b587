//
//  DeepSeekStreamClient.m
//  TimeMachine
//
//  Created by fs0011 on 2025/2/24.
//

#import "DeepSeekStreamClient.h"
#import <AFNetworking/AFNetworking.h>
#import <objc/runtime.h>
static NSString *const kDeepSeekBaseURL = @"https://ark.cn-beijing.volces.com/api/v3/chat/completions";

@interface DeepSeekStreamClient()
@property (nonatomic, strong) AFHTTPSessionManager *sessionManager;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSMutableData *> *responseBuffers;
@property (nonatomic, strong) NSMutableDictionary<NSString *, DeepSeekStreamCallback> *callbacks;
@end

@implementation DeepSeekStreamClient

// MARK: - 初始化
- (instancetype)initWithAPIKey:(NSString *)apiKey {
    self = [super init];
    if (self) {
        [self setupSessionManagerWithAPIKey:apiKey];
        _responseBuffers = [NSMutableDictionary dictionary];
        _callbacks = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)setupSessionManagerWithAPIKey:(NSString *)apiKey {
    _sessionManager = [AFHTTPSessionManager manager];
    _sessionManager.requestSerializer = [AFJSONRequestSerializer serializer];
    _sessionManager.responseSerializer = [AFHTTPResponseSerializer serializer];
    // 配置请求头
    _sessionManager.responseSerializer.acceptableContentTypes =
    [NSSet setWithObjects:@"text/event-stream", @"application/json", nil];
    
    [_sessionManager.requestSerializer setValue:[NSString stringWithFormat:@"Bearer %@", apiKey]
                             forHTTPHeaderField:@"Authorization"];
    [_sessionManager.requestSerializer setValue:@"application/json"
                             forHTTPHeaderField:@"Content-Type"];
    
    
    // 设置流式数据处理回调
    __weak typeof(self) weakSelf = self;
    [_sessionManager setDataTaskDidReceiveDataBlock:^(NSURLSession * _Nonnull session,
                                                      NSURLSessionDataTask * _Nonnull dataTask,
                                                      NSData * _Nonnull data) {
        [weakSelf handleReceivedData:data forTask:dataTask];
    }];
}

// MARK: - 请求管理
- (NSString *)sendStreamRequestWithMessages:(NSArray<NSDictionary *> *)messages
                                   callback:(DeepSeekStreamCallback)callback {
    // 生成唯一请求ID
    NSString *requestID = [[NSUUID UUID] UUIDString];
    
    // 存储回调
    self.callbacks[requestID] = callback;
    
    // 创建请求参数
    NSDictionary *params = @{
        @"model": @"ep-20250221092553-n2cgj",
        @"messages": messages ?: @[],
        @"stream": [NSNumber numberWithBool:YES]
    };
    
    // 创建请求任务
    __weak typeof(self) weakSelf = self;
    NSURLSessionDataTask *task = [self.sessionManager POST:kDeepSeekBaseURL
                                                parameters:params
                                                   headers:nil
                                                  progress:nil
                                                   success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        // 流式请求不会触发success回调
        [weakSelf handleReceivedData:responseObject forTask:task];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [weakSelf handleRequestFailure:task error:error];
    }];
    
    // 绑定请求ID到任务
    [self setRequestIdentifier:requestID forTask:task];
    
    // 初始化缓冲区
    self.responseBuffers[requestID] = [NSMutableData data];
    
    [task resume];
    return requestID;
}

- (NSString *)sendImageStreamRequestWithMessages:(NSString *)message andImageUrl:(NSString*)url
                                        callback:(DeepSeekStreamCallback)callback {
    // 生成唯一请求ID
    NSString *requestID = [[NSUUID UUID] UUIDString];
    
    // 存储回调
    self.callbacks[requestID] = callback;
    
    // 创建请求参数
    NSDictionary *params = @{
        @"model": @"ep-20250306092024-8wfd9",
        @"messages": @[
            @{
                @"role":@"user",
                @"content": @[
                    @{
                    @"type":@"text",
                    @"text":message
                },
                    @{
                    @"type":@"image_url",
                    @"image_url":
                        @{
                            @"url":url
                        }
                }
                ]
            }
        ],
        @"stream": [NSNumber numberWithBool:YES]
    };
    
    // 创建请求任务
    __weak typeof(self) weakSelf = self;
    NSURLSessionDataTask *task = [self.sessionManager POST:kDeepSeekBaseURL
                                                parameters:params
                                                   headers:nil
                                                  progress:nil
                                                   success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        // 流式请求不会触发success回调
        [weakSelf handleReceivedData:responseObject forTask:task];
    } failure:^(NSURLSessionDataTask * _Nullable task, NSError * _Nonnull error) {
        [weakSelf handleRequestFailure:task error:error];
    }];
    
    // 绑定请求ID到任务
    [self setRequestIdentifier:requestID forTask:task];
    
    // 初始化缓冲区
    self.responseBuffers[requestID] = [NSMutableData data];
    
    [task resume];
    return requestID;
}




- (void)cancelRequestWithID:(NSString *)requestID {
    // 查找对应任务
    for (NSURLSessionTask *task in self.sessionManager.tasks) {
        if ([self requestIdentifierForTask:task] == requestID) {
            [task cancel];
            [self cleanupForRequestID:requestID];
            return;
        }
    }
}

// MARK: - 数据处理
- (void)handleReceivedData:(NSData *)data forTask:(NSURLSessionDataTask *)task {
    NSString *requestID = [self requestIdentifierForTask:task];
    if (!requestID) return;
    
    NSMutableData *buffer = self.responseBuffers[requestID];
    if (!buffer) return;
    
    [buffer appendData:data];
    
    // 转换为字符串并分割完整事件
    NSString *bufferString = [[NSString alloc] initWithData:buffer encoding:NSUTF8StringEncoding];
    NSArray *events = [bufferString componentsSeparatedByString:@"\n\n"];
    
    // 处理所有完整事件（最后一个可能是部分数据）
    for (NSUInteger i = 0; i < events.count - 1; i++) {
        NSString *event = events[i];
        [self parseEvent:event forRequestID:requestID];
    }
    
    // 保留未完成的事件部分
    NSString *remaining = events.count > 0 ? [events lastObject] : @"";
    [buffer setData:[remaining dataUsingEncoding:NSUTF8StringEncoding]];
}

- (void)parseEvent:(NSString *)event forRequestID:(NSString *)requestID {
    if ([event hasPrefix:@"data: "]) {
        NSString *jsonString = [event substringFromIndex:6]; // 去掉 "data: "
        if ([jsonString isEqualToString:@"[DONE]"]) {
            [self handleStreamCompletionForRequestID:requestID];
            return;
        }
        
        NSError *jsonError;
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&jsonError];
        
        if (jsonError) {
            [self handleParseErrorForRequestID:requestID error:jsonError];
            return;
        }
        
        NSString *content = json[@"choices"][0][@"delta"][@"content"];
        if (content) {
            [self deliverContent:content forRequestID:requestID];
        }
    }
}
// MARK: - 回调处理
- (void)deliverContent:(NSString *)content forRequestID:(NSString *)requestID {
    DeepSeekStreamCallback callback = self.callbacks[requestID];
    if (!callback) return;
    
    dispatch_async(dispatch_get_main_queue(), ^{
        callback(requestID, content, nil);
    });
}

- (void)handleStreamCompletionForRequestID:(NSString *)requestID {
    DeepSeekStreamCallback callback = self.callbacks[requestID];
    if (callback) {
        dispatch_async(dispatch_get_main_queue(), ^{
            callback(requestID, nil, nil); // 用nil表示结束
        });
    }
    [self cleanupForRequestID:requestID];
}

- (void)handleParseErrorForRequestID:(NSString *)requestID error:(NSError *)error {
    DeepSeekStreamCallback callback = self.callbacks[requestID];
    if (callback) {
        dispatch_async(dispatch_get_main_queue(), ^{
            callback(requestID, nil, error);
        });
    }
    [self cleanupForRequestID:requestID];
}

- (void)handleRequestFailure:(NSURLSessionTask *)task error:(NSError *)error {
    NSString *requestID = [self requestIdentifierForTask:task];
    if (!requestID) return;
    
    DeepSeekStreamCallback callback = self.callbacks[requestID];
    if (callback) {
        dispatch_async(dispatch_get_main_queue(), ^{
            callback(requestID, nil, error);
        });
    }
    [self cleanupForRequestID:requestID];
}

// MARK: - 工具方法
- (void)cleanupForRequestID:(NSString *)requestID {
    [self.responseBuffers removeObjectForKey:requestID];
    [self.callbacks removeObjectForKey:requestID];
}

static char kRequestIdentifierKey;
- (void)setRequestIdentifier:(NSString *)identifier forTask:(NSURLSessionTask *)task {
    objc_setAssociatedObject(task, &kRequestIdentifierKey, identifier, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (NSString *)requestIdentifierForTask:(NSURLSessionTask *)task {
    return objc_getAssociatedObject(task, &kRequestIdentifierKey);
}


- (void)test
{
    // 初始化客户端
    DeepSeekStreamClient *client = [[DeepSeekStreamClient alloc] initWithAPIKey:@"your-api-key"];
    
    // 发起请求1
    NSString *requestID1 = [client sendStreamRequestWithMessages:@[
        @{@"role": @"user", @"content": @"第一个问题"}
    ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
        if (error) {
            NSLog(@"请求%@出错: %@", requestID, error);
            return;
        }
        
        if (chunkContent) {
            NSLog(@"请求%@收到片段: %@", requestID, chunkContent);
        } else {
            NSLog(@"请求%@完成", requestID);
        }
    }];
    
    // 发起请求2
    NSString *requestID2 = [client sendStreamRequestWithMessages:@[
        @{@"role": @"user", @"content": @"第二个问题"}
    ] callback:^(NSString *requestID, NSString *chunkContent, NSError *error) {
        // 处理第二个请求的响应...
    }];
    
    // 取消请求
    [client cancelRequestWithID:requestID1];
    
}

@end

