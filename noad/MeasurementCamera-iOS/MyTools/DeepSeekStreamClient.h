//
//  DeepSeekStreamClient.h
//  TimeMachine
//
//  Created by fs0011 on 2025/2/24.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^DeepSeekStreamCallback)(NSString *requestID, NSString *chunkContent, NSError *error);

@interface DeepSeekStreamClient : NSObject

/// 初始化方法
/// @param apiKey DeepSeek API Key
- (instancetype)initWithAPIKey:(NSString *)apiKey;

/// 发起流式请求
/// @param messages 消息数组 (格式: @[@{@"role": @"user", @"content": ...}])
/// @param callback 数据块回调 (自动返回 requestID)
/// @return 请求ID (可用于取消请求)
- (NSString *)sendStreamRequestWithMessages:(NSArray<NSDictionary *> *)messages
                                  callback:(DeepSeekStreamCallback)callback;
- (NSString *)sendImageStreamRequestWithMessages:(NSString *)message andImageUrl:(NSString*)url
                                        callback:(DeepSeekStreamCallback)callback;
/// 取消指定请求
/// @param requestID 请求ID
- (void)cancelRequestWithID:(NSString *)requestID;

@end


NS_ASSUME_NONNULL_END
