//
//  ImageManager.h
//  TimeMachine
//
//  Created by FS003 on 2021/7/21.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface ImageManager : NSObject

// 压缩图像
+ (UIImage *)compressImage:(UIImage *)image maxSize:(CGFloat)maxSize;

+ (UIImage *)cutImageFromImageView:(UIImageView *)imgView;

// 裁切图像
+ (UIImage *)cutImageFromView:(UIView *)view;

+ (UIImage *)cutImageFromView:(UIView *)view inFrame:(CGRect)frame;

// 人脸位置检测，并裁剪包含五官的人脸
+ (UIImage *)detectFace:(UIImage *)fullImage;


@end

NS_ASSUME_NONNULL_END
