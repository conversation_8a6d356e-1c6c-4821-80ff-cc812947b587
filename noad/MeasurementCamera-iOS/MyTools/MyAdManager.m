//
//  MyAdManager.m
//  FootprintMap
//
//  Created by 叶庭文 on 2022/4/9.
//

#import "MyAdManager.h"
#import <BUAdSDK/BUAdSDK.h>

@interface MyAdManager () <BUNativeExpressFullscreenVideoAdDelegate, BUNativeExpressBannerViewDelegate>

@property (nonatomic, strong) BaseViewController *viewController;

@property (nonatomic, strong) BUNativeExpressFullscreenVideoAd *fullscreenVideoAd;

@property (nonatomic, strong) BUNativeExpressBannerView *bannerView;

@end

@implementation MyAdManager

- (id)init
{
    self = [super init];
    if (self) {
    }
    return self;
}

// FullscreenVideoAd
- (void)showFullscreenVideoAdWithViewController:(BaseViewController *)viewController
{
    self.viewController = viewController;
    
    if (self.fullscreenVideoAd == nil) {
        self.fullscreenVideoAd = [[BUNativeExpressFullscreenVideoAd alloc] initWithSlotID:@"952763996"];
        self.fullscreenVideoAd.delegate = self;
        [self.fullscreenVideoAd loadAdData];
    }
}

- (void)nativeExpressFullscreenVideoAdDidLoad:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    NSLog(@"nativeExpressFullscreenVideoAdDidLoad");
}

- (void)nativeExpressFullscreenVideoAdViewRenderSuccess:(BUNativeExpressFullscreenVideoAd *)rewardedVideoAd
{
    NSLog(@"nativeExpressFullscreenVideoAdViewRenderSuccess");
}

- (void)nativeExpressFullscreenVideoAd:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd didFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressFullscreenVideoAd");
    if (error != nil) {
        if (self.fullscreenVideoAd) {
            self.fullscreenVideoAd = nil;
            if ([self.delegate respondsToSelector:@selector(myAdManagerLoadFullscreenVideoAdFailed)]) {
                [self.delegate myAdManagerLoadFullscreenVideoAdFailed];
            }
        }
    }
}

- (void)nativeExpressFullscreenVideoAdViewRenderFail:(BUNativeExpressFullscreenVideoAd *)rewardedVideoAd error:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressFullscreenVideoAdViewRenderFail");
    if (error != nil) {
        if (self.fullscreenVideoAd) {
            self.fullscreenVideoAd = nil;
            if ([self.delegate respondsToSelector:@selector(myAdManagerLoadFullscreenVideoAdFailed)]) {
                [self.delegate myAdManagerLoadFullscreenVideoAdFailed];
            }
        }
    }
}

- (void)nativeExpressFullscreenVideoAdDidDownLoadVideo:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    if (self.fullscreenVideoAd) {
       [self.fullscreenVideoAd showAdFromRootViewController:self.viewController];
    }
    
    if ([self.delegate respondsToSelector:@selector(myAdManagerFullscreenVideoAdDidDownLoadVideo)]) {
        [self.delegate myAdManagerFullscreenVideoAdDidDownLoadVideo];
    }
}

- (void)nativeExpressFullscreenVideoAdWillVisible:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidVisible:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClick:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClickSkip:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdWillClose:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    
}

- (void)nativeExpressFullscreenVideoAdDidClose:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd
{
    if (self.fullscreenVideoAd) {
        self.fullscreenVideoAd = nil;
        
        if ([self.delegate respondsToSelector:@selector(myAdManagerFullscreenVideoAdCloseRefresh)]) {
            [self.delegate myAdManagerFullscreenVideoAdCloseRefresh];
        }
    }
}

- (void)nativeExpressFullscreenVideoAdDidPlayFinish:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd didFailWithError:(NSError *_Nullable)error
{
    
}

- (void)nativeExpressFullscreenVideoAdCallback:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd withType:(BUNativeExpressFullScreenAdType) nativeExpressVideoAdType
{
    
}

- (void)nativeExpressFullscreenVideoAdDidCloseOtherController:(BUNativeExpressFullscreenVideoAd *)fullscreenVideoAd interactionType:(BUInteractionType)interactionType
{
    
}

// BannerAd
- (void)showBannerAdWithViewController:(BaseViewController *)viewController frame:(CGRect)frame size:(CGSize)size
{
    self.viewController = viewController;
    
    UIWindow *window = nil;
    if ([[UIApplication sharedApplication].delegate respondsToSelector:@selector(window)]) {
        window = [[UIApplication sharedApplication].delegate window];
    }
    if (![window isKindOfClass:[UIView class]]) {
        window = [UIApplication sharedApplication].keyWindow;
    }
    if (!window) {
        window = [[UIApplication sharedApplication].windows objectAtIndex:0];
    }
    CGFloat bottom = 0.0;
    if (@available(iOS 11.0, *)) {
        bottom = window.safeAreaInsets.bottom;
    } else {
        // Fallback on earlier versions
    }
    if (self.bannerView == nil) {
        self.bannerView = [[BUNativeExpressBannerView alloc] initWithSlotID:@"952763995" rootViewController:viewController adSize:size];
        self.bannerView.backgroundColor = [UIColor clearColor];
        self.bannerView.frame = frame;
        self.bannerView.delegate = self;
        [self.bannerView loadAdData];
    }
}

- (void)nativeExpressBannerAdViewRenderSuccess:(BUNativeExpressBannerView *)bannerAdView
{
    //在此回调方法中进行广告的展示，可保证播放流畅和展示流畅，用户体验更好。
    NSLog(@"nativeExpressBannerAdViewRenderSuccess");
    
    [self.viewController.view addSubview:self.bannerView];
    
    if ([self.delegate respondsToSelector:@selector(myAdManagerBannerAdViewRenderSuccess)]) {
        [self.delegate myAdManagerBannerAdViewRenderSuccess];
    }
}

- (void)nativeExpressAdView:(BUNativeExpressAdView *)nativeExpressAdView dislikeWithReason:(NSArray<BUDislikeWords *> *)filterWords
{
    //【重要】需要在点击叉以后 在这个回调中移除视图，否则，会出现用户点击叉无效的情况
    NSLog(@"nativeExpressAdView");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}

- (void)nativeExpressBannerAdViewDidRemoved:(BUNativeExpressBannerView *)nativeExpressAdView
{
    //【重要】若开发者收到此回调，代表穿山甲会主动关闭掉广告，广告移除后需要开发者对界面进行适配
    NSLog(@"nativeExpressBannerAdViewDidRemoved");
    [UIView animateWithDuration:0.3 animations:^{
        self.bannerView.alpha = 0;
    } completion:^(BOOL finished) {
        [self.bannerView removeFromSuperview];
        self.bannerView = nil;
        if ([self.delegate respondsToSelector:@selector(myAdManagerBannerViewCloseRefresh)]) {
            [self.delegate myAdManagerBannerViewCloseRefresh];
        }
    }];
}

- (void)nativeExpressBannerAdView:(BUNativeExpressBannerView *)bannerAdView didLoadFailWithError:(NSError *_Nullable)error
{
    NSLog(@"nativeExpressBannerAdView");
    
    if (self.bannerView  &&  error != nil) {
        if ([self.delegate respondsToSelector:@selector(myAdManagerLoadBannerViewFailed)]) {
            [self.delegate myAdManagerLoadBannerViewFailed];
        }
    }
}

- (void)nativeExpressBannerAdViewRenderFail:(BUNativeExpressBannerView *)bannerAdView error:(NSError * __nullable)error
{
    NSLog(@"nativeExpressBannerAdViewRenderFail");
    
    if (self.bannerView  &&  error != nil) {
        if ([self.delegate respondsToSelector:@selector(myAdManagerLoadBannerViewFailed)]) {
            [self.delegate myAdManagerLoadBannerViewFailed];
        }
    }
}


@end
